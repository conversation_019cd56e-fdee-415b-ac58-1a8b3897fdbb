#VRML_SIM R2025a utf8
# license: Apache License 2.0
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/robotis/turtlebot/protos/TurtleBot3Burger.proto
# keywords: robot/wheeled
# Burger model of the third version of the TurtleBot robot.
# template language: javascript

PROTO EngagedWheel [
  field SFFloat    wheelRadius     0.005
  field SFFloat    translationX      0
  field SFFloat    translationY      0
]
{
  BallJoint {
    jointParameters BallJointParameters {
      anchor %<= (fields.translationX.value) >% %<= (fields.translationY.value) >% %<= (fields.wheelRadius.value) >%
    }
    endPoint Solid {
      translation %<= (fields.translationX.value) >% %<= (fields.translationY.value) >% %<= (fields.wheelRadius.value) >%
      children [
        Shape {
          appearance PBRAppearance {
          }
          geometry DEF CASTER_SPHERE Sphere {
            radius %<= (fields.wheelRadius.value) >%
            subdivision 2
          }
        }
      ]
      boundingObject USE CASTER_SPHERE
      physics Physics {
      }
    }
  }

}
