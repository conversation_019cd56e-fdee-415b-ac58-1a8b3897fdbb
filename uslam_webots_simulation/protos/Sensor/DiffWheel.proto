#VRML_SIM R2025a utf8
# license: Apache License 2.0
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/robotis/turtlebot/protos/TurtleBot3Burger.proto
# keywords: robot/wheeled
# Burger model of the third version of the TurtleBot robot.
# template language: javascript

PROTO DiffWheel [
  field SFBool     isLeftWheel     TRUE
  field SFFloat    wheelRadius     0.03
  field SFFloat    wheelThickness      0.03
  field SFFloat    wheelToCenterDis      0.2
  field SFFloat    maxLinearVelocity     2.0
]
{
  HingeJoint {
    %< if (fields.isLeftWheel.value) { >%
    jointParameters HingeJointParameters {
      axis 0 1 0
      anchor %<= 0 >% %<= -(fields.wheelToCenterDis.value) >% %<= fields.wheelRadius.value >%
    }
    device [
      RotationalMotor {
        name "right wheel motor"
        consumptionFactor -0.001
        maxVelocity %<= (fields.maxLinearVelocity.value)/(fields.wheelRadius.value) >%
      }
      PositionSensor {
        name "right wheel sensor"
        resolution 0.00628
      }
    ]
    endPoint Solid {
      translation %<= 0 >% %<= -(fields.wheelToCenterDis.value) >% %<= fields.wheelRadius.value >%
      rotation 0 -1 0 1.570796
      children [
        DEF RIGHT_WHEEL Pose {
          rotation 1 0 0 -1.5708003061004252
          children [
            Shape {
              appearance PBRAppearance {
                baseColor 0 0 0
                roughness 1
                metalness 0
              }
              geometry Cylinder {
                radius IS wheelRadius
                height IS wheelThickness
              }
            }
          ]
        }
      ]
      name "right wheel"
      boundingObject Pose {
        rotation 1 0 0 -1.570796
        children [
          DEF WHEEL_BOUNDING_OBJECT Cylinder {
            height IS wheelThickness
            radius IS wheelRadius
          }
        ]
      }
      physics Physics {
        density -1
        mass 0.028499
        centerOfMass [
          0 0 0
        ]
      }
    }
    %< } else { >%

    jointParameters HingeJointParameters {
      axis 0 1 0
      anchor %<= 0 >% %<= (fields.wheelToCenterDis.value) >% %<= fields.wheelRadius.value >%
    }
    device [
      RotationalMotor {
        name "left wheel motor"
        consumptionFactor -0.001
        maxVelocity %<= (fields.maxLinearVelocity.value)/(fields.wheelRadius.value) >%
      }
      PositionSensor {
        name "left wheel sensor"
        resolution 0.00628
      }
    ]
    endPoint Solid {
      translation %<= 0 >% %<= (fields.wheelToCenterDis.value) >% %<= fields.wheelRadius.value >%
      rotation 0.707105 0 0.707109 -3.141588
      children [
        DEF RIGHT_WHEEL Pose {
          rotation 1 0 0 -1.5708003061004252
          children [
            Shape {
              appearance PBRAppearance {
                baseColor 0 0 0
                roughness 1
                metalness 0
              }
              geometry Cylinder {
                radius IS wheelRadius
                height IS wheelThickness
              }
            }
          ]
        }
      ]
      name "left wheel"
      boundingObject Pose {
        rotation 1 0 0 -1.570796
        children [
          DEF WHEEL_BOUNDING_OBJECT Cylinder {
            height IS wheelThickness
            radius IS wheelRadius
          }
        ]
      }
      physics Physics {
        density -1
        mass 0.028499
        centerOfMass [
          0 0 0
        ]
      }
    }
    %< } >%
  }

}
