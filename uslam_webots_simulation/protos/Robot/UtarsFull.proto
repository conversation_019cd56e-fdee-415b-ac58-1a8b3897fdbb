#VRML_SIM R2023b utf8
# license: Apache License 2.0
# license url: http://www.apache.org/licenses/LICENSE-2.0
# This is a proto file for Webots for the utars
# Extracted from: meshes/utars/cruzr_s2_v1.urdf

EXTERNPROTO "DiffChassisBase.proto"
EXTERNPROTO "sensor_protos/DiffWheel.proto"
EXTERNPROTO "sensor_protos/EngagedWheel.proto"
EXTERNPROTO "sensor_protos/IMU.proto"
EXTERNPROTO "sensor_protos/RGBD.proto"
EXTERNPROTO "sensor_protos/Lidar2D.proto"

PROTO UtarsFull [
  field  SFVec3f     translation     0 1 0
  field  SFRotation  rotation        0 0 1 0
  field  SFString    name            "UtarsFull"  # Is `Robot.name`.
  field  SFString    controller      "<generic>"   # Is `Robot.controller`.
  field  MFString    controllerArgs  []       # Is `Robot.controllerArgs`.
  field  SFString    customData      ""       # Is `Robot.customData`.
  field  SFBool      supervisor      TRUE    # Is `Robot.supervisor`.
  field  SFBool      synchronization TRUE     # Is `Robot.synchronization`.
  field  SFBool      selfCollision   FALSE    # Is `Robot.selfCollision`.
  field MFNode     extensionSlot    []
]
{
  DiffChassisBase {
    translation IS translation
    rotation IS rotation
    controller IS controller
    controllerArgs IS controllerArgs
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    wheelToCenterDis 0.2022
    casterWheelPosX 0.30
    casterWheelPosY 0.16
    extensionSlot [
      Solid {
        translation 0.000000 0.000000 0.1
        rotation 0.000000 0.000000 1.000000 0
        children [
          Shape {
            appearance DEF base_link_material PBRAppearance {
              baseColor 0.752941 0.752941 0.752941
              roughness 1.000000
              metalness 0
            }
            geometry DEF base_link Mesh {
              url "meshes/utars/base_link.STL"
            }
          }
        ]
        boundingObject Pose {
          translation 0 0 0.1
          children [
            Box {
              size 0.6 0.45 0.3
            }
          ]
        }
        physics Physics {
          density -1
          mass 96.077629
          centerOfMass [ -0.00 -0.0000 0.00 ]
        }
      }

      Solid {
        translation 0.000000 0.000000 0.281000
        rotation 0.000000 0.000000 1.000000 1.570800
        children [
          Shape {
            appearance DEF lifter_base_link_material PBRAppearance {
              baseColor 0.752940 0.752940 0.752940
              roughness 1.000000
              metalness 0
            }
            geometry DEF lifter_base_link Mesh {
              url "meshes/utars/lifter_base_link.STL"
            }
          }
          HingeJoint {
            jointParameters HingeJointParameters {
              axis -1.000000 0.000004 -0.000004
            }
            device [
              RotationalMotor {
                name "lifter_pitch_1_joint"
                minPosition -1.5708
                maxPosition 1.5708
                maxTorque 10000
              }
              PositionSensor {
                name "lifter_pitch_1_joint_sensor"
              }
            ]
            endPoint Solid {
              rotation 0.577350 -0.577352 -0.577350 2.094399
              children [
                Shape {
                  appearance DEF lifter_pitch_1_link_material PBRAppearance {
                    baseColor 0.752940 0.752940 0.752940
                    roughness 1.000000
                    metalness 0
                  }
                  geometry DEF lifter_pitch_1_link Mesh {
                    url "meshes/utars/lifter_pitch_1_link.STL"
                  }
                }
                HingeJoint {
                  jointParameters HingeJointParameters {
                    axis 0.000000 0.000000 1.000000
                    anchor 0.000000 0.276000 0.000000
                  }
                  device [
                    RotationalMotor {
                      name "lifter_pitch_2_joint"
                      minPosition -2.6179939
                      maxPosition 2.6179939
                      maxTorque 10000
                    }
                    PositionSensor {
                      name "lifter_pitch_2_joint_sensor"
                    }
                  ]
                  endPoint Solid {
                    translation 0.000000 0.276000 0.000000
                    children [
                      Shape {
                        appearance DEF lifter_pitch_2_link_material PBRAppearance {
                          baseColor 0.752940 0.752940 0.752940
                          roughness 1.000000
                          metalness 0
                        }
                        geometry DEF lifter_pitch_2_link Mesh {
                          url "meshes/utars/lifter_pitch_2_link.STL"
                        }
                      }
                      HingeJoint {
                        jointParameters HingeJointParameters {
                          axis 0.000000 0.000000 1.000000
                          anchor 0.000000 0.255000 0.000000
                        }
                        device [
                          RotationalMotor {
                            name "lifter_pitch_3_joint"
                            minPosition -1.5708
                            maxPosition 1.5708
                            maxTorque 10000
                          }
                          PositionSensor {
                            name "lifter_pitch_3_joint_sensor"
                          }
                        ]
                        endPoint Solid {
                          translation 0.000000 0.255000 0.000000
                          children [
                            Shape {
                              appearance DEF lifter_pitch_3_link_material PBRAppearance {
                                baseColor 0.752940 0.752940 0.752940
                                roughness 1.000000
                                metalness 0
                              }
                              geometry DEF lifter_pitch_3_link Mesh {
                                url "meshes/utars/lifter_pitch_3_link.STL"
                              }
                            }
                            HingeJoint {
                              jointParameters HingeJointParameters {
                                axis 0.000000 -1.000000 -0.000004
                                anchor 0.000000 0.085500 0.000000
                              }
                              device [
                                RotationalMotor {
                                  name "waist_yaw_joint"
                                  minPosition -2.9670597
                                  maxPosition 2.9670597
                                  maxTorque 10000
                                }
                                PositionSensor {
                                  name "waist_yaw_joint_sensor"
                                }
                              ]
                              endPoint Solid {
                                translation 0.000000 0.085500 0.000000
                                rotation 1.000000 0.000000 0.000000 1.570800
                                children [
                                  Shape {
                                    appearance DEF waist_yaw_link_material PBRAppearance {
                                      baseColor 0.752940 0.752940 0.752940
                                      roughness 1.000000
                                      metalness 0
                                    }
                                    geometry DEF waist_yaw_link Mesh {
                                      url "meshes/utars/waist_yaw_link.STL"
                                    }
                                  }
                                  Solid {
                                    translation 0.000000 -0.004600 -0.373136
                                    rotation 1.000000 0.000000 0.000000 3.141593
                                    children [
                                      Shape {
                                        appearance DEF torso_link_material PBRAppearance {
                                          baseColor 0.752940 0.752940 0.752940
                                          roughness 1.000000
                                          metalness 0
                                        }
                                        geometry DEF torso_link Mesh {
                                          url "meshes/utars/torso_link.STL"
                                        }
                                      }
                                      HingeJoint {
                                        jointParameters HingeJointParameters {
                                          axis 0.000000 0.000000 1.000000
                                          anchor 0.004600 0.000000 0.135460
                                        }
                                        device [
                                          RotationalMotor {
                                            name "head_yaw_joint"
                                            maxVelocity 3.1415926
                                            minPosition -1.658
                                            maxPosition 1.658
                                            maxTorque 4.5
                                          }
                                          PositionSensor {
                                            name "head_yaw_joint_sensor"
                                          }
                                        ]
                                        endPoint Solid {
                                          translation 0.004600 0.000000 0.135460
                                          children [
                                            Shape {
                                              appearance DEF head_yaw_link_material PBRAppearance {
                                                baseColor 0.752941 0.752941 0.752941
                                                roughness 1.000000
                                                metalness 0
                                              }
                                              geometry DEF head_yaw_link Mesh {
                                                url "meshes/utars/head_yaw_link.STL"
                                              }
                                            }
                                            HingeJoint {
                                              jointParameters HingeJointParameters {
                                                axis 0.000000 -1.000000 -0.000004
                                                anchor 0.000000 0.000000 0.025000
                                              }
                                              device [
                                                RotationalMotor {
                                                  name "head_pitch_joint"
                                                  maxVelocity 3.1415926
                                                  minPosition -0.7854
                                                  maxPosition 0.5236
                                                  maxTorque 4.5
                                                }
                                                PositionSensor {
                                                  name "head_pitch_joint_sensor"
                                                }
                                              ]
                                              endPoint Solid {
                                                translation 0.000000 0.000000 0.025000
                                                rotation 1.000000 0.000000 0.000000 1.570800
                                                children [
                                                  Shape {
                                                    appearance DEF head_pitch_link_material PBRAppearance {
                                                      baseColor 0.752941 0.752941 0.752941
                                                      roughness 1.000000
                                                      metalness 0
                                                    }
                                                    geometry DEF head_pitch_link Mesh {
                                                      url "meshes/utars/head_pitch_link.STL"
                                                    }
                                                  }
                                                ]
                                                name "head_pitch_link"
                                                boundingObject NULL
                                                physics Physics {
                                                  density -1
                                                  mass 0.000000
                                                  centerOfMass [ 0.040566 0.102500 -0.000260 ]
                                                  inertiaMatrix [
                                                    4.735400e-03 4.508100e-03 5.185700e-03
                                                    3.343700e-04 -1.260000e-05 -2.680000e-05
                                                  ]
                                                }
                                              }
                                            }
                                          ]
                                          name "head_yaw_link"
                                          boundingObject Pose{
                                            translation 0.02 0 0.1
                                            children [
                                              Box{
                                                size 0.2 0.2 0.3
                                              }
                                            ]
                                          }
                                          physics Physics {
                                            density -1
                                            mass 0.000000
                                            centerOfMass [ 0.000233 -0.000040 0.023081 ]
                                            inertiaMatrix [
                                              8.120000e-05 6.290000e-05 7.630000e-05
                                              1.010000e-06 -3.200000e-07 3.780000e-06
                                            ]
                                          }
                                        }
                                      }
                                      HingeJoint {
                                        jointParameters HingeJointParameters {
                                          axis -0.000011 -0.984814 -0.173612
                                          anchor 0.000000 0.153870 0.000000
                                        }
                                        device [
                                          RotationalMotor {
                                            name "L_shoulder_pitch_joint"
                                            maxVelocity 3.1415926
                                            minPosition -2.8623
                                            maxPosition 2.8623
                                            maxTorque 80.0
                                          }
                                          PositionSensor {
                                            name "L_shoulder_pitch_joint_sensor"
                                          }
                                        ]
                                        endPoint Solid {
                                          translation 0.000000 0.153870 0.000000
                                          rotation -0.608113 0.608118 -0.510285 4.085287
                                          children [
                                            Shape {
                                              appearance DEF L_shoulder_pitch_link_material PBRAppearance {
                                                baseColor 0.752941 0.752941 0.752941
                                                roughness 1.000000
                                                metalness 0
                                              }
                                              geometry DEF L_shoulder_pitch_link Mesh {
                                                url "meshes/utars/L_shoulder_pitch_link.STL"
                                              }
                                            }
                                            HingeJoint {
                                              jointParameters HingeJointParameters {
                                                axis 0.000000 1.000000 -0.000004
                                                anchor 0.000000 0.000000 -0.060000
                                              }
                                              device [
                                                RotationalMotor {
                                                  name "L_shoulder_roll_joint"
                                                  maxVelocity 3.1415926
                                                  minPosition -1.885
                                                  maxPosition 0.1222
                                                  maxTorque 80.0
                                                }
                                                PositionSensor {
                                                  name "L_shoulder_roll_joint_sensor"
                                                }
                                              ]
                                              endPoint Solid {
                                                translation 0.000000 0.000000 -0.060000
                                                rotation -1.000000 0.000000 0.000000 1.570800
                                                children [
                                                  Shape {
                                                    appearance DEF L_shoulder_roll_link_material PBRAppearance {
                                                      baseColor 0.752940 0.752940 0.752940
                                                      roughness 1.000000
                                                      metalness 0
                                                    }
                                                    geometry DEF L_shoulder_roll_link Mesh {
                                                      url "meshes/utars/L_shoulder_roll_link.STL"
                                                    }
                                                  }
                                                  HingeJoint {
                                                    jointParameters HingeJointParameters {
                                                      axis -1.000000 -0.000004 -0.000004
                                                      anchor -0.083000 0.000000 0.000000
                                                    }
                                                    device [
                                                      RotationalMotor {
                                                        name "L_shoulder_yaw_joint"
                                                        maxVelocity 3.1416
                                                        minPosition -2.9322
                                                        maxPosition 2.9322
                                                        maxTorque 45.0
                                                      }
                                                      PositionSensor {
                                                        name "L_shoulder_yaw_joint_sensor"
                                                      }
                                                    ]
                                                    endPoint Solid {
                                                      translation -0.083000 0.000000 0.000000
                                                      rotation -0.577350 -0.577352 0.577350 2.094399
                                                      children [
                                                        Shape {
                                                          appearance DEF L_shoulder_yaw_link_material PBRAppearance {
                                                            baseColor 0.752941 0.752941 0.752941
                                                            roughness 1.000000
                                                            metalness 0
                                                          }
                                                          geometry DEF L_shoulder_yaw_link Mesh {
                                                            url "meshes/utars/L_shoulder_yaw_link.STL"
                                                          }
                                                        }
                                                        HingeJoint {
                                                          jointParameters HingeJointParameters {
                                                            axis 0.000000 1.000000 -0.000004
                                                            anchor -0.019993 0.000000 0.213000
                                                          }
                                                          device [
                                                            RotationalMotor {
                                                              name "L_elbow_roll_joint"
                                                              maxVelocity 3.1416
                                                              minPosition -2.6529
                                                              maxPosition 0.0349
                                                              maxTorque 45.0
                                                            }
                                                            PositionSensor {
                                                              name "L_elbow_roll_joint_sensor"
                                                            }
                                                          ]
                                                          endPoint Solid {
                                                            translation -0.019993 0.000000 0.213000
                                                            rotation -1.000000 0.000000 0.000000 1.570800
                                                            children [
                                                              Shape {
                                                                appearance DEF L_elbow_roll_link_material PBRAppearance {
                                                                  baseColor 0.752941 0.752941 0.752941
                                                                  roughness 1.000000
                                                                  metalness 0
                                                                }
                                                                geometry DEF L_elbow_roll_link Mesh {
                                                                  url "meshes/utars/L_elbow_roll_link.STL"
                                                                }
                                                              }
                                                              HingeJoint {
                                                                jointParameters HingeJointParameters {
                                                                  axis 0.000000 -1.000000 -0.000004
                                                                  anchor 0.019993 -0.103680 0.000000
                                                                }
                                                                device [
                                                                  RotationalMotor {
                                                                    name "L_elbow_yaw_joint"
                                                                    maxVelocity 3.1416
                                                                    minPosition -2.9322
                                                                    maxPosition 2.9322
                                                                    maxTorque 20.0
                                                                  }
                                                                  PositionSensor {
                                                                    name "L_elbow_yaw_joint_sensor"
                                                                  }
                                                                ]
                                                                endPoint Solid {
                                                                  translation 0.019993 -0.103680 0.000000
                                                                  rotation 1.000000 0.000000 0.000000 1.570800
                                                                  children [
                                                                    Shape {
                                                                      appearance DEF L_elbow_yaw_link_material PBRAppearance {
                                                                        baseColor 0.752941 0.752941 0.752941
                                                                        roughness 1.000000
                                                                        metalness 0
                                                                      }
                                                                      geometry DEF L_elbow_yaw_link Mesh {
                                                                        url "meshes/utars/L_elbow_yaw_link.STL"
                                                                      }
                                                                    }
                                                                    HingeJoint {
                                                                      jointParameters HingeJointParameters {
                                                                        axis -1.000000 0.000007 0.000004
                                                                        anchor 0.000000 0.000000 0.118320
                                                                      }
                                                                      device [
                                                                        RotationalMotor {
                                                                          name "L_wrist_pitch_joint"
                                                                          maxVelocity 3.1416
                                                                          minPosition -1.658
                                                                          maxPosition 1.658
                                                                          maxTorque 20.0
                                                                        }
                                                                        PositionSensor {
                                                                          name "L_wrist_pitch_joint_sensor"
                                                                        }
                                                                      ]
                                                                      endPoint Solid {
                                                                        translation 0.000000 0.000000 0.118320
                                                                        rotation 0.707105 -0.000003 -0.707108 3.141598
                                                                        children [
                                                                          Shape {
                                                                            appearance DEF L_wrist_pitch_link_material PBRAppearance {
                                                                              baseColor 0.752940 0.752940 0.752940
                                                                              roughness 1.000000
                                                                              metalness 0
                                                                            }
                                                                            geometry DEF L_wrist_pitch_link Mesh {
                                                                              url "meshes/utars/L_wrist_pitch_link.STL"
                                                                            }
                                                                          }
                                                                          HingeJoint {
                                                                            jointParameters HingeJointParameters {
                                                                              axis -0.000004 1.000000 0.000000
                                                                              anchor -0.076000 0.000000 0.000000
                                                                            }
                                                                            device [
                                                                              RotationalMotor {
                                                                                name "L_wrist_roll_joint"
                                                                                maxVelocity 3.1416
                                                                                minPosition -2.0245
                                                                                maxPosition 2.0245
                                                                                maxTorque 20.0
                                                                              }
                                                                              PositionSensor {
                                                                                name "L_wrist_roll_joint_sensor"
                                                                              }
                                                                            ]
                                                                            endPoint Solid {
                                                                              translation -0.076000 0.000000 0.000000
                                                                              rotation -0.577352 0.577348 0.577350 2.094395
                                                                              children [
                                                                                Shape {
                                                                                  appearance DEF L_wrist_roll_link_material PBRAppearance {
                                                                                    baseColor 0.752941 0.752941 0.752941
                                                                                    roughness 1.000000
                                                                                    metalness 0
                                                                                  }
                                                                                  geometry DEF L_wrist_roll_link Mesh {
                                                                                    url "meshes/utars/L_wrist_roll_link.STL"
                                                                                  }
                                                                                }
                                                                                Solid {
                                                                                  translation 0.000000 0.077120 0.000000
                                                                                  rotation -0.577350 0.577350 0.577352 2.094399
                                                                                  children [
                                                                                    Shape {
                                                                                      appearance DEF L_sixforce_link_material PBRAppearance {
                                                                                        baseColor 0.698039 0.698039 0.698039
                                                                                        roughness 1.000000
                                                                                        metalness 0
                                                                                      }
                                                                                      geometry DEF L_sixforce_link Mesh {
                                                                                        url "meshes/utars/L_sixforce_link.STL"
                                                                                      }
                                                                                    }
                                                                                    Solid {
                                                                                      translation -0.094000 0.000000 0.011500
                                                                                      rotation 0.707108 0.000003 0.707105 3.141598
                                                                                      children [
                                                                                        Shape {
                                                                                          appearance DEF L_hand_link_material PBRAppearance {
                                                                                            baseColor 0.752940 0.752940 0.752940
                                                                                            roughness 1.000000
                                                                                            metalness 0
                                                                                          }
                                                                                          geometry DEF L_hand_link Mesh {
                                                                                            url "meshes/utars/L_hand_link.STL"
                                                                                          }
                                                                                        }
                                                                                      ]
                                                                                      name "L_hand_link"
                                                                                      boundingObject NULL
                                                                                      physics Physics {
                                                                                        density -1
                                                                                        mass 0.000000
                                                                                        centerOfMass [ -0.000515 0.000005 0.046181 ]
                                                                                        inertiaMatrix [
                                                                                          7.472900e-04 7.847900e-04 3.628100e-04
                                                                                          1.000000e-08 5.460000e-05 -7.000000e-08
                                                                                        ]
                                                                                      }
                                                                                    }
                                                                                  ]
                                                                                  name "L_sixforce_link"
                                                                                  boundingObject Pose{
                                                                                    translation 0 0 -0.05
                                                                                    children [
                                                                                      Box{
                                                                                        size 0.1 0.1 0.2
                                                                                      }
                                                                                    ]
                                                                                  }
                                                                                  physics Physics {
                                                                                    density -1
                                                                                    mass 0.000000
                                                                                    centerOfMass [ 0.000186 0.000002 -0.011066 ]
                                                                                    inertiaMatrix [
                                                                                      3.570000e-05 5.030000e-05 6.130000e-05
                                                                                      2.100000e-07 -7.800000e-07 6.000000e-08
                                                                                    ]
                                                                                  }
                                                                                }
                                                                              ]
                                                                              name "L_wrist_roll_link"
                                                                              boundingObject NULL
                                                                              physics Physics {
                                                                                density -1
                                                                                mass 0.000000
                                                                                centerOfMass [ -0.000005 0.017056 -0.000749 ]
                                                                                inertiaMatrix [
                                                                                  2.735600e-04 2.154600e-04 1.102300e-04
                                                                                  0.000000e+00 -2.000000e-08 -7.680000e-06
                                                                                ]
                                                                              }
                                                                            }
                                                                          }
                                                                        ]
                                                                        name "L_wrist_pitch_link"
                                                                        boundingObject NULL
                                                                        physics Physics {
                                                                          density -1
                                                                          mass 0.000000
                                                                          centerOfMass [ -0.038445 -0.004856 -0.004772 ]
                                                                          inertiaMatrix [
                                                                            9.003600e-04 2.731400e-03 2.735800e-03
                                                                            -2.185100e-04 2.270000e-04 3.050000e-05
                                                                          ]
                                                                        }
                                                                      }
                                                                    }
                                                                  ]
                                                                  name "L_elbow_yaw_link"
                                                                  boundingObject Box{
                                                                    size 0.1 0.1 0.3
                                                                  }
                                                                  physics Physics {
                                                                    density -1
                                                                    mass 0.000000
                                                                    centerOfMass [ 0.000974 0.000179 0.045777 ]
                                                                    inertiaMatrix [
                                                                      1.438400e-03 1.636300e-03 8.758300e-04
                                                                      3.890000e-06 -5.220000e-05 7.000000e-07
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            ]
                                                            name "L_elbow_roll_link"
                                                            boundingObject NULL
                                                            physics Physics {
                                                              density -1
                                                              mass 0.000000
                                                              centerOfMass [ 0.008530 -0.057410 0.000893 ]
                                                              inertiaMatrix [
                                                                2.538400e-03 1.395100e-03 2.205000e-03
                                                                3.061100e-04 7.490000e-06 -3.350000e-05
                                                              ]
                                                            }
                                                          }
                                                        }
                                                      ]
                                                      name "L_shoulder_yaw_link"
                                                      boundingObject Pose{
                                                        translation 0 0 0.05
                                                        children [
                                                          Box{
                                                            size 0.1 0.1 0.35
                                                          }
                                                        ]
                                                      }
                                                      physics Physics {
                                                        density -1
                                                        mass 0.000000
                                                        centerOfMass [ -0.008990 0.002780 0.128600 ]
                                                        inertiaMatrix [
                                                          1.565600e-02 1.576800e-02 2.105700e-03
                                                          5.400000e-05 1.565300e-03 -4.360000e-04
                                                        ]
                                                      }
                                                    }
                                                  }
                                                ]
                                                name "L_shoulder_roll_link"
                                                boundingObject NULL
                                                physics Physics {
                                                  density -1
                                                  mass 0.000000
                                                  centerOfMass [ -0.036346 0.001975 -0.005270 ]
                                                  inertiaMatrix [
                                                    1.230500e-03 1.891500e-03 1.429800e-03
                                                    -1.900000e-05 1.070000e-04 -1.300000e-05
                                                  ]
                                                }
                                              }
                                            }
                                          ]
                                          name "L_shoulder_pitch_link"
                                          boundingObject NULL
                                          physics Physics {
                                            density -1
                                            mass 0.000000
                                            centerOfMass [ 0.000221 -0.005093 -0.052630 ]
                                            inertiaMatrix [
                                              1.175900e-03 1.123700e-03 9.004100e-04
                                              -4.840000e-06 -2.410000e-06 -3.120000e-05
                                            ]
                                          }
                                        }
                                      }
                                      HingeJoint {
                                        jointParameters HingeJointParameters {
                                          axis -0.000011 0.984814 -0.173612
                                          anchor 0.000000 -0.153870 0.000000
                                        }
                                        device [
                                          RotationalMotor {
                                            name "R_shoulder_pitch_joint"
                                            maxVelocity 3.1416
                                            minPosition -2.8623
                                            maxPosition 2.8623
                                            maxTorque 80.0
                                          }
                                          PositionSensor {
                                            name "R_shoulder_pitch_joint_sensor"
                                          }
                                        ]
                                        endPoint Solid {
                                          translation 0.000000 -0.153870 0.000000
                                          rotation 0.608113 0.608118 0.510285 4.085287
                                          children [
                                            Shape {
                                              appearance DEF R_shoulder_pitch_link_material PBRAppearance {
                                                baseColor 0.752941 0.752941 0.752941
                                                roughness 1.000000
                                                metalness 0
                                              }
                                              geometry DEF R_shoulder_pitch_link Mesh {
                                                url "meshes/utars/R_shoulder_pitch_link.STL"
                                              }
                                            }
                                            HingeJoint {
                                              jointParameters HingeJointParameters {
                                                axis 0.000000 1.000000 -0.000004
                                                anchor 0.000000 0.000000 -0.060000
                                              }
                                              device [
                                                RotationalMotor {
                                                  name "R_shoulder_roll_joint"
                                                  maxVelocity 3.1416
                                                  minPosition -1.885
                                                  maxPosition 0.1222
                                                  maxTorque 80.0
                                                }
                                                PositionSensor {
                                                  name "R_shoulder_roll_joint_sensor"
                                                }
                                              ]
                                              endPoint Solid {
                                                translation 0.000000 0.000000 -0.060000
                                                rotation -1.000000 0.000000 0.000000 1.570800
                                                children [
                                                  Shape {
                                                    appearance DEF R_shoulder_roll_link_material PBRAppearance {
                                                      baseColor 0.752940 0.752940 0.752940
                                                      roughness 1.000000
                                                      metalness 0
                                                    }
                                                    geometry DEF R_shoulder_roll_link Mesh {
                                                      url "meshes/utars/R_shoulder_roll_link.STL"
                                                    }
                                                  }
                                                  HingeJoint {
                                                    jointParameters HingeJointParameters {
                                                      axis -1.000000 -0.000004 -0.000004
                                                      anchor -0.083000 0.000000 0.000000
                                                    }
                                                    device [
                                                      RotationalMotor {
                                                        name "R_shoulder_yaw_joint"
                                                        maxVelocity 3.1416
                                                        minPosition -2.9322
                                                        maxPosition 2.9322
                                                        maxTorque 45.0
                                                      }
                                                      PositionSensor {
                                                        name "R_shoulder_yaw_joint_sensor"
                                                      }
                                                    ]
                                                    endPoint Solid {
                                                      translation -0.083000 0.000000 0.000000
                                                      rotation -0.573457 -0.585058 0.573457 2.082878
                                                      children [
                                                        Shape {
                                                          appearance DEF R_shoulder_yaw_link_material PBRAppearance {
                                                            baseColor 0.752941 0.752941 0.752941
                                                            roughness 1.000000
                                                            metalness 0
                                                          }
                                                          geometry DEF R_shoulder_yaw_link Mesh {
                                                            url "meshes/utars/R_shoulder_yaw_link.STL"
                                                          }
                                                        }
                                                        HingeJoint {
                                                          jointParameters HingeJointParameters {
                                                            axis -0.020022 0.999800 -0.000004
                                                            anchor -0.019989 -0.000400 0.213000
                                                          }
                                                          device [
                                                            RotationalMotor {
                                                              name "R_elbow_roll_joint"
                                                              maxVelocity 3.1416
                                                              minPosition -2.6529
                                                              maxPosition 0.0349
                                                              maxTorque 45.0
                                                            }
                                                            PositionSensor {
                                                              name "R_elbow_roll_joint_sensor"
                                                            }
                                                          ]
                                                          endPoint Solid {
                                                            translation -0.019989 -0.000400 0.213000
                                                            rotation -0.999900 -0.010011 0.010011 1.570900
                                                            children [
                                                              Shape {
                                                                appearance DEF R_elbow_roll_link_material PBRAppearance {
                                                                  baseColor 0.752941 0.752941 0.752941
                                                                  roughness 1.000000
                                                                  metalness 0
                                                                }
                                                                geometry DEF R_elbow_roll_link Mesh {
                                                                  url "meshes/utars/R_elbow_roll_link.STL"
                                                                }
                                                              }
                                                              HingeJoint {
                                                                jointParameters HingeJointParameters {
                                                                  axis 0.000000 -1.000000 -0.000004
                                                                  anchor 0.019993 -0.103680 0.000000
                                                                }
                                                                device [
                                                                  RotationalMotor {
                                                                    name "R_elbow_yaw_joint"
                                                                    maxVelocity 3.1416
                                                                    minPosition -2.9322
                                                                    maxPosition 2.9322
                                                                    maxTorque 20.0
                                                                  }
                                                                  PositionSensor {
                                                                    name "R_elbow_yaw_joint_sensor"
                                                                  }
                                                                ]
                                                                endPoint Solid {
                                                                  translation 0.019993 -0.103680 0.000000
                                                                  rotation 1.000000 0.000000 0.000000 1.570800
                                                                  children [
                                                                    Shape {
                                                                      appearance DEF R_elbow_yaw_link_material PBRAppearance {
                                                                        baseColor 0.752941 0.752941 0.752941
                                                                        roughness 1.000000
                                                                        metalness 0
                                                                      }
                                                                      geometry DEF R_elbow_yaw_link Mesh {
                                                                        url "meshes/utars/R_elbow_yaw_link.STL"
                                                                      }
                                                                    }
                                                                    HingeJoint {
                                                                      jointParameters HingeJointParameters {
                                                                        axis -1.000000 0.000007 0.000004
                                                                        anchor 0.000000 0.000000 0.118320
                                                                      }
                                                                      device [
                                                                        RotationalMotor {
                                                                          name "R_wrist_pitch_joint"
                                                                          maxVelocity 3.1416
                                                                          minPosition -1.658
                                                                          maxPosition 1.658
                                                                          maxTorque 20.0
                                                                        }
                                                                        PositionSensor {
                                                                          name "R_wrist_pitch_joint_sensor"
                                                                        }
                                                                      ]
                                                                      endPoint Solid {
                                                                        translation 0.000000 0.000000 0.118320
                                                                        rotation 0.707105 -0.000003 -0.707108 3.141598
                                                                        children [
                                                                          Shape {
                                                                            appearance DEF R_wrist_pitch_link_material PBRAppearance {
                                                                              baseColor 0.752940 0.752940 0.752940
                                                                              roughness 1.000000
                                                                              metalness 0
                                                                            }
                                                                            geometry DEF R_wrist_pitch_link Mesh {
                                                                              url "meshes/utars/R_wrist_pitch_link.STL"
                                                                            }
                                                                          }
                                                                          HingeJoint {
                                                                            jointParameters HingeJointParameters {
                                                                              axis -0.000004 1.000000 0.000000
                                                                              anchor -0.076000 0.000000 0.000000
                                                                            }
                                                                            device [
                                                                              RotationalMotor {
                                                                                name "R_wrist_roll_joint"
                                                                                maxVelocity 3.1416
                                                                                minPosition -2.0245
                                                                                maxPosition 2.0245
                                                                                maxTorque 20.0
                                                                              }
                                                                              PositionSensor {
                                                                                name "R_wrist_roll_joint_sensor"
                                                                              }
                                                                            ]
                                                                            endPoint Solid {
                                                                              translation -0.076000 0.000000 0.000000
                                                                              rotation -0.577352 0.577348 0.577350 2.094395
                                                                              children [
                                                                                Shape {
                                                                                  appearance DEF R_wrist_roll_link_material PBRAppearance {
                                                                                    baseColor 0.752941 0.752941 0.752941
                                                                                    roughness 1.000000
                                                                                    metalness 0
                                                                                  }
                                                                                  geometry DEF R_wrist_roll_link Mesh {
                                                                                    url "meshes/utars/R_wrist_roll_link.STL"
                                                                                  }
                                                                                }
                                                                                Solid {
                                                                                  translation 0.000000 0.077120 0.000000
                                                                                  rotation -0.577350 0.577350 0.577352 2.094399
                                                                                  children [
                                                                                    Shape {
                                                                                      appearance DEF R_sixforce_link_material PBRAppearance {
                                                                                        baseColor 0.698039 0.698039 0.698039
                                                                                        roughness 1.000000
                                                                                        metalness 0
                                                                                      }
                                                                                      geometry DEF R_sixforce_link Mesh {
                                                                                        url "meshes/utars/R_sixforce_link.STL"
                                                                                      }
                                                                                    }
                                                                                    Solid {
                                                                                      translation 0.094000 0.000000 0.011500
                                                                                      rotation -0.000007 1.000000 -0.000007 4.712393
                                                                                      children [
                                                                                        Shape {
                                                                                          appearance DEF R_hand_link_material PBRAppearance {
                                                                                            baseColor 0.752941 0.752941 0.752941
                                                                                            roughness 1.000000
                                                                                            metalness 0
                                                                                          }
                                                                                          geometry DEF R_hand_link Mesh {
                                                                                            url "meshes/utars/R_hand_link.STL"
                                                                                          }
                                                                                        }
                                                                                      ]
                                                                                      name "R_hand_link"
                                                                                      boundingObject NULL
                                                                                      physics Physics {
                                                                                        density -1
                                                                                        mass 0.000000
                                                                                        centerOfMass [ -0.000515 0.000005 0.046181 ]
                                                                                        inertiaMatrix [
                                                                                          7.472900e-04 7.847900e-04 3.628100e-04
                                                                                          1.000000e-08 5.460000e-05 -7.000000e-08
                                                                                        ]
                                                                                      }
                                                                                    }
                                                                                  ]
                                                                                  name "R_sixforce_link"
                                                                                  boundingObject Pose{
                                                                                    translation 0 0 -0.05
                                                                                    children [
                                                                                      Box{
                                                                                        size 0.1 0.1 0.2
                                                                                      }
                                                                                    ]
                                                                                  }
                                                                                  physics Physics {
                                                                                    density -1
                                                                                    mass 0.000000
                                                                                    centerOfMass [ -0.000186 -0.000002 -0.011066 ]
                                                                                    inertiaMatrix [
                                                                                      3.570000e-05 5.030000e-05 6.130000e-05
                                                                                      2.100000e-07 7.800000e-07 -6.000000e-08
                                                                                    ]
                                                                                  }
                                                                                }
                                                                              ]
                                                                              name "R_wrist_roll_link"
                                                                              boundingObject NULL
                                                                              physics Physics {
                                                                                density -1
                                                                                mass 0.000000
                                                                                centerOfMass [ 0.000005 0.017056 0.000749 ]
                                                                                inertiaMatrix [
                                                                                  2.735600e-04 2.154600e-04 1.102300e-04
                                                                                  0.000000e+00 -2.000000e-08 7.680000e-06
                                                                                ]
                                                                              }
                                                                            }
                                                                          }
                                                                        ]
                                                                        name "R_wrist_pitch_link"
                                                                        boundingObject NULL
                                                                        physics Physics {
                                                                          density -1
                                                                          mass 0.000000
                                                                          centerOfMass [ -0.038445 0.004856 0.004772 ]
                                                                          inertiaMatrix [
                                                                            9.003600e-04 2.731400e-03 2.735800e-03
                                                                            2.190000e-04 -2.271100e-04 3.050000e-05
                                                                          ]
                                                                        }
                                                                      }
                                                                    }
                                                                  ]
                                                                  name "R_elbow_yaw_link"
                                                                  boundingObject Box{
                                                                    size 0.1 0.1 0.3
                                                                  }
                                                                  physics Physics {
                                                                    density -1
                                                                    mass 0.000000
                                                                    centerOfMass [ -0.000974 -0.000179 0.045777 ]
                                                                    inertiaMatrix [
                                                                      1.438400e-03 1.640000e-03 8.758300e-04
                                                                      3.890000e-06 5.220000e-05 -7.100000e-07
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            ]
                                                            name "R_elbow_roll_link"
                                                            boundingObject NULL
                                                            physics Physics {
                                                              density -1
                                                              mass 0.000000
                                                              centerOfMass [ 0.008361 -0.057900 -0.000658 ]
                                                              inertiaMatrix [
                                                                2.599800e-03 1.432600e-03 2.248700e-03
                                                                2.975300e-04 -5.050000e-06 2.640000e-05
                                                              ]
                                                            }
                                                          }
                                                        }
                                                      ]
                                                      name "R_shoulder_yaw_link"
                                                      boundingObject  Pose{
                                                        translation 0 0 0.05
                                                        children [
                                                          Box{
                                                            size 0.1 0.1 0.35
                                                          }
                                                        ]
                                                      }
                                                      physics Physics {
                                                        density -1
                                                        mass 0.000000
                                                        centerOfMass [ -0.009056 -0.002980 0.128630 ]
                                                        inertiaMatrix [
                                                          1.564100e-02 1.575100e-02 2.109300e-03
                                                          -4.940000e-05 1.572200e-03 4.680000e-04
                                                        ]
                                                      }
                                                    }
                                                  }
                                                ]
                                                name "R_shoulder_roll_link"
                                                boundingObject NULL
                                                physics Physics {
                                                  density -1
                                                  mass 0.000000
                                                  centerOfMass [ -0.036091 0.001966 0.005345 ]
                                                  inertiaMatrix [
                                                    1.246100e-03 1.912000e-03 1.436800e-03
                                                    -1.870000e-05 -1.090000e-04 1.300000e-05
                                                  ]
                                                }
                                              }
                                            }
                                          ]
                                          name "R_shoulder_pitch_link"
                                          boundingObject NULL
                                          physics Physics {
                                            density -1
                                            mass 0.000000
                                            centerOfMass [ -0.000089 0.005044 -0.052623 ]
                                            inertiaMatrix [
                                              1.177800e-03 1.124400e-03 9.028700e-04
                                              -5.210000e-06 -4.640000e-06 3.150000e-05
                                            ]
                                          }
                                        }
                                      }
                                    ]
                                    name "torso_link"
                                    boundingObject Pose{
                                      translation 0 0 -0.15
                                      children [
                                        Box{
                                          size 0.25 0.3 0.5
                                        }
                                      ]
                                    }
                                    physics Physics {
                                      density -1
                                      mass 0.000000
                                      centerOfMass [ -0.001570 -0.000402 -0.065466 ]
                                      inertiaMatrix [
                                        2.615476e-01 1.995542e-01 1.260000e-01
                                        -1.000560e-03 1.320000e-02 -2.388100e-04
                                      ]
                                    }
                                  }
                                ]
                                name "waist_yaw_link"
                                boundingObject NULL
                                physics Physics {
                                  density -1
                                  mass 0.000000
                                  centerOfMass [ 0.000127 -0.001922 -0.051393 ]
                                  inertiaMatrix [
                                    6.475400e-03 4.369340e-03 6.102590e-03
                                    1.199000e-05 -1.558200e-04 -2.512300e-04
                                  ]
                                }
                              }
                            }
                          ]
                          name "lifter_pitch_3_link"
                          boundingObject Box{
                            size 0.15 0.15 0.15
                          }
                          physics Physics {
                            density -1
                            mass 0.000000
                            centerOfMass [ -0.000021 0.020415 -0.008391 ]
                            inertiaMatrix [
                              9.316690e-03 6.764540e-03 7.939860e-03
                              2.705000e-05 -2.875000e-05 -4.142400e-04
                            ]
                          }
                        }
                      }
                    ]
                    name "lifter_pitch_2_link"
                    boundingObject Pose{
                      translation 0 0.1 0.0
                      children [
                        Box{
                          size 0.15 0.3 0.2
                        }
                      ]
                    }
                    physics Physics {
                      density -1
                      mass 0.000000
                      centerOfMass [ -0.000004 0.126283 0.013213 ]
                      inertiaMatrix [
                        2.983932e-02 1.161597e-02 2.171850e-02
                        5.300000e-07 -1.600000e-07 4.530000e-05
                      ]
                    }
                  }
                }
              ]
              name "lifter_pitch_1_link"
              boundingObject Pose{
                translation 0 0.1 0.0
                children [
                  Box{
                    size 0.15 0.25 0.2
                  }
                ]
              }
              physics Physics {
                density -1
                mass 0.000000
                centerOfMass [ 0.000000 0.138000 -0.017166 ]
                inertiaMatrix [
                  2.921288e-01 4.460403e-02 2.891671e-01
                  -4.851700e-04 0.000000e+00 -1.100000e-07
                ]
              }
            }
          }
        ]
        name "lifter_base_link"
        boundingObject NULL
        physics Physics {
          density -1
          mass 0.000000
          centerOfMass [ -0.009477 0.000000 -0.040774 ]
          inertiaMatrix [
            4.411150e-03 9.562620e-03 8.454610e-03
            1.100000e-07 5.017800e-04 0.000000e+00
          ]
        }
      }
      Solid {
        translation 0 0 0
        children [
          DEF FRONT_LIDAR_2D Lidar2d {
            translation 0.299632 0 0.2
            rotation 0 0 1 1.56702
            name "lidar_front"
          }
          DEF BACK_LIDAR_2D Lidar2d {
            translation -0.2985475 0 0.2
            rotation 0 0 1 -1.5725575
            name "lidar_back"
          }
          InertialUnit {
            translation 0.1 0.07 -0.05
            rotation 0 0 1 0
            name "imu_inertialunit"
          }
          Accelerometer {
            translation 0.1 0.07 -0.05
            rotation 0 0 1 0
            name "imu_accelerometer"
          }
          Gyro {
            translation 0.1 0.07 -0.05
            rotation 0 0 1 0
            name "imu_gyro"
          }
        ]
        name "sensor_base"
        physics Physics {
          density 10
          mass 2
        }
      }
    ]
  }
}
