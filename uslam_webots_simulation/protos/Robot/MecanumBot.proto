#VRML_SIM R2025a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/kuka/youbot/protos/Youbot.proto
# keywords: robot/wheeled
# The KUKA youBot is a powerful, educational robot that is especially designed for research and education in mobile manipulation, which counts as a key technology for professional service robotics. It consists of an omnidirectional platform, a five degree-of-freedom robot arm and a two-finger gripper.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/BodyMesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm0Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm1Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm2Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm3Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm4Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm5Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/FingerMesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/InteriorWheel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/ExteriorWheel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/PlateMesh.proto"

PROTO MecanumBot [
  field SFVec3f    translation     0 0 0                 # Is `Pose.translation`.
  field SFRotation rotation        0 1 0 0               # Is `Pose.rotation`.
  field SFString   name            "MecanumBot"    # Is `Solid.name`.
  field SFString   controller      "<generic>"           # Is `Robot.controller`.
  field MFString   controllerArgs  []                    # Is `Robot.controllerArgs`.
  field SFString   customData      ""                    # Is `Robot.customData`.
  field SFBool     supervisor      TRUE                 # Is `Robot.supervisor`.
  field SFBool     synchronization TRUE                  # Is `Robot.synchronization`.
  field MFNode     extensionSlot   [
  ]  # Extends the robot with new nodes in the extension slot.
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    children [
      BodyMesh {
      }
      Group {
        children IS extensionSlot
      }
      DEF WHEEL1 InteriorWheel {
        translation 0.2281268273939244 -0.15800001126722663 -0.054698220116484814
        anchor 0.228 -0.158 -0.055
        name "wheel1"
        sensorName "wheel1sensor"
      }
      DEF WHEEL2 ExteriorWheel {
        translation 0.228121289171915 0.1579999838153725 -0.0546946118169235
        anchor 0.228 0.158 -0.055
        name "wheel2"
        sensorName "wheel2sensor"
      }
      DEF WHEEL3 ExteriorWheel {
        translation -0.2281490864288505 -0.1580000069581775 -0.05479149063522682
        anchor -0.228 -0.158 -0.055
        name "wheel3"
        sensorName "wheel3sensor"
      }
      DEF WHEEL4 InteriorWheel {
        translation -0.2282544205456349 0.15799998544279248 -0.05495983827422764
        anchor -0.228 0.158 -0.055
        name "wheel4"
        sensorName "wheel4sensor"
      }
      DEF PLATE Solid {
        translation -0.155 0 0
        children [
          PlateMesh {
          }
        ]
        name "plate"
        boundingObject Group {
          children [
            Pose {
              translation 0.008 0 0.045
              children [
                Box {
                  size 0.25 0.17 0.007
                }
              ]
            }
            Pose {
              translation -0.015 0.112 0.045
              rotation 0 0 1 -0.13
              children [
                Box {
                  size 0.17 0.08 0.007
                }
              ]
            }
            Pose {
              translation -0.015 -0.112 0.045
              rotation 0 0 1 0.13
              children [
                Box {
                  size 0.17 0.08 0.007
                }
              ]
            }
            Pose {
              translation 0.076 0.084 0.045
              rotation 0 0 1 0.81
              children [
                Box {
                  size 0.08 0.08 0.007
                }
              ]
            }
            Pose {
              translation 0.076 -0.084 0.045
              rotation 0 0 1 -0.81
              children [
                Box {
                  size 0.08 0.08 0.007
                }
              ]
            }
          ]
        }
        physics Physics {
          density -1
          mass 0.5
        }
      }
    ]
    name "youBot"
    model "KUKA youBot"
    description "KUKA youBot"
    boundingObject Group {
      children [
        Pose {
          translation 0 0 -0.045
          children [
            Box {
              size 0.34 0.34 0.09
            }
          ]
        }
        Pose {
          translation 0 0 -0.045
          children [
            Box {
              size 0.56 0.23 0.09
            }
          ]
        }
      ]
    }
    physics Physics {
      density -1
      mass 22
      centerOfMass [
        0 0 -0.045
      ]
      inertiaMatrix [
        0.166204 0.418086 0.55459
        0 0 0
      ]
    }
  }
}
