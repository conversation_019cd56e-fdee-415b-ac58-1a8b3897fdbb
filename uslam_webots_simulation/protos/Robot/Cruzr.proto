#VRML_SIM R2025a utf8
# license: Apache License 2.0
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/robotis/turtlebot/protos/TurtleBot3Burger.proto
# keywords: robot/wheeled
# Burger model of the third version of the Cruzr robot.

EXTERNPROTO "DiffChassisBase.proto"
EXTERNPROTO "../Sensor/RGBD.proto"
EXTERNPROTO "../Sensor/Lidar2D.proto"
EXTERNPROTO "../Sensor/IMU.proto"

PROTO Cruzr [
  field SFVec3f    translation     0 0 0                 # Is `Pose.translation`.
  field SFRotation rotation        0 1 0 0               # Is `Pose.rotation`.
  field SFString   name            "Cruzr"    # Is `Solid.name`.
  field SFString   controller      "<generic>"           # Is `Robot.controller`.
  field MFString   controllerArgs  []                    # Is `Robot.controllerArgs`.
  field SFString   customData      ""                    # Is `Robot.customData`.
  field SFBool     supervisor      TRUE                 # Is `Robot.supervisor`.
  field SFBool     synchronization TRUE                  # Is `Robot.synchronization`.
]
{
  DiffChassisBase {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    extensionSlot [
      DEF MESH_SOLID Solid {
        children [
          DEF BODY_MESH Pose {
            translation 0 0 0.01
            rotation 0 0 1 1.57
            children [
              CadShape {
                url "meshes/cruzr/base_link.dae"
              }
            ]
          }
          DEF HEAD_MESH Pose {
            translation 0 0 1.0
            rotation 0 0 1 1.57
            children [
              CadShape {
                url "meshes/cruzr/head.dae"
              }
            ]
          }
        ]
        boundingObject Group {
          children [
            DEF BODY_BBOX Pose {
              translation 0 0 0.3
              children [
                Shape {
                  geometry Cylinder {
                    height 0.5
                    radius 0.28
                  }
                }
              ]
            }
            DEF HEAD_BBOX Pose {
              translation 0 0 0.9
              children [
                Shape {
                  geometry Cylinder {
                    height 0.7
                    radius 0.24
                  }
                }
              ]
            }
          ]
        }
      }
      Lidar2D {
        translation 0 0 0.21
        name "lidar"
      }
      RGBD {
        translation 0.22 0 0.56
        rotation 0 0 1 0
        name "rgbd"
      }
      IMU {
        translation 0.0 0.0 0.1
        name "imu"
      }

      Accelerometer {
        translation -0.032 0 0.078
      }
      Gyro {
        translation -0.032 0 0.078
      }
      Compass {
        translation -0.032 0 0.078
      }

    ]
  }
}
