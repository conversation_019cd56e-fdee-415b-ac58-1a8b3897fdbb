#VRML_SIM R2025a utf8
# license: Apache License 2.0.
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/pal_robotics/tiago_base/protos/TiagoBase.proto
# keywords: robot/wheeled
# This is a PROTO file for Webots for the base diff chassis robot WheelDistance = 0.4 WheelRadius = 0.0985
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Asphalt.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/BrushedAluminium.proto"

PROTO DiffChassisBase [
  field  SFVec3f     translation      0 0 0.097
  field  SFRotation  rotation         0 0 1 0
  field  SFString    name             "DiffChassisBase"
  field  SFString    controller       "<generic>"
  field  MFString    controllerArgs   []
  field  SFString    window          "<generic>"
  field  SFString    customData       ""
  field  SFBool      supervisor       FALSE
  field  SFBool      synchronization  TRUE
  field  SFBool      selfCollision    FALSE   # Enables/disables the detection of collisions within the robot.
  field  MFNode      extensionSlot         []      # Extends the robot with new nodes in the extension slot.
  field  SFFloat     wheelToCenterDis    0.2022
  field  SFFloat     casterWheelPosX      0.1695
  field  SFFloat     casterWheelPosY      0.102
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    controller IS controller
    controllerArgs IS controllerArgs
    window IS window
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    selfCollision IS selfCollision
    name IS name
    children [
      DEF BODY_EXTENSION Pose {
        translation 0 0 -0.097
        children IS extensionSlot
      }
      DEF RIGHT_WHEEL_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 1 0
          anchor %<= 0 >% %<= -(fields.wheelToCenterDis.value) >% %<= 0 >%
          suspensionSpringConstant 0
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
          RotationalMotor {
            name "right wheel motor"
            acceleration 5.0762
            maxVelocity 10.1523
            maxTorque 10
          }
          PositionSensor {
            name "right wheel sensor"
          }
        ]
        endPoint DEF RIGHT_WHEEL Solid {
          translation %<= 0 >% %<= -(fields.wheelToCenterDis.value) >% %<= 0 >%
          rotation 1 0 0 1.5708
          children [
            DEF BIG_WHEEL Group {
              children [
                Pose {
                  translation 0 0 0.042
                  children [
                    Shape {
                      appearance PBRAppearance {
                        baseColor 0 0 0
                        roughness 1
                        metalness 0
                      }
                      geometry Cylinder {
                        height 0.005
                        radius 0.016
                      }
                    }
                  ]
                }
                DEF TIRE Shape {
                  appearance Asphalt {
                    textureTransform TextureTransform {
                      scale 25 25
                    }
                    IBLStrength 0.5
                  }
                  geometry Mesh {
                    url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/tire.obj"
                  }
                }
                DEF RIM Shape {
                  appearance PBRAppearance {
                    baseColorMap ImageTexture {
                      url [
                        "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/textures/metal_stainless_clean_base_color.jpg"
                      ]
                    }
                    roughnessMap ImageTexture {
                      url [
                        "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/textures/metal_stainless_clean_roughness.jpg"
                      ]
                    }
                    metalness 0.9
                    textureTransform TextureTransform {
                      scale 10 10
                    }
                  }
                  geometry Mesh {
                    url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/rim.obj"
                  }
                }
              ]
            }
          ]
          name "wheel_right_link"
          boundingObject Pose {
            children [
              DEF WHEEL_BOUNDING_OBJECT Cylinder {
                height 0.04
                radius 0.0985
              }
            ]
          }
          physics Physics {
            density -1
            mass 1.82362
          }
        }
      }
      DEF LEFT_WHEEL_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 1 0
          anchor %<= 0 >% %<= fields.wheelToCenterDis.value >% %<= 0 >%
          suspensionSpringConstant 0
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
          RotationalMotor {
            name "left wheel motor"
            acceleration 5.0762
            maxVelocity 10.1523
            maxTorque 10
          }
          PositionSensor {
            name "left wheel sensor"
          }
        ]
        endPoint DEF LEFT_WHEEL Solid {
          translation %<= 0 >% %<= fields.wheelToCenterDis.value >% %<= 0 >%
          rotation 1 0 0 1.5708
          children [
            Pose {
              rotation -1 0 0 3.141593
              children [
                DEF BIG_WHEEL Group {
                  children [
                    Pose {
                      translation 0 0 0.042
                      children [
                        Shape {
                          appearance PBRAppearance {
                            baseColor 0 0 0
                            roughness 1
                            metalness 0
                          }
                          geometry Cylinder {
                            height 0.005
                            radius 0.016
                          }
                        }
                      ]
                    }
                    DEF TIRE Shape {
                      appearance Asphalt {
                        textureTransform TextureTransform {
                          scale 25 25
                        }
                        IBLStrength 0.5
                      }
                      geometry Mesh {
                        url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/tire.obj"
                      }
                    }
                    DEF RIM Shape {
                      appearance PBRAppearance {
                        baseColorMap ImageTexture {
                          url [
                            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/textures/metal_stainless_clean_base_color.jpg"
                          ]
                        }
                        roughnessMap ImageTexture {
                          url [
                            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/textures/metal_stainless_clean_roughness.jpg"
                          ]
                        }
                        metalness 0.9
                        textureTransform TextureTransform {
                          scale 10 10
                        }
                      }
                      geometry Mesh {
                        url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/rim.obj"
                      }
                    }
                  ]
                }
              ]
            }
          ]
          name "wheel_left_link"
          boundingObject Pose {
            children [
              DEF WHEEL_BOUNDING_OBJECT Cylinder {
                height 0.04
                radius 0.0985
              }
            ]
          }
          physics Physics {
            density -1
            mass 1.82362
          }
        }
      }
      DEF CASTER_WHEEL_FRONT_RIGHT_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 0 1
          anchor %<= fields.casterWheelPosX.value >% %<= -(fields.casterWheelPosY.value) >% %<= -0.0335 >%
          dampingConstant 0
          suspensionSpringConstant 250000
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF CASTER_WHEEL_FRONT_RIGHT Solid {
          translation %<= fields.casterWheelPosX.value >% %<= -(fields.casterWheelPosY.value) >% %<= -0.0335 >%
          rotation 0 0 1 0
          children [
            DEF SMALL_WHEEL_JOINT HingeJoint {
              jointParameters HingeJointParameters {
                axis 0 1 0
                anchor -0.016 0 -0.04
              }
              device [
              ]
              endPoint DEF SMALL_WHEEL Solid {
                translation -0.016 0 -0.04
                rotation -1 0 0 1.5708
                children [
                  DEF SMALL_WHEEL_SHAPE Shape {
                    appearance Asphalt {
                      textureTransform TextureTransform {
                        scale 25 25
                      }
                      IBLStrength 0.5
                    }
                    geometry Mesh {
                      url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/small_wheel_shape.obj"
                    }
                  }
                ]
                name "caster_front_right_2_link"
                boundingObject DEF SMALL_WHEEL_BO Pose {
                  children [
                    Cylinder {
                      height 0.015
                      radius 0.025
                    }
                  ]
                }
                physics DEF SMALL_WHEEL_PHYSIC Physics {
                  density -1
                  mass 0.088558
                }
              }
            }
            DEF CASTER_SUPPORT Shape {
              appearance BrushedAluminium {
                textureTransform TextureTransform {
                  rotation -1.57
                  scale 2 2
                }
              }
              geometry Mesh {
                url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/caster_support.obj"
              }
            }
          ]
          name "caster_front_right_1_link"
          boundingObject DEF CASTER_WHEEL_BO Pose {
            translation -0.003 0 -0.022
            children [
              DEF CASTER_SUPPORT_BO Box {
                size 0.036 0.032 0.04
              }
            ]
          }
          physics DEF CASTER_WHEEL_PHYSIC Physics {
            density -1
            mass 0.051448
          }
        }
      }
      DEF CASTER_WHEEL_FRONT_LEFT_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 0 1
          anchor %<= fields.casterWheelPosX.value >% %<= fields.casterWheelPosY.value >% %<= -0.0335 >%
          dampingConstant 0
          suspensionSpringConstant 250000
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF CASTER_WHEEL_FRONT_LEFT Solid {
          translation %<= fields.casterWheelPosX.value >% %<= fields.casterWheelPosY.value >% %<= -0.0335 >%
          rotation 0 0 1 0
          children [
            DEF SMALL_WHEEL_JOINT HingeJoint {
              jointParameters HingeJointParameters {
                axis 0 1 0
                anchor -0.016 0 -0.04
              }
              device [
              ]
              endPoint DEF SMALL_WHEEL Solid {
                translation -0.016 0 -0.04
                rotation -1 0 0 1.5708
                children [
                  DEF SMALL_WHEEL_SHAPE Shape {
                    appearance Asphalt {
                      textureTransform TextureTransform {
                        scale 25 25
                      }
                      IBLStrength 0.5
                    }
                    geometry Mesh {
                      url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/small_wheel_shape.obj"
                    }
                  }
                ]
                name "caster_front_left_2_link"
                boundingObject DEF SMALL_WHEEL_BO Pose {
                  children [
                    Cylinder {
                      height 0.015
                      radius 0.025
                    }
                  ]
                }
                physics DEF SMALL_WHEEL_PHYSIC Physics {
                  density -1
                  mass 0.088558
                }
              }
            }
            DEF CASTER_SUPPORT Shape {
              appearance BrushedAluminium {
                textureTransform TextureTransform {
                  rotation -1.57
                  scale 2 2
                }
              }
              geometry Mesh {
                url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/caster_support.obj"
              }
            }
          ]
          name "caster_front_left_1_link"
          boundingObject DEF CASTER_WHEEL_BO Pose {
            translation -0.003 0 -0.022
            children [
              DEF CASTER_SUPPORT_BO Box {
                size 0.036 0.032 0.04
              }
            ]
          }
          physics DEF CASTER_WHEEL_PHYSIC Physics {
            density -1
            mass 0.051448
          }
        }
      }
      DEF CASTER_WHEEL_BACK_RIGHT_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 0 1
          anchor %<= -(fields.casterWheelPosX.value) >% %<= -(fields.casterWheelPosY.value) >% %<= -0.0335 >%
          dampingConstant 0
          suspensionSpringConstant 250000
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF CASTER_WHEEL_BACK_RIGHT Solid {
          translation %<= -(fields.casterWheelPosX.value) >% %<= -(fields.casterWheelPosY.value) >% %<= -0.0335 >%
          rotation 0 0 1 0
          children [
            DEF SMALL_WHEEL_JOINT HingeJoint {
              jointParameters HingeJointParameters {
                axis 0 1 0
                anchor -0.016 0 -0.04
              }
              device [
              ]
              endPoint DEF SMALL_WHEEL_JOINT Solid {
                translation -0.016 0 -0.04
                rotation -1 0 0 1.5708
                children [
                  DEF SMALL_WHEEL_SHAPE Shape {
                    appearance Asphalt {
                      textureTransform TextureTransform {
                        scale 25 25
                      }
                      IBLStrength 0.5
                    }
                    geometry Mesh {
                      url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/small_wheel_shape.obj"
                    }
                  }
                ]
                name "caster_back_right_2_link"
                boundingObject DEF SMALL_WHEEL_BO Pose {
                  children [
                    Cylinder {
                      height 0.015
                      radius 0.025
                    }
                  ]
                }
                physics DEF SMALL_WHEEL_PHYSIC Physics {
                  density -1
                  mass 0.088558
                }
              }
            }
            DEF CASTER_SUPPORT Shape {
              appearance BrushedAluminium {
                textureTransform TextureTransform {
                  rotation -1.57
                  scale 2 2
                }
              }
              geometry Mesh {
                url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/caster_support.obj"
              }
            }
          ]
          name "caster_back_right_1_link"
          boundingObject DEF CASTER_WHEEL_BO Pose {
            translation -0.003 0 -0.022
            children [
              DEF CASTER_SUPPORT_BO Box {
                size 0.036 0.032 0.04
              }
            ]
          }
          physics DEF CASTER_WHEEL_PHYSIC Physics {
            density -1
            mass 0.051448
          }
        }
      }
      DEF CASTER_WHEEL_BACK_LEFT_JOINT HingeJoint {
        jointParameters HingeJointParameters {
          axis 0 0 1
          anchor %<= -(fields.casterWheelPosX.value) >% %<= fields.casterWheelPosY.value >% %<= -0.0335 >%
          dampingConstant 0
          suspensionSpringConstant 250000
          suspensionDampingConstant 0
          suspensionAxis 0 0 1
        }
        device [
        ]
        endPoint DEF CASTER_WHEEL_BACK_LEFT Solid {
          translation %<= -(fields.casterWheelPosX.value) >% %<= fields.casterWheelPosY.value >% %<= -0.0335 >%
          rotation 0 0 1 0
          children [
            DEF SMALL_WHEEL_JOINT HingeJoint {
              jointParameters HingeJointParameters {
                axis 0 1 0
                anchor -0.016 0 -0.04
              }
              device [
              ]
              endPoint DEF SMALL_WHEEL Solid {
                translation -0.016 0 -0.04
                rotation -1 0 0 1.5708
                children [
                  DEF SMALL_WHEEL_SHAPE Shape {
                    appearance Asphalt {
                      textureTransform TextureTransform {
                        scale 25 25
                      }
                      IBLStrength 0.5
                    }
                    geometry Mesh {
                      url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/small_wheel_shape.obj"
                    }
                  }
                ]
                name "caster_back_left_2_link"
                boundingObject DEF SMALL_WHEEL_BO Pose {
                  children [
                    Cylinder {
                      height 0.015
                      radius 0.025
                    }
                  ]
                }
                physics DEF SMALL_WHEEL_PHYSIC Physics {
                  density -1
                  mass 0.088558
                }
              }
            }
            DEF CASTER_SUPPORT Shape {
              appearance BrushedAluminium {
                textureTransform TextureTransform {
                  rotation -1.57
                  scale 2 2
                }
              }
              geometry Mesh {
                url "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/pal_robotics/tiago_base/protos/meshes/caster_support.obj"
              }
            }
          ]
          name "caster_back_left_1_link"
          boundingObject DEF CASTER_WHEEL_BO Pose {
            translation -0.003 0 -0.022
            children [
              DEF CASTER_SUPPORT_BO Box {
                size 0.036 0.032 0.04
              }
            ]
          }
          physics DEF CASTER_WHEEL_PHYSIC Physics {
            density -1
            mass 0.051448
          }
        }
      }
    ]
    name "DiffChassisBase"
    boundingObject Group {
      children [
        DEF BASE_BO Pose {
          translation 0 0 0.06
          children [
            Shape {
              geometry Cylinder {
                height 0.276
                radius 0.20
              }
            }
          ]
        }
      ]
    }
    physics Physics {
      density -1
      mass 28.26649
    }
  }
}
