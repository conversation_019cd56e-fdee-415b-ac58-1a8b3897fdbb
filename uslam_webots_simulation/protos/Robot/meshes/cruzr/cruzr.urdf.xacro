<?xml version="1.0" encoding="utf-8"?>
<robot name="cruzr" xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:macro name="default_inertial">
    <inertial>
      <mass value="1.0" />
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0" />
    </inertial>
  </xacro:macro>
  <link
    name="dummy_link">
  </link>

  <link
    name="base_link">
    <visual>
      <origin rpy="1.5707963268 0 1.5707963268" xyz="0 0 -0.01"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/base_link.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963268 0 1.5707963268" xyz="0 0 -0.01"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/base_link.dae"/>
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0.4" rpy="0 0 0" />
      <mass value="15.0" />
      <inertia ixx="6.0" ixy="0.0" ixz="0.0" iyy="6.0" iyz="0.0" izz="4.0" />
    </inertial>
  </link>

  <joint name="dummy_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="dummy_link"/>
    <child link="base_link"/>
  </joint>

  <link
    name="head">
    <visual>
      <origin rpy="0 1.5707963268 -0.2" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/head.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 -1.5707963268 ${0.36-0.2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/head.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="head_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-0.16" upper="0.28176" velocity="0.5"/>
    <origin rpy="1.5707963268 -0.16 0" xyz="0 0 0.969"/>
    <parent link="base_link"/>
    <child link="head"/>
  </joint>

  <link
    name="left_shoulder">
    <visual>
      <origin rpy="0 1.5707963268 0" xyz="0 0 -0.072"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_shoulder.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.074"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_shoulder.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="left_shoulder_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-2.874557278" upper="2.8850292535" velocity="0.5"/>
    <origin rpy="-1.5707963268 0 0" xyz="0 0.28 0.969"/>
    <parent link="base_link"/>
    <child link="left_shoulder"/>
  </joint>

  <link
    name="left_uparm">
    <visual>
      <origin rpy="0 3.1415926 1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_uparm.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="3.1415926 0 ${3.1415926 - 0.05}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_uparm.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>


  <joint name="left_uparm_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-0.00872664626" upper="2.6965336943" velocity="0.5"/>
    <origin rpy="1.5707963268 0 1.5707963268" xyz="0 0 0"/>
    <parent link="left_shoulder"/>
    <child link="left_uparm"/>
  </joint>

  <link
    name="left_elbow">
    <visual>
      <origin rpy="-1.5707963268 0 1.5707963268" xyz="0 0 -0.05"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_elbow.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 ${1.5707963268} 1.5707963268" xyz="0 0 -0.069"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_elbow.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="left_elbow_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-2.153736297" upper="0.46425758103" velocity="0.5"/>
    <origin rpy="1.5707963268 0 1.5707963268" xyz="0.201 0 0"/>
    <parent link="left_uparm"/>
    <child link="left_elbow"/>
  </joint>

  <link
    name="left_lowerarm">
    <visual>
      <origin rpy="-3.1415926 1.5707963268 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_lowerarm.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 1.5707963268 -1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_lowerarm.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="left_lowerarm_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-0.099483767364" upper="1.2967796342" velocity="0.5"/>
    <origin rpy="1.5707963268 0 0" xyz="0 0 0"/>
    <parent link="left_elbow"/>
    <child link="left_lowerarm"/>
  </joint>

  <link
    name="left_hand">
    <visual>
      <origin rpy="-1.5707963268 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_hand.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 3.1415926" xyz="0 0 -0.006"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/left_hand.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="left_wrist_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-1.4992378275" upper="1.4678219" velocity="0.5"/>
    <origin rpy="-1.5707963268 0 0" xyz="0 0.13 0"/>
    <parent link="left_lowerarm"/>
    <child link="left_hand"/>
  </joint>

  <link
    name="right_shoulder">
    <visual>
      <origin rpy="0 1.5707963268 0" xyz="0 0 0.072"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_shoulder.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.074"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_shoulder.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="right_shoulder_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-2.8850292535" upper="2.874557278" velocity="0.5"/>
    <origin rpy="-1.5707963268 0 0" xyz="0 -0.28 0.969"/>
    <parent link="base_link"/>
    <child link="right_shoulder"/>
  </joint>

  <link
    name="right_uparm">
    <visual>
      <origin rpy="0 3.1415926 1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_uparm.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="3.1415926 0 ${3.1415926 + 0.05}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_uparm.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="right_uparm_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-2.6162485487" upper="0.001745329252" velocity="0.5"/>
    <origin rpy="1.5707963268 0 1.5707963268" xyz="0 0 0"/>
    <parent link="right_shoulder"/>
    <child link="right_uparm"/>
  </joint>

  <link
    name="right_elbow">
    <visual>
      <origin rpy="-1.5707963268 0 1.5707963268" xyz="0 0 -0.05"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_elbow.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 ${1.5707963268} 1.5707963268" xyz="0 0 -0.069"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_elbow.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="right_elbow_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-0.53581608036" upper="2.0821777976" velocity="0.5"/>
    <origin rpy="1.5707963268 0 1.5707963268" xyz="0.201 0 0"/>
    <parent link="right_uparm"/>
    <child link="right_elbow"/>
  </joint>

  <link
    name="right_lowerarm">
    <visual>
      <origin rpy="3.1415926 -1.5707963268 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_lowerarm.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 1.5707963268 -1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_lowerarm.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="right_lowerarm_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-1.3177235853" upper="0.07853981634" velocity="0.5"/>
    <origin rpy="1.5707963268 0 0" xyz="0 0 0"/>
    <parent link="right_elbow"/>
    <child link="right_lowerarm"/>
  </joint>

  <link
    name="right_hand">
    <visual>
      <origin rpy="-1.5707963268 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_hand.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 3.1415926" xyz="0 0 -0.006"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/right_hand.stl"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="right_wrist_joint" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-1.4643312424" upper="1.572541656" velocity="0.5"/>
    <origin rpy="-1.5707963268 0 0" xyz="0 0.13 0"/>
    <parent link="right_lowerarm"/>
    <child link="right_hand"/>
  </joint>


<!-- chassis part-->
  <link
    name="left_wheel">
    <visual>
      <origin rpy="0 0 1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/driving_wheel.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963268 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.08482"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="left_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0.210371 0.08482"/>
    <parent link="base_link"/>
    <child link="left_wheel"/>
  </joint>

  <link
    name="right_wheel">
    <visual>
      <origin rpy="0 0 1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/driving_wheel.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963268 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.08482"/>
      </geometry>
    </collision>
    <xacro:default_inertial/>
  </link>

  <joint name="right_wheel_joint" type="continuous">
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.210371 0.08482"/>
    <parent link="base_link"/>
    <child link="right_wheel"/>
  </joint>

  <link
    name="front_wheel">
    <visual>
      <origin rpy="1.5707963268 0 -1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/universal_wheel.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.05982"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
  </link>

  <joint name="front_wheel_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.210371 0 0.08482"/>
    <parent link="base_link"/>
    <child link="front_wheel"/>
  </joint>

  <link
    name="back_wheel">
    <visual>
      <origin rpy="1.5707963268 0 -1.5707963268" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://cruzr_description/meshes/universal_wheel.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.05982"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
  </link>

  <joint name="back_wheel_joint" type="fixed">
    <origin rpy="0 0 0" xyz="-0.210371 0 0.08482"/>
    <parent link="base_link"/>
    <child link="back_wheel"/>
  </joint>


  <link
    name="laser_link">
    <visual>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <geometry>
            <cylinder length="0.02" radius="0.02"/>
        </geometry>
    </visual> 
    <collision>
        <origin rpy="0 0 0" xyz="0 0 0" />
        <geometry>
            <cylinder length="0.02" radius="0.02"/>
        </geometry>
    </collision> 
  </link>

  <joint name="laser_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.3 0 0.19"/>
    <parent link="base_link" />
    <child link="laser_link" />
  </joint>

</robot>
