#VRML_SIM R2025a utf8
# license: Apache License 2.0
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/robotis/turtlebot/protos/TurtleBot3Burger.proto
# keywords: robot/wheeled
# Burger model of the third version of the TurtleBot robot.

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Plastic.proto"
EXTERNPROTO "DiffChassisBase.proto"
EXTERNPROTO "../Sensor/RGBD.proto"
EXTERNPROTO "../Sensor/Lidar2D.proto"
EXTERNPROTO "../Sensor/IMU.proto"

PROTO UtarsChassis [
  field SFVec3f    translation     0 0 0                 # Is `Pose.translation`.
  field SFRotation rotation        0 1 0 0               # Is `Pose.rotation`.
  field SFString   name            "UtarsChassis"    # Is `Solid.name`.
  field SFString   controller      "<generic>"           # Is `Robot.controller`.
  field MFString   controllerArgs  []                    # Is `Robot.controllerArgs`.
  field SFString   customData      ""                    # Is `Robot.customData`.
  field SFBool     supervisor      TRUE                 # Is `Robot.supervisor`.
  field SFBool     synchronization TRUE                  # Is `Robot.synchronization`.
]
{
  DiffChassisBase {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    extensionSlot [
      DEF MESH_SOLID Solid {
        children [
          DEF BODY_MESH Pose {
            translation 0 0 0.1
            rotation 0 0 1 0
            children [
              Shape {
                appearance Plastic {
                }
                geometry Mesh {
                  url "meshes/utars/base_link.STL"
                }
              }

            ]
          }
        ]
        boundingObject Group {
          children [
            DEF BODY_BBOX Pose {
              translation 0 0 0.2
              children [
                Box {
                  size 0.6 0.45 0.3
                }
              ]
            }
          ]
        }
      }
      Lidar2D {
        translation 0.299632 0 0.2
        rotation 0 0 1 1.56702
        name "lidar_front"
      }
      Lidar2D {
        translation -0.2985475 0 0.2
        rotation 0 0 1 -1.5725575
        name "lidar_back"
      }
      RGBD {
        translation 0.25 0 1.093
        rotation 0 1 0 0.699
        name "rgbd_waist"
      }
      RGBD {
        translation 0.25 0 0.208
        rotation 0 1 0 -0.4707
        name "rgbd_bottom"
      }
      IMU
      {
        translation 0.0 0.0 0.1
        name "imu"
      }

    ]
  }
}
