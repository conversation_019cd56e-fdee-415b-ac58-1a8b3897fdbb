#pragma once
#include "sensor_base.hpp"

#include <webots/Camera.hpp>

#include <sensor_msgs/msg/Image.h>
#include <geometry_msgs/msg/TransformStamped.h>

#include "sensor_factory.h"

namespace simulation {
class CameraSensor : public SensorBase
{
public:
    CameraSensor(webots::Robot *robot, int step, const YAML::Node &param);
    bool onInit() override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

private:
    webots::Camera *camera_;

    std::string camera_name_;
    std::string image_topic_name_, link_name_;
    int sensor_freq_ratio_;

    SensorWriterBase::SharedPtr image_writer_;
    geometry_msgs::msg::TransformStamped tf_;
};
}  // namespace simulation
