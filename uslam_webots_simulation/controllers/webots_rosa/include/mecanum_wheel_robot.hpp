#pragma once

#include <thread>

#include <webots/Motor.hpp>
#include <webots/PositionSensor.hpp>
#include <webots/Keyboard.hpp>
#include <webots/Receiver.hpp>

#include <geometry_msgs/msg/PoseStamped.h>
#include <geometry_msgs/msg/TransformStamped.h>
#include <nav_msgs/msg/Odometry.h>
#include <rosa/transform_broadcaster.h>
#include <sensor_msgs/msg/JointState.h>

#include "robot_base.hpp"
#include "mc_task_msgs/action/HeadRotate.h"

namespace simulation {

using HeadRotate = mc_task_msgs::action::HeadRotate;
using GoalHandleHeadControl = rosa::ServerGoalHandle<HeadRotate>;

enum class MOTION_STATE {
    FORWARD = 0,
    BACKWARD = 1,
    LEFT = 2,
    RIGHT = 3,
    LEFT_TURN = 4,
    RIGHT_TURN = 5,
    STOP = 6,
    HEAD_PITCH_UP = 7,
    HEAD_PITCH_DOWN = 8,
    HEAD_YAW_LEFT = 9,
    HEAD_YAW_RIGHT = 10
};

class MecanumWheelRobot : public RobotBase
{
public:
    MecanumWheelRobot(webots::Robot *robot, int step, const YAML::Node &param);
    ~MecanumWheelRobot();

    bool onInit() override;
    void setVelocity(const Eigen::Vector3d &linear, const Eigen::Vector3d &angular) override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

    void keyboard();

private:
    void publishHeadTF();  //  base_link  -> head_pitch_link -> head_yaw_link -> stereo_link
                           //                                                  -> imu
                           //                                                  -> head_front_rgbd
                           //             -> waist_camera
    void publishRealOdom();
    void setHeadVelocity(const double &pitch_goal, const double &yaw_goal, const double &pitch_vel, const double &yaw_vel);

    std::string motionStateToString(MOTION_STATE state);

    // action
    rosa::GoalResponse handleGoal(const rosa::GoalUUID &uuid, std::shared_ptr<const HeadRotate::Goal> goal);
    rosa::CancelResponse handleCancel(const std::shared_ptr<GoalHandleHeadControl> goal_handle);
    void handleAccepted(const std::shared_ptr<GoalHandleHeadControl> goal_handle);
    void execute(const std::shared_ptr<GoalHandleHeadControl> goal_handle);

    webots::Motor *front_right_wheel_motor_, *front_left_wheel_motor_, *back_right_wheel_motor_, *back_left_wheel_motor_;
    webots::Motor *head_pitch_motor_, *head_yaw_motor_;
    webots::PositionSensor *head_pitch_pos_sensor_, *head_yaw_pos_sensor_;
    webots::Keyboard kb_;

    MOTION_STATE state_ = MOTION_STATE::STOP;

    double WHEEL_BASE;
    double WHEEL_RADIUS = 0.05;
    double LX = 0.228;
    double LY = 0.158;

    bool publish_tf_, publish_odom_base_footprint_tf_, publish_map_odom_tf_, publish_pose_, use_real_odom_and_pose_;
    std::string odom_out_topic_, pose_out_topic_;
    rosa::Writer<nav_msgs::msg::Odometry>::SharedPtr odom_writer_;
    rosa::Writer<geometry_msgs::msg::PoseStamped>::SharedPtr pose_writer_;
    rosa::Writer<sensor_msgs::msg::JointState>::SharedPtr head_joint_writer_;

    rosa::ActionServer<HeadRotate>::SharedPtr action_server_;

    std::unique_ptr<rosa::TransformBroadcaster> tb_;
    rosa::Node::SharedPtr node_;
};

}  // namespace simulation
