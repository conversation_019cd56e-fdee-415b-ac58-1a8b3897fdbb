#pragma once
#include "sensor_base.hpp"

#include <webots/RangeFinder.hpp>

#include <sensor_msgs/msg/Image.h>
#include <sensor_msgs/msg/PointCloud2.h>
#include <geometry_msgs/msg/TransformStamped.h>

namespace simulation {
class RangeFinderSensor : public SensorBase
{
public:
    RangeFinderSensor(webots::Robot *robot, int step, const YAML::Node &param);
    bool onInit() override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

private:
    webots::RangeFinder *range_finder_;

    std::string range_finder_name_, topic_name_, link_name_;
    bool enable_depth_points_;
    int sensor_freq_ratio_;

    rosa::Writer<sensor_msgs::msg::Image>::SharedPtr depth_writer_;
    rosa::Writer<sensor_msgs::msg::PointCloud2>::SharedPtr pointcloud_writer_;
    geometry_msgs::msg::TransformStamped tf_;
};
}  // namespace simulation
