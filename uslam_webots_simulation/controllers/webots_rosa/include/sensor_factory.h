#pragma once
#include <memory>
#include <string>

#include <webots/RangeFinder.hpp>
#include <webots/Camera.hpp>

#include <opencv2/opencv.hpp>

#include "cv_bridge/cv_bridge.hpp"

#include "rosa/env.h"
#include "rosa/rosa.h"
#include "rosa/utils/convert_bytes.h"

#include "sensor_msgs/msg/Image.h"
#include "shm_msgs/array_helper.hpp"
#include "shm_msgs/msg/Image1m.h"
#include "shm_msgs/msg/Image2m.h"
#include "shm_msgs/msg/Image4m.h"
#include "shm_msgs/msg/Image512k.h"
#include "shm_msgs/msg/Image8k.h"
#include "shm_msgs/msg/Image8m.h"
#include "shm_msgs/msg/Vec2Image1m.h"
#include "shm_msgs/msg/Vec2Image2m.h"
#include "shm_msgs/msg/Vec2Image4m.h"
#include "shm_msgs/msg/Vec2Image512k.h"
#include "shm_msgs/msg/Vec2Image8k.h"
#include "shm_msgs/msg/Vec2Image8m.h"
#include "std_msgs/msg/Header.h"

#include "utils.hpp"

namespace simulation {
constexpr rosa::Env kZeroCopy("ENABLE_ZERO_COPY", true);
constexpr rosa::Env kIdentFrame("ENABLE_INDENT_FRAME", true);
constexpr rosa::Env kMixFrame("ENABLE_MIX_FRAME", true);
constexpr rosa::Env kMiddleware("MIDDLE_WARE_TYPE", "fastdds");

enum class ImageResolution { RES_8K, RES_512K, RES_1M, RES_2M, RES_4M, RES_8M, RES_UNKNOWN };

static ImageResolution getImageResolution(int32_t image_size)
{
    if (image_size <= rosa::utils::convertToBytes("8KB")) {
        return ImageResolution::RES_8K;
    } else if (image_size <= rosa::utils::convertToBytes("512KB")) {
        return ImageResolution::RES_512K;
    } else if (image_size <= rosa::utils::convertToBytes("1MB")) {
        return ImageResolution::RES_1M;
    } else if (image_size <= rosa::utils::convertToBytes("2MB")) {
        return ImageResolution::RES_2M;
    } else if (image_size <= rosa::utils::convertToBytes("4MB")) {
        return ImageResolution::RES_4M;
    } else if (image_size <= rosa::utils::convertToBytes("8MB")) {
        return ImageResolution::RES_8M;
    } else {
        return ImageResolution::RES_UNKNOWN;
    }
}

class SensorWriterBase
{
public:
    using SharedPtr = std::shared_ptr<SensorWriterBase>;

    virtual ~SensorWriterBase() = default;

    virtual void registerDeadlineMissed(rosa::OnDeadlineMissed &callback) = 0;

    virtual void publish(const cv::Mat &image, const std::string &encoding, const rosa::Time &timestamp,
                         bool is_bigendian, uint32_t step, const std::string &frame_id) = 0;

    virtual void publish(const webots::Camera *camera, const std::string &encoding, const rosa::Time &timestamp,
                         bool is_bigendian, uint32_t step, const std::string &frame_id) = 0;

    virtual void publish(const webots::RangeFinder *range_finder, const std::string &encoding,
                         const rosa::Time &timestamp, bool is_bigendian, uint32_t step, const std::string &frame_id) = 0;
};

template <typename ImageType>
class SensorWriter : public SensorWriterBase
{
public:
    SensorWriter(rosa::Node::SharedPtr node, const std::string &topic_name,
                 const rosa::QoS &sensor_qos = rosa::SensorDataQoS()) :
        middleware_type_(rosa::MiddlewareType::FastDDS)
    {
        if (std::string(kMiddleware.load(rosa::EnvStringHandler::LOWER)) == "iceoryx") {
            middleware_type_ = rosa::MiddlewareType::IceOryx;
        }
        ULOGI_F("MiddleWare type: {}", kMiddleware.load());

        sensor_writer_ = node->createWriter<ImageType>(topic_name, sensor_qos, std::nullopt, middleware_type_);
    }

    ~SensorWriter()
    {
        ULOGI_F("SensorWriter destructor");

        if (sensor_writer_) {
            sensor_writer_.reset();
        }
    }

    void registerDeadlineMissed(rosa::OnDeadlineMissed &callback) override
    {
        sensor_writer_->registerDeadlineMissed(callback);
    }

    void publish(const cv::Mat &image, const std::string &encoding, const rosa::Time &timestamp, bool is_bigendian,
                 uint32_t step, const std::string &frame_id) override
    {
        auto msg = sensor_writer_->borrowLoanedMessage();
        if (!msg) {
            ULOGW_F("borrowLoanedMessage failed");
            return;
        }

        auto cv_image = cv_bridge::CvImage(std_msgs::msg::Header(), encoding, image);
        cv_image.toImageMsg(*msg);

        msg->header.stamp = timestamp;
        msg->is_bigendian = is_bigendian;
        msg->step = step;

        if constexpr (std::is_same<ImageType, sensor_msgs::msg::Image>::value) {
            msg->header.frame_id = frame_id;
        } else {
            shm_msgs::set_str(msg->header.frame_id, frame_id);
        }

        sensor_writer_->write(std::move(msg));
    }

    void publish(const webots::Camera *camera, const std::string &encoding, const rosa::Time &timestamp,
                 bool is_bigendian, uint32_t step, const std::string &frame_id) override
    {
        auto msg = sensor_writer_->borrowLoanedMessage();
        if (!msg) {
            ULOGW_F("borrowLoanedMessage failed");
            return;
        }

        getRgbImageMsg(camera, timestamp, frame_id, is_bigendian, *msg);

        sensor_writer_->write(std::move(msg));
    }

    void publish(const webots::RangeFinder *range_finder, const std::string &encoding, const rosa::Time &timestamp,
                 bool is_bigendian, uint32_t step, const std::string &frame_id) override
    {
        auto msg = sensor_writer_->borrowLoanedMessage();
        if (!msg) {
            ULOGW_F("borrowLoanedMessage failed");
            return;
        }

        getDepthImageMsg(range_finder, timestamp, frame_id, is_bigendian, *msg);

        sensor_writer_->write(std::move(msg));
    }

private:
    typename rosa::Writer<ImageType>::SharedPtr sensor_writer_;
    rosa::MiddlewareType middleware_type_;
};

inline std::shared_ptr<SensorWriterBase> factoryCreateSensorWriter(rosa::Node::SharedPtr node, uint32_t image_szie,
                                                                   const std::string &topic_name,
                                                                   const rosa::QoS &sensor_qos)
{
    if (kZeroCopy.load() == false) {
        ULOGI_F("sensor_msgs::msg::Image SensorWriter");
        return std::make_shared<SensorWriter<sensor_msgs::msg::Image>>(node, topic_name, sensor_qos);
    }

    std::shared_ptr<SensorWriterBase> sensor_writer;

    auto solution = getImageResolution(image_szie);
    switch (solution) {
        case ImageResolution::RES_8K: {
            sensor_writer = std::make_shared<SensorWriter<shm_msgs::msg::Image8k>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Image8k SensorWriter");
        } break;
        case ImageResolution::RES_512K: {
            sensor_writer = std::make_shared<SensorWriter<shm_msgs::msg::Image512k>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Image512k SensorWriter");
        } break;
        case ImageResolution::RES_1M: {
            sensor_writer = std::make_shared<SensorWriter<shm_msgs::msg::Image1m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Image1m SensorWriter");
        } break;
        case ImageResolution::RES_2M: {
            sensor_writer = std::make_shared<SensorWriter<shm_msgs::msg::Image2m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Image2m SensorWriter");
        } break;
        case ImageResolution::RES_4M: {
            sensor_writer = std::make_shared<SensorWriter<shm_msgs::msg::Image4m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Image4m SensorWriter");
        } break;
        case ImageResolution::RES_8M: {
            sensor_writer = std::make_shared<SensorWriter<shm_msgs::msg::Image8m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Image8m SensorWriter");
        } break;
        case ImageResolution::RES_UNKNOWN:
        default: {
            sensor_writer = std::make_shared<SensorWriter<sensor_msgs::msg::Image>>(node, topic_name, sensor_qos);
            ULOGI_F("sensor_msgs::msg::Image SensorWriter");
        }
    }

    return sensor_writer;
}

struct FrameInfo {
    cv::Mat image;
    std::string encoding;
    rosa::Time timestamp;
    std::string frame_id;
    bool is_bigendian;
    uint32_t step;
};

class SensorVecWriterBase
{
public:
    using SharedPtr = std::shared_ptr<SensorVecWriterBase>;

    virtual ~SensorVecWriterBase() = default;

    virtual void registerDeadlineMissed(rosa::OnDeadlineMissed &callback) = 0;

    virtual void publish(const std::vector<FrameInfo> &frame_infos) = 0;
};

template <typename VecImageType>
class SensorVecWriter : public SensorVecWriterBase
{
public:
    SensorVecWriter(rosa::Node::SharedPtr node, const std::string &topic_name,
                    const rosa::QoS &sensor_qos = rosa::SensorDataQoS()) :
        middleware_type_(rosa::MiddlewareType::FastDDS)
    {
        if (std::string(kMiddleware.load(rosa::EnvStringHandler::LOWER)) == "iceoryx") {
            middleware_type_ = rosa::MiddlewareType::IceOryx;
        }
        ULOGI_F("MiddleWare type: {}", kMiddleware.load());
        sensor_writer_ = node->createWriter<VecImageType>(topic_name, sensor_qos, std::nullopt, middleware_type_);
    }

    ~SensorVecWriter()
    {
        ULOGI_F("SensorVecWriter destructor");

        if (sensor_writer_) {
            sensor_writer_.reset();
        }
    }

    void registerDeadlineMissed(rosa::OnDeadlineMissed &callback) override
    {
        sensor_writer_->registerDeadlineMissed(callback);
    }

    void publish(const std::vector<FrameInfo> &frame_infos) override
    {
        ULOGD("pubish mix frame including %d frames", frame_infos.size());
        auto msg = sensor_writer_->borrowLoanedMessage();

        if (!msg) {
            ULOGW_F("borrowLoanedMessage failed");
            return;
        }

        for (size_t index = 0; index < frame_infos.size(); ++index) {
            const auto &frame_info = frame_infos[index];
            ULOGD_F("get frame info encoding:{}, frame_id:{}, step:{}", frame_info.encoding, frame_info.frame_id,
                    frame_info.step);

            auto cv_image = cv_bridge::CvImage(std_msgs::msg::Header(), frame_info.encoding, frame_info.image);

            auto &index_image = msg->images[index];
            cv_image.toImageMsg(index_image);

            index_image.header.stamp = frame_info.timestamp;
            index_image.is_bigendian = frame_info.is_bigendian;
            index_image.step = frame_info.step;

            shm_msgs::set_str(index_image.header.frame_id, frame_info.frame_id);
        }

        shm_msgs::set_str(msg->header.frame_id, frame_infos[0].frame_id);
        msg->header.stamp = frame_infos[0].timestamp;
        // ULOGI_F("Time: {} seconds, {} nanoseconds", msg->header.stamp.sec,
        // msg->header.stamp.nanosec);

        sensor_writer_->write(std::move(msg));
    }

private:
    typename rosa::Writer<VecImageType>::SharedPtr sensor_writer_;
    rosa::MiddlewareType middleware_type_;
};

inline std::shared_ptr<SensorVecWriterBase> factoryCreateSensorVecWriter(rosa::Node::SharedPtr node, uint32_t image_szie,
                                                                         const std::string &topic_name,
                                                                         const rosa::QoS &sensor_qos)
{
    if (kZeroCopy.load() == false) {
        return nullptr;
    }

    std::shared_ptr<SensorVecWriterBase> sensor_writer;

    auto solution = getImageResolution(image_szie);
    switch (solution) {
        case ImageResolution::RES_8K: {
            sensor_writer = std::make_shared<SensorVecWriter<shm_msgs::msg::Vec2Image8k>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Vec2Image8k SensorVecWriter");
        } break;
        case ImageResolution::RES_512K: {
            sensor_writer = std::make_shared<SensorVecWriter<shm_msgs::msg::Vec2Image512k>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Vec2Image512k SensorVecWriter");
        } break;
        case ImageResolution::RES_1M: {
            sensor_writer = std::make_shared<SensorVecWriter<shm_msgs::msg::Vec2Image1m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Vec2Image1m SensorVecWriter");
        } break;
        case ImageResolution::RES_2M: {
            sensor_writer = std::make_shared<SensorVecWriter<shm_msgs::msg::Vec2Image2m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Vec2Image2m SensorVecWriter");
        } break;
        case ImageResolution::RES_4M: {
            sensor_writer = std::make_shared<SensorVecWriter<shm_msgs::msg::Vec2Image4m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Vec2Image4m SensorVecWriter");
        } break;
        case ImageResolution::RES_8M: {
            sensor_writer = std::make_shared<SensorVecWriter<shm_msgs::msg::Vec2Image8m>>(node, topic_name, sensor_qos);
            ULOGI_F("shm_msgs::msg::Vec2Image8m SensorVecWriter");
        } break;
        case ImageResolution::RES_UNKNOWN:
        default: {
            ULOGI_F("Not create SensorVecWriter");
        }
    }

    return sensor_writer;
}
}  // namespace simulation
