#pragma once
#include <string>

#include <webots/Device.hpp>
#include <webots/Robot.hpp>
#include <webots/Supervisor.hpp>

#include <yaml-cpp/yaml.h>
#include <Eigen/Core>

#include <rosa/rosa.h>
#include <rosa/static_transform_broadcaster.h>

namespace simulation {
class SensorBase
{
public:
    SensorBase(webots::Robot *robot, int step, const YAML::Node &param);

    bool init();
    void publishValue(const rosa::Time &time_stamp);
    inline void initStaticTB(const std::shared_ptr<rosa::StaticTransformBroadcaster> &static_tb)
    {
        static_tb_ = static_tb;
    }
    virtual bool isStepReady();
    virtual void createCommunicationInterface(const rosa::Node::SharedPtr &node) = 0;
    virtual void onPublish(const rosa::Time &time_stamp) = 0;

protected:
    virtual bool onInit() = 0;

    webots::Robot *const robot_;
    webots::Supervisor *const super_robot_;
    int sensor_step_;
    YAML::Node param_;

    double last_time_ = 0;
    bool inited_ = false;

    bool publish_tf_;
    std::string parent_frame_id_;
    std::shared_ptr<rosa::StaticTransformBroadcaster> static_tb_;
};
}  // namespace simulation
