#pragma once
#include "sensor_base.hpp"

#include <webots/Accelerometer.hpp>
#include <webots/Gyro.hpp>
#include <webots/InertialUnit.hpp>

#include <sensor_msgs/msg/Imu.h>
#include <geometry_msgs/msg/TransformStamped.h>

namespace simulation {
class IMUSensor : public SensorBase
{
public:
    IMUSensor(webots::Robot *robot, int step, const YAML::Node &param);

    bool onInit() override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

private:
    webots::Accelerometer *accelerometer_;
    webots::Gyro *gyro_;
    webots::InertialUnit *iu_;

    std::string imu_name_;
    std::string link_name_;
    std::string topic_name_;

    rosa::Writer<sensor_msgs::msg::Imu>::SharedPtr writer_;
    geometry_msgs::msg::TransformStamped tf_;
};
}  // namespace simulation
