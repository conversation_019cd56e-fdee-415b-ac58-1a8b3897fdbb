#pragma once

#include "sensor_base.hpp"

#include <webots/Camera.hpp>

#include <sensor_msgs/msg/Image.h>
#include <geometry_msgs/msg/TransformStamped.h>

#include "sensor_factory.h"
namespace simulation {
class StereoCameraSensor : public SensorBase
{
public:
    StereoCameraSensor(webots::Robot *robot, int step, const YAML::Node &param);
    bool onInit() override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

private:
    webots::Camera *left_camera_;
    webots::Camera *right_camera_;

    std::string stereo_name_;
    std::string left_image_topic_name_, right_image_topic_name_, left_link_name_, right_link_name_;
    int sensor_freq_ratio_;

    SensorWriterBase::SharedPtr left_image_writer_;
    SensorWriterBase::SharedPtr right_image_writer_;

    geometry_msgs::msg::TransformStamped left_tf_;
    geometry_msgs::msg::TransformStamped right_tf_;
};
}  // namespace simulation
