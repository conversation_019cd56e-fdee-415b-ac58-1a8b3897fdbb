#pragma once

#include "sensor_base.hpp"
#include "utils.hpp"
#include "sensor_factory.h"
namespace simulation {
class RgbdSensor : public SensorBase
{
public:
    RgbdSensor(webots::Robot *robot, int step, const YAML::Node &param);
    bool onInit() override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

private:
    webots::RangeFinder *range_finder_;
    webots::Camera *camera_;

    std::string rgbd_name_, link_name_;
    bool enable_ident_frame_, enable_mix_frame_, enable_depth_points_;
    int sensor_freq_ratio_;

    cv::Mat rgb_image_, depth_image_;
    SensorWriterBase::SharedPtr rgb_writer_;
    SensorWriterBase::SharedPtr depth_writer_;
    SensorVecWriterBase::SharedPtr mix_writer_;
    rosa::Writer<sensor_msgs::msg::PointCloud2>::SharedPtr pointcloud_writer_;
    rosa::Writer<sensor_msgs::msg::CameraInfo>::SharedPtr depth_info_writer_;
    rosa::Writer<sensor_msgs::msg::CameraInfo>::SharedPtr color_info_writer_;

    geometry_msgs::msg::TransformStamped tf_;
};
}  // namespace simulation
