#pragma once
#include "sensor_base.hpp"

#include <webots/Lidar.hpp>

#include <sensor_msgs/msg/PointCloud2.h>
#include <geometry_msgs/msg/TransformStamped.h>
namespace simulation {
class Lidar3dSensor : public SensorBase
{
public:
    Lidar3dSensor(webots::Robot *robot, int step, const YAML::Node &param);
    bool onInit() override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;

private:
    webots::Lidar *lidar_;

    std::string lidar_name_;
    std::string topic_name_, link_name_;
    int sensor_freq_ratio_;

    double range_min_, range_max_;
    double angle_min_, angle_max_;
    int angle_resolution_;

    rosa::Writer<sensor_msgs::msg::PointCloud2>::SharedPtr pc_writer_;
    geometry_msgs::msg::TransformStamped tf_;
};
}  // namespace simulation
