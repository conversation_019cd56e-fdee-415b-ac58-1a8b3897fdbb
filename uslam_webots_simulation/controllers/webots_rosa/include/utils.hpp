#pragma once
#include <webots/RangeFinder.hpp>
#include <webots/Camera.hpp>
#include <webots/Supervisor.hpp>

#include <Eigen/Core>
#include <Eigen/Dense>

#include <opencv2/opencv.hpp>
#include "cv_bridge/cv_bridge.hpp"

#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>

#include <rosa/rosa.h>
#include <sensor_msgs/msg/Image.h>
#include <sensor_msgs/msg/CameraInfo.h>
#include <sensor_msgs/msg/PointCloud2.h>
#include <geometry_msgs/msg/Quaternion.h>
#include <geometry_msgs/msg/TransformStamped.h>
#include <rosa/static_transform_broadcaster.h>
#include <rosa/tf/LinearMath/Quaternion.h>
#include <rosa/tf2_geometry_msgs.h>

#include <shm_msgs/msg/Image1m.h>
#include <shm_msgs/msg/Image2m.h>
#include <shm_msgs/msg/Image4m.h>
#include <shm_msgs/msg/Image512k.h>
#include <shm_msgs/msg/Image8k.h>
#include <shm_msgs/msg/Image8m.h>
#include <shm_msgs/array_helper.hpp>
namespace simulation {

bool getCvMatFromCamera(const webots::Camera *camera, cv::Mat &image);

bool getCvMatFromRangeFinder(const webots::RangeFinder *range_finder, cv::Mat &image);

template <typename ImageType>
void getRgbImageMsg(const webots::Camera *camera, const rosa::Time &timestamp, const std::string &frame_id,
                    const bool is_bigendian, ImageType &img_msg)
{
    /******************get image message with bgra8 encoding**********************/
    // const unsigned char *image_data = camera->getImage();
    // const auto width = camera->getWidth();
    // const auto height = camera->getHeight();

    // std_msgs::msg::Header header;
    // header.stamp = timestamp;
    // header.frame_id = frame_id;
    // if constexpr (std::is_same<ImageType, sensor_msgs::msg::Image>::value) {
    //     img_msg.header = header;
    //     img_msg.encoding = "bgra8";
    //     img_msg.data.resize(4 * height * width * sizeof(uchar));
    // } else {
    //     shm_msgs::set_header(img_msg.header, header);
    //     shm_msgs::set_str(img_msg.encoding, "bgra8");  // 对应于 CV_8UC4
    // }

    // img_msg.width = width;
    // img_msg.height = height;
    // img_msg.is_bigendian = is_bigendian;
    // img_msg.step = sizeof(uchar) * 4 * img_msg.width;
    // auto size = sizeof(uchar) * 4 * img_msg.height * img_msg.width;

    // memcpy(reinterpret_cast<uchar *>(reinterpret_cast<uchar *>(&img_msg.data[0])), image_data, size);
    /******************get image message with bgra8 encoding**********************/

    cv::Mat cv_mat;
    const bool cv_mat_obtained = getCvMatFromCamera(camera, cv_mat);
    if (!cv_mat_obtained) {
        ULOGE("getCvMatFromCamera failed");
        return;
    }

    try {
        auto cv_image = cv_bridge::CvImage(std_msgs::msg::Header(), "bgr8", cv_mat);
        cv_image.toImageMsg(img_msg);
    } catch (cv_bridge::Exception &e) {
        ULOGE("cv_bridge exception: %s", e.what());
        return;
    }

    img_msg.is_bigendian = is_bigendian;

    std_msgs::msg::Header header;
    header.stamp = timestamp;
    header.frame_id = frame_id;
    if constexpr (std::is_same<ImageType, sensor_msgs::msg::Image>::value) {
        img_msg.header = header;
    } else {
        shm_msgs::set_header(img_msg.header, header);
    }
}

template <typename ImageType>
void getDepthImageMsg(const webots::RangeFinder *range_finder, const rosa::Time &timestamp, const std::string &frame_id,
                      const bool is_bigendian, ImageType &img_msg)
{
    const float *rangeData = range_finder->getRangeImage();
    const size_t height = range_finder->getHeight();
    const size_t width = range_finder->getWidth();

    std_msgs::msg::Header header;
    header.stamp = timestamp;
    header.frame_id = frame_id;

    if constexpr (std::is_same<ImageType, sensor_msgs::msg::Image>::value) {
        img_msg.header = header;
        img_msg.encoding = "mono16";
        img_msg.data.resize(height * width * sizeof(uint16_t));  // 分配数据缓冲区
    } else {
        shm_msgs::set_header(img_msg.header, header);
        shm_msgs::set_str(img_msg.encoding, "mono16");  // 对应于 CV_16UC1
    }

    img_msg.height = height;
    img_msg.width = width;
    img_msg.is_bigendian = false;             // 根据需要设置
    img_msg.step = width * sizeof(uint16_t);  // 每行的字节数

    for (int i = 0; i < height * width; ++i) {
        uint16_t depth_value = static_cast<uint16_t>(rangeData[i] * 1000);
        memcpy(&img_msg.data[i * sizeof(uint16_t)], &depth_value, sizeof(uint16_t));
    }
}

void getPointCloudMsg(const webots::RangeFinder *range_finder, const rosa::Time &timestamp, const std::string &frame_id,
                      sensor_msgs::msg::PointCloud2 &pc_msg);

geometry_msgs::msg::TransformStamped getSensorStaticTF(const webots::Node *sensor_node, const std::string parent_frame_id,
                                                       const std::string &child_frame_id, const bool is_camera = false);

geometry_msgs::msg::TransformStamped getParentTF(const webots::Node *sensor_node, const std::string &link_name);

geometry_msgs::msg::Quaternion createQuaternionMsgFromYaw(double yaw);

std::shared_ptr<sensor_msgs::msg::CameraInfo> getNoDistortionCameraInfo(const webots::RangeFinder *range_finder,
                                                                        const std_msgs::msg::Header &header);
}  // namespace simulation
