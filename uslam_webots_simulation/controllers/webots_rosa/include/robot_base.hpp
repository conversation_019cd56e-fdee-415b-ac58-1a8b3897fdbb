#pragma once
#include <string>

#include <yaml-cpp/yaml.h>
#include <Eigen/Core>

#include <webots/Device.hpp>
#include <webots/Robot.hpp>
#include <webots/Supervisor.hpp>

#include <rosa/rosa.h>
#include <rosa/static_transform_broadcaster.h>

namespace simulation {
class RobotBase
{
public:
    RobotBase(webots::Robot *robot, int step, const YAML::Node &param);

    bool init();
    void publishValue(const rosa::Time &time_stamp);
    virtual bool isStepReady();

    virtual bool onInit() = 0;
    virtual void setVelocity(const Eigen::Vector3d &linear, const Eigen::Vector3d &angular) = 0;
    virtual void createCommunicationInterface(const rosa::Node::SharedPtr &node) = 0;
    virtual void onPublish(const rosa::Time &time_stamp) = 0;
    inline void initStaticTB(const std::shared_ptr<rosa::StaticTransformBroadcaster> &static_tb)
    {
        static_tb_ = static_tb;
    }

protected:
    bool initPose();
    void getRobotPose(Eigen::Matrix3d &R, Eigen::Vector3d &t, Eigen::Vector3d &lv, Eigen::Vector3d &av);

    webots::Robot *const robot_;
    webots::Supervisor *const super_robot_;
    YAML::Node param_;

    int robot_step_;
    double last_time_ = 0;
    bool inited_ = false;

    Eigen::Matrix4d Two_ = Eigen::Matrix4d::Identity();
    std::shared_ptr<rosa::StaticTransformBroadcaster> static_tb_;
};
}  // namespace simulation
