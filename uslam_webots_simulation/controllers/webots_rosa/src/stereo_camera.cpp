#include "stereo_camera.hpp"

#include "utils.hpp"

namespace simulation {
StereoCameraSensor::StereoCameraSensor(webots::Robot *robot, int step, const YAML::Node &param) :
    SensorBase(robot, step, param)
{
}

bool StereoCameraSensor::onInit()
{
    stereo_name_ = param_["stereo_name"].as<std::string>();
    left_image_topic_name_ = param_["left_image_topic_name"].as<std::string>();
    right_image_topic_name_ = param_["right_image_topic_name"].as<std::string>();
    left_link_name_ = param_["left_link_name"].as<std::string>();
    right_link_name_ = param_["right_link_name"].as<std::string>();
    sensor_freq_ratio_ = param_["sensor_freq_ratio"].as<int>();

    std::string left_camera_name = stereo_name_ + "_left";
    std::string right_camera_name = stereo_name_ + "_right";
    left_camera_ = (webots::Camera *)robot_->getCamera(left_camera_name);
    right_camera_ = (webots::Camera *)robot_->getCamera(right_camera_name);
    if (!left_camera_ || !right_camera_) {
        ULOGF("not find left_camera_ %s or right_camera_ %s", left_camera_name.c_str(), right_camera_name.c_str());
        return false;
    }

    sensor_step_ = sensor_step_ * sensor_freq_ratio_;

    left_camera_->enable(sensor_step_);
    right_camera_->enable(sensor_step_);

    // if (camera_->hasRecognition()) {
    //     camera_->recognitionEnable(sensor_step_);
    //     camera_->enableRecognitionSegmentation();
    // }
    // ULOGI("hasRecognition:%d", camera_->hasRecognition());

    if (publish_tf_) {
        auto left_camera_node = super_robot_->getFromDevice(left_camera_);
        if (left_camera_node == nullptr) {
            ULOGF("StereoCameraSensor get left_camera_node failed");
            return false;
        }

        auto right_camera_node = super_robot_->getFromDevice(right_camera_);
        if (right_camera_node == nullptr) {
            ULOGF("StereoCameraSensor get right_camera_node failed");
            return false;
        }

        left_tf_ = getSensorStaticTF(left_camera_node, parent_frame_id_, left_link_name_, false);

        right_tf_ = getSensorStaticTF(right_camera_node, parent_frame_id_, right_link_name_, false);
    }

    ULOGI(
        "StereoCameraSensor::init stereo_name: %s, step: %d, left_image_topic_name: %s, right_image_topic_name: %s, "
        "left_link_name: %s, right_link_name: %s",
        stereo_name_.c_str(), sensor_step_, left_image_topic_name_.c_str(), right_image_topic_name_.c_str(),
        left_link_name_.c_str(), right_link_name_.c_str());
    return true;
}

void StereoCameraSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    const auto rgb_image_size = left_camera_->getWidth() * left_camera_->getHeight() * 3 * sizeof(uchar) * 4;
    left_image_writer_ = factoryCreateSensorWriter(node, rgb_image_size, left_image_topic_name_, rosa::SensorDataQoS());
    right_image_writer_ = factoryCreateSensorWriter(node, rgb_image_size, right_image_topic_name_, rosa::SensorDataQoS());
    ULOGI("create left image publisher with topic name = %s, right image publisher with topic name = %s",
          left_image_topic_name_.c_str(), right_image_topic_name_.c_str());
}

void StereoCameraSensor::onPublish(const rosa::Time &time_stamp)
{
    ULOGD("StereoCameraSensor::onPublish");
    const auto color_frame_step = 3 * left_camera_->getWidth() * sizeof(uchar);
    left_image_writer_->publish(left_camera_, "bgr8", time_stamp, false, color_frame_step, left_link_name_);
    right_image_writer_->publish(right_camera_, "bgr8", time_stamp, false, color_frame_step, right_link_name_);

    // if (camera_->hasRecognition()) {
    //      ULOGI("Recognition size:%d", camera_->getRecognitionNumberOfObjects());
    // }

    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(left_tf_);
        static_tb_->sendTransform(right_tf_);
    }
}
}  // namespace simulation
