#include "imu.hpp"

#include <Eigen/Core>
#include <Eigen/Dense>

#include "utils.hpp"

namespace simulation {
IMUSensor::IMUSensor(webots::Robot *robot, int step, const YAML::Node &param) : SensorBase(robot, step, param) {}

bool IMUSensor::onInit()
{
    imu_name_ = param_["imu_name"].as<std::string>();
    link_name_ = param_["link_name"].as<std::string>();
    topic_name_ = param_["topic_name"].as<std::string>();

    std::string accelerometer_name = imu_name_ + "_accelerometer";
    std::string gyro_name = imu_name_ + "_gyro";
    std::string iu_name = imu_name_ + "_inertialunit";

    accelerometer_ = (webots::Accelerometer *)robot_->getDevice(accelerometer_name);
    if (accelerometer_ == nullptr) return false;
    accelerometer_->enable(sensor_step_);
    gyro_ = (webots::Gyro *)robot_->getDevice(gyro_name);
    if (gyro_ == nullptr) return false;
    gyro_->enable(sensor_step_);
    iu_ = (webots::InertialUnit *)robot_->getDevice(iu_name);
    if (iu_ == nullptr) return false;
    iu_->enable(sensor_step_);

    if (publish_tf_) {
        auto imu_node = super_robot_->getFromDevice(accelerometer_);
        if (imu_node == nullptr) {
            ULOGF("IMUSensor super_robot_->getFromDevice(accelerometer_) failed");
            return false;
        }
        tf_ = getSensorStaticTF(imu_node, parent_frame_id_, link_name_);
    }

    ULOGI("IMUSensor::init step:%d imu_name:%s topic_name:%s link_name:%s\n", sensor_step_, imu_name_.c_str(),
          topic_name_.c_str(), link_name_.c_str());
    return true;
}

void IMUSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    writer_ = node->createWriter<sensor_msgs::msg::Imu>(topic_name_, rosa::SensorDataQoS());
    ULOGI("IMUSensor create publisher with topic name = %s", topic_name_.c_str());
}

void IMUSensor::onPublish(const rosa::Time &time_stamp)
{
    ULOGD("IMUSensor::onPublish");
    const double *acc_value_ptr = accelerometer_->getValues();
    const double *gyro_value_ptr = gyro_->getValues();
    const double *orientation_value_ptr = iu_->getQuaternion();
    sensor_msgs::msg::Imu msg;
    msg.header.stamp = time_stamp;
    msg.header.frame_id = link_name_;
    msg.orientation.x = *orientation_value_ptr;
    msg.orientation.y = *(orientation_value_ptr + 1);
    msg.orientation.z = *(orientation_value_ptr + 2);
    msg.orientation.w = *(orientation_value_ptr + 3);
    msg.linear_acceleration.x = *acc_value_ptr;
    msg.linear_acceleration.y = *(acc_value_ptr + 1);
    msg.linear_acceleration.z = *(acc_value_ptr + 2);
    msg.angular_velocity.x = *gyro_value_ptr;
    msg.angular_velocity.y = *(gyro_value_ptr + 1);
    msg.angular_velocity.z = *(gyro_value_ptr + 2);

    writer_->write(msg);

    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(tf_);
    }
}
}  // namespace simulation
