#include "utils.hpp"

namespace simulation {
bool getCvMatFromCamera(const webots::Camera *camera, cv::Mat &image)
{
    if (camera == nullptr) {
        ULOGE("camera is nullptr");
        return false;
    }

    const unsigned char *image_data = camera->getImage();
    const auto width = camera->getWidth();
    const auto height = camera->getHeight();

    if (image.empty() || image.cols != width || image.rows != height || image.type() != CV_8UC3) {
        image.create(height, width, CV_8UC3);
    }

    cv::Mat bgra_img = cv::Mat(height, width, CV_8UC4);
    memcpy(&bgra_img.data[0], image_data, sizeof(uchar) * 4 * width * height);
    cv::cvtColor(bgra_img, image, cv::COLOR_BGRA2BGR);

    return true;
}

bool getCvMatFromRangeFinder(const webots::RangeFinder *range_finder, cv::Mat &image)
{
    if (range_finder == nullptr) {
        ULOGE("range_finder is nullptr");
        return false;
    }

    const float *range_data = range_finder->getRangeImage();
    const auto width = range_finder->getWidth();
    const auto height = range_finder->getHeight();

    if (image.empty() || image.cols != width || image.rows != height || image.type() != CV_16UC1) {
        image.create(height, width, CV_16UC1);
    }

    for (int i = 0; i < width * height; ++i) {
        image.at<unsigned short>(i) = range_data[i] * 1000;
    }

    return true;
}

void getPointCloudMsg(const webots::RangeFinder *range_finder, const rosa::Time &timestamp, const std::string &frame_id,
                      sensor_msgs::msg::PointCloud2 &pc_msg)
{
    const float *range_data = range_finder->getRangeImage();
    double fx = range_finder->getWidth() / (2.0 * tan(range_finder->getFov() / 2.0));
    double fy = fx * range_finder->getWidth() / range_finder->getHeight();
    double cx = range_finder->getWidth() / 2.0;
    double cy = range_finder->getHeight() / 2.0;

    pcl::PointCloud<pcl::PointXYZ>::Ptr point_cloud(new pcl::PointCloud<pcl::PointXYZ>);
    point_cloud->height = 1;

    for (int col = 0; col < range_finder->getHeight(); ++col) {
        for (int row = 0; row < range_finder->getWidth(); ++row) {
            pcl::PointXYZ point;
            double depth = range_data[col * range_finder->getWidth() + row];
            point.x = depth * (row - cx) / fx;
            point.y = depth * (col - cy) / fx;
            point.z = depth;
            point_cloud->points.emplace_back(point);
        }
    }
    point_cloud->width = point_cloud->points.size();

    pcl::toROSMsg(*point_cloud, pc_msg);
    pc_msg.header.stamp = timestamp;
    pc_msg.header.frame_id = frame_id;
}

geometry_msgs::msg::TransformStamped getSensorStaticTF(const webots::Node *sensor_node, const std::string parent_frame_id,
                                                       const std::string &child_frame_id, const bool is_camera)
{
    const double *position = sensor_node->getField("translation")->getSFVec3f();
    const double *rotation = sensor_node->getField("rotation")->getSFRotation();

    Eigen::Vector3d t(position[0], position[1], position[2]);

    Eigen::AngleAxisd axis_angle(rotation[3], Eigen::Vector3d(rotation[0], rotation[1], rotation[2]));
    Eigen::Quaterniond q(axis_angle);
    q.normalize();

    Eigen::Quaterniond quaternion = q;

    // 对相机传感器执行坐标系转换：xyz前左上 -> xyz右下前
    if (is_camera) {
        const Eigen::Quaterniond q_z(0.5, -0.5, 0.5, -0.5);  // z朝前
        quaternion = q * q_z;
    }

    geometry_msgs::msg::TransformStamped static_tf_msg;

    static_tf_msg.header.frame_id = parent_frame_id;
    static_tf_msg.child_frame_id = child_frame_id;
    static_tf_msg.transform.translation.x = t.x();
    static_tf_msg.transform.translation.y = t.y();
    static_tf_msg.transform.translation.z = t.z();
    static_tf_msg.transform.rotation.x = quaternion.x();
    static_tf_msg.transform.rotation.y = quaternion.y();
    static_tf_msg.transform.rotation.z = quaternion.z();
    static_tf_msg.transform.rotation.w = quaternion.w();
    return static_tf_msg;
}

geometry_msgs::msg::TransformStamped getParentTF(const webots::Node *parent_node, const std::string &link_name)
{
    const double *position = parent_node->getField("translation")->getSFVec3f();
    const double *rotation = parent_node->getField("rotation")->getSFRotation();

    Eigen::Vector3d t(position[0], position[1], position[2]);

    Eigen::AngleAxisd axis_angle(rotation[3], Eigen::Vector3d(rotation[0], rotation[1], rotation[2]));
    Eigen::Quaterniond q(axis_angle);
    q.normalize();

    geometry_msgs::msg::TransformStamped static_tf_msg;
    static_tf_msg.header.frame_id = "sensor_base";
    static_tf_msg.child_frame_id = link_name;
    static_tf_msg.transform.translation.x = t.x();
    static_tf_msg.transform.translation.y = t.y();
    static_tf_msg.transform.translation.z = t.z();
    static_tf_msg.transform.rotation.x = q.x();
    static_tf_msg.transform.rotation.y = q.y();
    static_tf_msg.transform.rotation.z = q.z();
    static_tf_msg.transform.rotation.w = q.w();
    return static_tf_msg;
}

geometry_msgs::msg::Quaternion createQuaternionMsgFromYaw(double yaw)
{
    rosa::tf::Quaternion q;
    q.setRPY(0, 0, yaw);        // 只设置偏航角
    return rosa::tf::toMsg(q);  // 转换为ROS消息类型
}

std::shared_ptr<sensor_msgs::msg::CameraInfo> getNoDistortionCameraInfo(const webots::RangeFinder *range_finder,
                                                                        const std_msgs::msg::Header &header)
{
    auto info = std::make_shared<sensor_msgs::msg::CameraInfo>();
    info->header = header;
    // sensor_msgs::distortion_models::RATIONAL_POLYNOMIAL
    info->distortion_model = "rational_polynomial";
    info->width = range_finder->getWidth();
    info->height = range_finder->getHeight();

    // Assume no distortion
    info->d.resize(8, 0.0);
    // info->d[0] = distortion.k1;
    // info->d[1] = distortion.k2;
    // info->d[2] = distortion.p1;
    // info->d[3] = distortion.p2;
    // info->d[4] = distortion.k3;
    // info->d[5] = distortion.k4;
    // info->d[6] = distortion.k5;
    // info->d[7] = distortion.k6;

    info->k.fill(0.0);
    // k is from orbbec dcw camera
    // info->k[0] = 379.83016967773438;  // fx
    // info->k[2] = 312.26080322265625;  // cx
    // info->k[4] = 379.83016967773438;  // fy
    // info->k[5] = 238.69963073730469;  // cy
    info->k[0] = range_finder->getWidth() / (2.0 * tan(range_finder->getFov() / 2.0));
    info->k[2] = range_finder->getWidth() / 2.0;
    // info->k[4] = info->k[0] * range_finder_->getWidth() / range_finder_->getHeight();
    info->k[4] = info->k[0];
    info->k[5] = range_finder->getHeight() / 2.0;
    info->k[8] = 1.0;

    info->r.fill(0.0);
    info->r[0] = 1;
    info->r[4] = 1;
    info->r[8] = 1;

    info->p.fill(0.0);
    info->p[0] = info->k[0];
    info->p[2] = info->k[2];
    info->p[5] = info->k[4];
    info->p[6] = info->k[5];
    info->p[10] = 1.0;
    return info;
}
}  // namespace simulation
