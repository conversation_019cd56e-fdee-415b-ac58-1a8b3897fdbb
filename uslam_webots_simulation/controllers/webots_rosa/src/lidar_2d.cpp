#include "lidar_2d.hpp"

#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>

#include "utils.hpp"

namespace simulation {
Lidar2dSensor::Lidar2dSensor(webots::Robot *robot, int step, const YAML::Node &param) : SensorBase(robot, step, param)
{
}

bool Lidar2dSensor::onInit()
{
    lidar_name_ = param_["lidar_name"].as<std::string>();
    topic_name_ = param_["topic_name"].as<std::string>();
    link_name_ = param_["link_name"].as<std::string>();
    sensor_freq_ratio_ = param_["sensor_freq_ratio"].as<int>();

    lidar_ = (webots::Lidar *)robot_->getDevice(lidar_name_);

    if (lidar_ == nullptr) {
        ULOGF("not find lidar %s", lidar_name_.c_str());
        return false;
    }

    sensor_step_ = sensor_step_ * sensor_freq_ratio_;
    lidar_->enable(sensor_step_);

    angle_resolution_ = lidar_->getHorizontalResolution();
    range_min_ = lidar_->getMinRange();
    range_max_ = lidar_->getMaxRange();
    angle_min_ = 0;
    angle_max_ = lidar_->getFov();

    if (publish_tf_) {
        auto lidar_node = super_robot_->getFromDevice(lidar_);
        if (lidar_node == nullptr) {
            ULOGF("Lidar2dSensor super_robot_->getFromDevice(lidar_) failed");
            return false;
        }

        tf_ = getSensorStaticTF(lidar_node, parent_frame_id_, link_name_);
    }

    ULOGI("Lidar2dSensor::init lidar_name:%s step:%d topic_name:%s link_name:%s", lidar_name_.c_str(), sensor_step_,
          topic_name_.c_str(), link_name_.c_str());
    ULOGI("resolution: %d, range_min: %f, range_max: %f", angle_resolution_, range_min_, range_max_);
    return true;
}

void Lidar2dSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    scan_writer_ = node->createWriter<sensor_msgs::msg::LaserScan>(topic_name_, rosa::SensorDataQoS());
    ULOGI("Lidar2dSensor create scan publisher with topic name = %s", topic_name_.c_str());
}

void Lidar2dSensor::onPublish(const rosa::Time &time_stamp)
{
    const float *lidar_points = lidar_->getRangeImage();

    sensor_msgs::msg::LaserScan scan_msg;
    scan_msg.header.stamp = time_stamp;
    scan_msg.header.frame_id = link_name_;
    scan_msg.angle_min = angle_min_;
    scan_msg.angle_max = angle_max_;
    scan_msg.angle_increment = (angle_max_ - angle_min_) / angle_resolution_;
    scan_msg.range_min = range_min_;
    scan_msg.range_max = range_max_;
    scan_msg.ranges.resize(angle_resolution_);

    for (unsigned int i = 0; i < angle_resolution_; ++i) {
        scan_msg.ranges[i] = lidar_points[angle_resolution_ - i];
    }
    scan_writer_->write(scan_msg);

    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(tf_);
    }
}

}  // namespace simulation
