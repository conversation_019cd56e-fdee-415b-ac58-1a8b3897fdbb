#include "range_finder.hpp"

#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>

#include "utils.hpp"

namespace simulation {

RangeFinderSensor::RangeFinderSensor(webots::Robot *robot, int step, const YAML::Node &param) :
    SensorBase(robot, step, param)
{
}

bool RangeFinderSensor::onInit()
{
    range_finder_name_ = param_["range_finder_name"].as<std::string>();
    topic_name_ = param_["topic_name"].as<std::string>();
    enable_depth_points_ = param_["enable_depth_points"].as<bool>();
    link_name_ = param_["link_name"].as<std::string>();
    sensor_freq_ratio_ = param_["sensor_freq_ratio"].as<int>();

    range_finder_ = (webots::RangeFinder *)robot_->getRangeFinder(range_finder_name_);

    if (range_finder_ == nullptr) {
        ULOGF("not find range_finder %s ", range_finder_name_.c_str());
        return false;
    }

    sensor_step_ = sensor_step_ * sensor_freq_ratio_;
    range_finder_->enable(sensor_step_);

    if (publish_tf_) {
        auto range_finder_node = super_robot_->getFromDevice(range_finder_);
        if (range_finder_node == nullptr) {
            ULOGF("RgbdSensor super_robot_->getFromDevice(range_finder_node) failed");
            return false;
        }

        tf_ = getSensorStaticTF(range_finder_node, parent_frame_id_, link_name_, true);
    }

    ULOGI("RangeFinderSensor::init step:%d topic_name:%s  link_name:%s", sensor_step_, topic_name_.c_str(),
          link_name_.c_str());
    return true;
}

void RangeFinderSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    depth_writer_ = node->createWriter<sensor_msgs::msg::Image>(topic_name_, rosa::SensorDataQoS());
    ULOGI("RangeFinderSensor create depth image writer with topic name = %s", topic_name_.c_str());
    if (enable_depth_points_) {
        pointcloud_writer_ =
            node->createWriter<sensor_msgs::msg::PointCloud2>(topic_name_ + "_points", rosa::SensorDataQoS());
        ULOGI("RangeFinderSensor create pointcloud writer with topic name = %s",
              std::string(topic_name_ + "_points").c_str());
    }
}

void RangeFinderSensor::onPublish(const rosa::Time &time_stamp)
{
    sensor_msgs::msg::Image image_msg;
    getDepthImageMsg(this->range_finder_, time_stamp, link_name_, false, image_msg);
    depth_writer_->write(image_msg);

    if (enable_depth_points_) {
        sensor_msgs::msg::PointCloud2 pc_msg;
        getPointCloudMsg(this->range_finder_, time_stamp, link_name_, pc_msg);
        pointcloud_writer_->write(pc_msg);
    }

    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(tf_);
    }
}

}  // namespace simulation
