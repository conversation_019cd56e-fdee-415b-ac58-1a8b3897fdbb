#include "camera.hpp"

#include "utils.hpp"

namespace simulation {
CameraSensor::CameraSensor(webots::Robot *robot, int step, const YAML::Node &param) : SensorBase(robot, step, param) {}

bool CameraSensor::onInit()
{
    camera_name_ = param_["camera_name"].as<std::string>();
    image_topic_name_ = param_["image_topic_name"].as<std::string>();
    link_name_ = param_["link_name"].as<std::string>();
    sensor_freq_ratio_ = param_["sensor_freq_ratio"].as<int>();

    camera_ = (webots::Camera *)robot_->getCamera(camera_name_);
    if (camera_ == nullptr) {
        ULOGF("not find camera %s", camera_name_.c_str());
        return false;
    }

    sensor_step_ = sensor_step_ * sensor_freq_ratio_;

    camera_->enable(sensor_step_);

    // if (camera_->hasRecognition()) {
    //     camera_->recognitionEnable(sensor_step_);
    //     camera_->enableRecognitionSegmentation();
    // }

    // ULOGI("hasRecognition:%d", camera_->hasRecognition());

    if (publish_tf_) {
        // 在Webots中，摄像头对象（如 Camera*）仅提供视觉数据功能（如图像），
        // 其物理位姿需通过关联的场景树节点（Node*）获取。
        // Supervisor机器人因全局权限可直接调用 getFromDevice() 获取该节点，
        // 而普通机器人需依赖预设的安装位置间接计算位姿。
        auto camera_node = super_robot_->getFromDevice(camera_);
        if (camera_node == nullptr) {
            ULOGE("CameraSensor super_robot_->getFromDevice(camera_) failed");
            return false;
        }

        tf_ = getSensorStaticTF(camera_node, parent_frame_id_, link_name_, true);
    }

    ULOGI("CameraSensor::init camera_name: %s, step: %d, image_topic_name: %s, link_name: %s", camera_name_.c_str(),
          sensor_step_, image_topic_name_.c_str(), link_name_.c_str());

    return true;
}

void CameraSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    const auto rgb_image_size = camera_->getWidth() * camera_->getHeight() * 3 * sizeof(uchar) * 4;
    image_writer_ = factoryCreateSensorWriter(node, rgb_image_size, image_topic_name_, rosa::SensorDataQoS());
    ULOGI("CameraSensor create image publisher with topic name = %s", image_topic_name_.c_str());
}

void CameraSensor::onPublish(const rosa::Time &time_stamp)
{
    ULOGD("CameraSensor::onPublish");
    const auto color_frame_step = 3 * camera_->getWidth() * sizeof(uchar);
    image_writer_->publish(camera_, "bgr8", time_stamp, false, color_frame_step, link_name_);

    // if (camera_->hasRecognition()) {
    //     ULOGI(node_->get_logger(), "Recognition size:%d", camera_->getRecognitionNumberOfObjects());
    // }

    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(tf_);
    }
}
}  // namespace simulation
