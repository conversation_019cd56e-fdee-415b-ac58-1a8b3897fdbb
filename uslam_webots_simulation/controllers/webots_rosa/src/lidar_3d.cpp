#include "lidar_3d.hpp"

#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>

#include "utils.hpp"

struct PointXYZIRT {
    PCL_ADD_POINT4D
    PCL_ADD_INTENSITY;
    uint16_t ring;
    double time;
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;
POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZIRT, (float, x, x)(float, y, y)(float, z, z)(float, intensity, intensity)(
                                                   uint16_t, ring, ring)(double, time, time))

namespace simulation {

Lidar3dSensor::Lidar3dSensor(webots::Robot *robot, int step, const YAML::Node &param) : SensorBase(robot, step, param)
{
}

bool Lidar3dSensor::onInit()
{
    lidar_name_ = param_["lidar_name"].as<std::string>();
    topic_name_ = param_["topic_name"].as<std::string>();
    link_name_ = param_["link_name"].as<std::string>();
    sensor_freq_ratio_ = param_["sensor_freq_ratio"].as<int>();

    lidar_ = (webots::Lidar *)robot_->getDevice(lidar_name_);

    if (lidar_ == nullptr) {
        ULOGF("not find lidar %s", lidar_name_.c_str());
        return false;
    }

    sensor_step_ = sensor_step_ * sensor_freq_ratio_;
    lidar_->enable(sensor_step_);
    lidar_->enablePointCloud();

    angle_resolution_ = lidar_->getHorizontalResolution();
    range_min_ = lidar_->getMinRange();
    range_max_ = lidar_->getMaxRange();
    angle_min_ = 0;
    angle_max_ = lidar_->getFov();

    if (publish_tf_) {
        auto lidar_node = super_robot_->getFromDevice(lidar_);
        if (lidar_node == nullptr) {
            ULOGF("Lidar3dSensor super_robot_->getFromDevice(lidar_) failed");
            return false;
        }

        tf_ = getSensorStaticTF(lidar_node, parent_frame_id_, link_name_);
    }

    ULOGI("Lidar3dSensor::init lidar_name:%s step:%d topic_name:%s link_name:%s", lidar_name_.c_str(), sensor_step_,
          topic_name_.c_str(), link_name_.c_str());
    ULOGI("resolution: %d, range_min: %f, range_max:%f", angle_resolution_, range_min_, range_max_);
    return true;
}

void Lidar3dSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    pc_writer_ = node->createWriter<sensor_msgs::msg::PointCloud2>(topic_name_, rosa::SensorDataQoS());
    ULOGI("Lidar3dSensor create publisher with topic name = %s", topic_name_.c_str());
}

void Lidar3dSensor::onPublish(const rosa::Time &time_stamp)
{
    const webots::LidarPoint *points_ptr = lidar_->getPointCloud();

    pcl::PointCloud<PointXYZIRT>::Ptr point_cloud(new pcl::PointCloud<PointXYZIRT>);
    point_cloud->header.frame_id = link_name_;
    point_cloud->height = 1;
    point_cloud->header.stamp = static_cast<uint64_t>(points_ptr->time * 1e6);
    int points_cnt = 0;
    while (points_cnt < lidar_->getNumberOfPoints()) {
        PointXYZIRT point;
        if (std::isinf(points_ptr->x) || std::isinf(points_ptr->y) || std::isinf(points_ptr->z)) {
            points_ptr++;
            points_cnt++;
            continue;
        }

        point.x = points_ptr->x;
        point.y = points_ptr->y;
        point.z = points_ptr->z;
        point.intensity = 100.0;
        point.ring = points_ptr->layer_id;
        point.time = 0.0;
        point_cloud->points.push_back(point);
        points_ptr++;
        points_cnt++;
    }
    point_cloud->width = point_cloud->points.size();

    sensor_msgs::msg::PointCloud2 pc_msg;
    pcl::toROSMsg(*point_cloud, pc_msg);
    pc_writer_->write(pc_msg);

    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(tf_);
    }
}

}  // namespace simulation
