#include "rgbd.hpp"

namespace simulation {
RgbdSensor::RgbdSensor(webots::Robot *robot, int step, const YAML::Node &param) : SensorBase(robot, step, param) {}

bool RgbdSensor::onInit()
{
    rgbd_name_ = param_["rgbd_name"].as<std::string>();
    enable_ident_frame_ = param_["enable_ident_frame"].as<bool>();
    enable_mix_frame_ = param_["enable_mix_frame"].as<bool>();
    enable_depth_points_ = param_["enable_depth_points"].as<bool>();
    link_name_ = param_["link_name"].as<std::string>();
    sensor_freq_ratio_ = param_["sensor_freq_ratio"].as<int>();

    std::string depth_name = rgbd_name_ + "_depth";
    std::string rgb_name = rgbd_name_ + "_rgb";
    range_finder_ = (webots::RangeFinder *)robot_->getRangeFinder(depth_name);
    camera_ = (webots::Camera *)robot_->getCamera(rgb_name);
    if (!range_finder_ || !camera_) {
        ULOGF("not find range_finder %s or camera %s", depth_name.c_str(), rgb_name.c_str());
        return false;
    }

    sensor_step_ = sensor_step_ * sensor_freq_ratio_;
    range_finder_->enable(sensor_step_);
    camera_->enable(sensor_step_);
    // if (camera_->hasRecognition()) {
    //     camera_->recognitionEnable(sensor_step_);
    //     camera_->enableRecognitionSegmentation();
    // }
    // ULOGI("hasRecognition:%d", camera_->hasRecognition());

    rgb_image_ = cv::Mat(camera_->getHeight(), camera_->getWidth(), CV_8UC4, cv::Scalar(0, 0, 0));
    depth_image_ = cv::Mat(range_finder_->getHeight(), range_finder_->getWidth(), CV_16UC1, cv::Scalar(0, 0, 0));

    if (publish_tf_) {
        auto camera_node = super_robot_->getFromDevice(camera_);
        if (camera_node == nullptr) {
            ULOGF("RgbdSensor super_robot_->getFromDevice(camera_) failed");
            return false;
        }

        tf_ = getSensorStaticTF(camera_node, parent_frame_id_, link_name_, true);
    }

    ULOGI("RgbdSensor::init rgbd_name:%s step:%d link_name:%s", rgbd_name_.c_str(), sensor_step_, link_name_.c_str());
    return true;
}

void RgbdSensor::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    ULOGF_IF(camera_ == nullptr, "camera_ is nullptr");
    ULOGF_IF(range_finder_ == nullptr, "range_finder_ is nullptr");

    if (enable_ident_frame_) {
        const auto rgb_image_size = camera_->getWidth() * camera_->getHeight() * 3 * sizeof(uchar);
        const auto depth_image_size = range_finder_->getWidth() * range_finder_->getHeight() * sizeof(uint16_t);
        const std::string rgb_topic = "/sensor/camera/" + rgbd_name_ + "/color/raw";
        const std::string depth_topic = "/sensor/camera/" + rgbd_name_ + "/depth/raw";

        rgb_writer_ = factoryCreateSensorWriter(node, rgb_image_size, rgb_topic, rosa::SensorDataQoS());
        ULOGI("RgbdSensor create rgb image publisher with topic name = %s", rgb_topic.c_str());

        depth_writer_ = factoryCreateSensorWriter(node, depth_image_size, depth_topic, rosa::SensorDataQoS());
        ULOGI("RgbdSensor create depth image publisher with topic name = %s", depth_topic.c_str());
    }

    if (enable_mix_frame_) {
        const auto mix_image_size = camera_->getWidth() * camera_->getHeight() * 3 * sizeof(uchar);
        const std::string mix_topic = "/sensor/camera/" + rgbd_name_ + "/mix/raw";
        mix_writer_ = factoryCreateSensorVecWriter(node, mix_image_size, mix_topic, rosa::SensorDataQoS());
        ULOGI("RgbdSensor create mix image publisher with topic name = %s", mix_topic.c_str());
    }

    if (enable_depth_points_) {
        const std::string depth_point_topic = "/sensor/camera/" + rgbd_name_ + "/depth/points";
        pointcloud_writer_ = node->createWriter<sensor_msgs::msg::PointCloud2>(depth_point_topic, rosa::SensorDataQoS());
        ULOGI("RgbdSensor create pointcloud publisher with topic name = %s", depth_point_topic.c_str());
    }

    const std::string depth_info_topic = "/sensor/camera/" + rgbd_name_ + "/depth/info";
    depth_info_writer_ = node->createWriter<sensor_msgs::msg::CameraInfo>(depth_info_topic, rosa::ConfigQoS());
    ULOGI("RgbdSensor create depth camera info publisher with topic name = %s", depth_info_topic.c_str());

    const std::string color_info_topic = "/sensor/camera/" + rgbd_name_ + "/color/info";
    color_info_writer_ = node->createWriter<sensor_msgs::msg::CameraInfo>(color_info_topic, rosa::ConfigQoS());
    ULOGI("RgbdSensor create color camera info publisher with topic name = %s", color_info_topic.c_str());
}

void RgbdSensor::onPublish(const rosa::Time &time_stamp)
{
    ULOGD("RgbdSensor::onPublish");

    ULOGF_IF(camera_ == nullptr, "camera_ is nullptr");
    ULOGF_IF(range_finder_ == nullptr, "range_finder_ is nullptr");

    // if (camera_->hasRecognition()) {
    //     ULOGI("Recognition size: %d", camera_->getRecognitionNumberOfObjects());
    // }

    const std::string color_optical_frame_id = rgbd_name_ + "_color_optical_frame";
    const std::string depth_optical_frame_id = rgbd_name_ + "_depth_optical_frame";
    const auto color_frame_step = 3 * camera_->getWidth() * sizeof(uchar);
    const auto depth_frame_step = range_finder_->getWidth() * sizeof(uint16_t);
    const bool color_frame_obtained = getCvMatFromCamera(camera_, rgb_image_);
    const bool depth_frame_obtained = getCvMatFromRangeFinder(range_finder_, depth_image_);

    if (enable_ident_frame_) {
        ULOGD("publish independent frame");

        if (depth_frame_obtained) {
            depth_writer_->publish(depth_image_, "mono16", time_stamp, false, depth_frame_step, link_name_);
        } else {
            ULOGE("getCvMatFromRangeFinder failed");
        }

        if (color_frame_obtained) {
            rgb_writer_->publish(rgb_image_, "bgr8", time_stamp, false, color_frame_step, link_name_);
        } else {
            ULOGE("getCvMatFromCamera failed");
        }
    }

    if (enable_mix_frame_ && kZeroCopy.load()) {
        ULOGD("publish mix frame");
        std::vector<FrameInfo> frame_infos;

        if (depth_frame_obtained) {
            FrameInfo depth_frame_info;
            depth_frame_info.timestamp = time_stamp;
            depth_frame_info.frame_id = link_name_;
            depth_frame_info.encoding = "mono16";
            depth_frame_info.image = depth_image_;
            depth_frame_info.is_bigendian = false;
            depth_frame_info.step = depth_frame_step;
            frame_infos.push_back(depth_frame_info);
        } else {
            ULOGE("getCvMatFromRangeFinder failed");
        }

        if (color_frame_obtained) {
            FrameInfo rgb_frame_info;
            rgb_frame_info.timestamp = time_stamp;
            rgb_frame_info.frame_id = link_name_;
            rgb_frame_info.encoding = "bgr8";
            rgb_frame_info.image = rgb_image_;
            rgb_frame_info.is_bigendian = false;
            rgb_frame_info.step = color_frame_step;
            frame_infos.push_back(rgb_frame_info);
        } else {
            ULOGE("getCvMatFromCamera failed");
        }

        if (color_frame_obtained && depth_frame_obtained) {
            mix_writer_->publish(frame_infos);
        }
    }

    if (enable_depth_points_) {
        sensor_msgs::msg::PointCloud2 pc_msg;
        getPointCloudMsg(this->range_finder_, time_stamp, depth_optical_frame_id, pc_msg);
        pointcloud_writer_->write(pc_msg);
    }

    std_msgs::msg::Header header;
    header.stamp = time_stamp;
    header.frame_id = link_name_;
    auto camera_info_msg = getNoDistortionCameraInfo(this->range_finder_, header);
    depth_info_writer_->write(*camera_info_msg);
    color_info_writer_->write(*camera_info_msg);
    if (publish_tf_ && static_tb_ != nullptr) {
        static_tb_->sendTransform(tf_);
    }
}

}  // namespace simulation
