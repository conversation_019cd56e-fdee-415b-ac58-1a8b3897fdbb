#include "robot_base.hpp"

using namespace webots;

namespace simulation {
RobotBase::RobotBase(webots::Robot *robot, int step, const YAML::Node &param) :
    robot_(robot), robot_step_(step), param_(param), super_robot_((webots::Supervisor *const)robot)
{
}

bool RobotBase::init()
{
    if (onInit() && initPose()) inited_ = true;
    return inited_;
}

bool RobotBase::initPose()
{
    if (super_robot_ == nullptr) {
        ULOGF("super_robot_ is nullptr");
        return false;
    }

    const double *position = super_robot_->getSelf()->getPosition();
    const double *orientation = super_robot_->getSelf()->getOrientation();

    Eigen::Matrix3d R;
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            R(i, j) = orientation[i * 3 + j];
        }
    }

    Eigen::Vector3d t;
    t.x() = position[0];
    t.y() = position[1];
    t.z() = position[2];

    Two_.block<3, 3>(0, 0) = R;
    Two_.block<3, 1>(0, 3) = t;

    return true;
}

void RobotBase::getRobotPose(Eigen::Matrix3d &R, Eigen::Vector3d &t, Eigen::Vector3d &lv, Eigen::Vector3d &av)
{
    //获取实时状态下的位姿，应该是base相对于world

    const double *position = super_robot_->getSelf()->getPosition();
    const double *orientation = super_robot_->getSelf()->getOrientation();
    const double *velocity = super_robot_->getSelf()->getVelocity();

    t.x() = position[0];
    t.y() = position[1];
    t.z() = position[2];

    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            R(i, j) = orientation[i * 3 + j];
        }
    }

    lv.x() = velocity[0];
    lv.y() = velocity[1];
    lv.z() = velocity[2];
    av.x() = velocity[3];
    av.y() = velocity[4];
    av.z() = velocity[5];
}

void RobotBase::publishValue(const rosa::Time &time_stamp)
{
    if (!isStepReady()) return;
    onPublish(time_stamp);
}

bool RobotBase::isStepReady()
{
    //确保仿真每过一个time step才发布一次数据

    if (!inited_) {
        ULOGE("robot not inited");
        return false;
    }

    if (last_time_ == 0) {
        last_time_ = robot_->getTime();
        return true;
    }
    if ((robot_->getTime() - last_time_) * 1000.0 > robot_step_ * 1.0 - 1e-6) {
        last_time_ = robot_->getTime();
        return true;
    } else {
        return false;
    }
}
}  // namespace simulation
