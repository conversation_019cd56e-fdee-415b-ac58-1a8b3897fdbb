#include "mecanum_wheel_robot.hpp"
#include <fstream>
#include <Eigen/Dense>

namespace simulation {

MecanumWheelRobot::MecanumWheelRobot(webots::Robot *robot, int step, const YAML::Node &param) :
    RobotBase(robot, step, param)
{
}

MecanumWheelRobot::~MecanumWheelRobot() {}

bool MecanumWheelRobot::onInit()
{  // read the config file
    // 注意base_footprint 和 base_link的区别，新版本将后者改为了前者
    odom_out_topic_ = param_["odom_out_topic"].as<std::string>();
    pose_out_topic_ = param_["pose_out_topic"].as<std::string>();
    publish_tf_ = param_["publish_tf"].as<bool>();
    publish_odom_base_footprint_tf_ = param_["publish_odom_base_footprint_tf"].as<bool>();
    publish_map_odom_tf_ = param_["publish_map_odom_tf"].as<bool>();
    publish_pose_ = param_["publish_pose"].as<bool>();
    use_real_odom_and_pose_ = param_["use_real_odom_and_pose"].as<bool>();

    // keyborad input enable
    kb_.enable(robot_->getBasicTimeStep());

    // 4 wheels 地盘
    front_right_wheel_motor_ = robot_->getMotor("wheel1");
    front_left_wheel_motor_ = robot_->getMotor("wheel2");
    back_right_wheel_motor_ = robot_->getMotor("wheel3");
    back_left_wheel_motor_ = robot_->getMotor("wheel4");

    // 2 head motors/sensors 头部
    head_pitch_motor_ = robot_->getMotor("head_pitch_joint");
    head_yaw_motor_ = robot_->getMotor("head_yaw_joint");
    head_pitch_pos_sensor_ = head_pitch_motor_->getPositionSensor();
    head_yaw_pos_sensor_ = head_yaw_motor_->getPositionSensor();

    // 并且要根据频率来enable sensors
    head_pitch_pos_sensor_->enable(robot_->getBasicTimeStep());
    head_yaw_pos_sensor_->enable(robot_->getBasicTimeStep());

    ULOGI_S() << "getSupervisor() = " << super_robot_->getSupervisor();
    ULOGI_S() << "getName() = " << super_robot_->getName();

    return true;
}

void MecanumWheelRobot::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    odom_writer_ = node->createWriter<nav_msgs::msg::Odometry>(odom_out_topic_, rosa::QoS(10));
    if (publish_pose_) {
        pose_writer_ = node->createWriter<geometry_msgs::msg::PoseStamped>(pose_out_topic_, rosa::QoS(10));
    }

    head_joint_writer_ = node->createWriter<sensor_msgs::msg::JointState>("/mc/head/joint_states", rosa::QoS(10));

    tb_ = std::make_unique<rosa::TransformBroadcaster>(*node);

    // 模拟控制头部接口
    action_server_ = node->createActionServer<HeadRotate>(
        "/mc/head/rotate", std::bind(&MecanumWheelRobot::handleGoal, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&MecanumWheelRobot::handleCancel, this, std::placeholders::_1),
        std::bind(&MecanumWheelRobot::handleAccepted, this, std::placeholders::_1));
}

// TODO: action 发布订阅设置头部旋转
rosa::GoalResponse MecanumWheelRobot::handleGoal(const rosa::GoalUUID &uuid, std::shared_ptr<const HeadRotate::Goal> goal)
{
    ULOGI("Received goal request: pitch_goal=%.2f, pitch_vel=%.2f, yaw_goal=%.2f, yaw_vel=%.2f", goal->pitch,
          goal->pitch_vel, goal->yaw, goal->yaw_vel);
    return rosa::GoalResponse::ACCEPT_AND_EXECUTE;
}

rosa::CancelResponse MecanumWheelRobot::handleCancel(const std::shared_ptr<GoalHandleHeadControl> goal_handle)
{
    ULOGI("Received request to cancel goal");
    return rosa::CancelResponse::ACCEPT;
}

void MecanumWheelRobot::handleAccepted(const std::shared_ptr<GoalHandleHeadControl> goal_handle)
{
    std::thread{std::bind(&MecanumWheelRobot::execute, this, goal_handle)}.detach();
}

void MecanumWheelRobot::execute(const std::shared_ptr<GoalHandleHeadControl> goal_handle)
{
    const auto goal = goal_handle->getGoal();
    auto feedback = std::make_shared<HeadRotate::Feedback>();
    auto result = std::make_shared<HeadRotate::Result>();

    double current_pitch = 0.0;     // 当前的 pitch 位置
    double current_yaw = 0.0;       // 当前的 yaw 位置
    const double tolerance = 0.01;  // 位置容忍度

    rosa::Rate rate(10);
    while (rosa::ok()) {
        setHeadVelocity(goal->pitch, goal->yaw, goal->pitch_vel, goal->yaw_vel);

        current_pitch = head_pitch_pos_sensor_->getValue();
        current_yaw = head_yaw_pos_sensor_->getValue();

        feedback->feedback.desc = "Head Running";
        goal_handle->publishFeedback(feedback);
        ULOGI("Feedback: pitch=%.2f, yaw=%.2f", current_pitch, current_yaw);

        if (std::abs(current_pitch - goal->pitch) < tolerance && std::abs(current_yaw - goal->yaw) < tolerance) {
            result->result.desc = "Head control succeeded.";
            goal_handle->succeed(result);
            ULOGI("Head control succeeded.");
            return;
        }

        if (goal_handle->isCanceling()) {
            result->result.desc = "Goal canceled.";
            goal_handle->canceled(result);
            ULOGI("Goal canceled.");
            return;
        }
        rate.sleep();
    }
}

//

void MecanumWheelRobot::onPublish(const rosa::Time &time_stamp)
{
    //每一个仿真step发布一次相关信息

    publishRealOdom();
    publishHeadTF();
    keyboard();
}

void MecanumWheelRobot::setVelocity(const Eigen::Vector3d &linear, const Eigen::Vector3d &angular)
{
    //将速度命令转化为麦克纳姆论的速度命令，并控制motor

    double speeds[4];
    speeds[0] = 1 / WHEEL_RADIUS * (linear.x() + linear.y() + (LX + LY) * angular.z());
    speeds[1] = 1 / WHEEL_RADIUS * (linear.x() - linear.y() - (LX + LY) * angular.z());
    speeds[2] = 1 / WHEEL_RADIUS * (linear.x() - linear.y() + (LX + LY) * angular.z());
    speeds[3] = 1 / WHEEL_RADIUS * (linear.x() + linear.y() - (LX + LY) * angular.z());
    front_right_wheel_motor_->setPosition(INFINITY);
    front_right_wheel_motor_->setVelocity(speeds[0]);
    front_left_wheel_motor_->setPosition(INFINITY);
    front_left_wheel_motor_->setVelocity(speeds[1]);
    back_right_wheel_motor_->setPosition(INFINITY);
    back_right_wheel_motor_->setVelocity(speeds[2]);
    back_left_wheel_motor_->setPosition(INFINITY);
    back_left_wheel_motor_->setVelocity(speeds[3]);

    ULOGI("- Speeds: vx=%.2f[m/s] vy=%.2f[m/s] ω=%.2f[rad/s]\n", linear.x(), linear.y(), angular.z());
}

void MecanumWheelRobot::setHeadVelocity(const double &pitch_goal, const double &yaw_goal, const double &pitch_vel,
                                        const double &yaw_vel)
{
    //控制头部的位姿

    head_pitch_motor_->setPosition(pitch_goal);
    head_pitch_motor_->setVelocity(pitch_vel);
    head_yaw_motor_->setPosition(yaw_goal);
    head_yaw_motor_->setVelocity(yaw_vel);

    double pitch_pos = head_pitch_pos_sensor_->getValue();
    double yaw_pos = head_yaw_pos_sensor_->getValue();

    ULOGI("- Head goal: pitch_goal=%.2f, yaw_goal=%.2f\n", pitch_goal, yaw_goal);
    ULOGI("- Head Pose: pitch_pos=%.2f, yaw_pose=%.2f\n", pitch_pos, yaw_pos);
}

void MecanumWheelRobot::publishRealOdom()
{
    Eigen::Vector3d t, lv, av;
    Eigen::Matrix3d R;

    // 获取实时的 real的pose信息，速度和角速度，base相对于世界
    getRobotPose(R, t, lv, av);

    // base相对于odom的位姿？ 体现相对于初始位置变化了多少？
    nav_msgs::msg::Odometry odom_msg;
    odom_msg.header.frame_id = "odom";
    odom_msg.header.stamp = rosa::Time::now();

    std::cout << "odom time = " << odom_msg.header.stamp.sec << " s" << std::endl;

    odom_msg.child_frame_id = "base_link";

    Eigen::Matrix4d Twb = Eigen::Matrix4d::Identity();  // base相对于world，wolrd to base
    Twb(0, 3) = t(0);
    Twb(1, 3) = t(1);
    Twb(2, 3) = t(2);
    Twb.block<3, 3>(0, 0) = R;

    Eigen::Matrix4d Tob = Two_.inverse().eval() * Twb;  // baselink相对于初始的位姿（无偏移的odom的位姿？）
    odom_msg.pose.pose.position.x = Tob(0, 3);
    odom_msg.pose.pose.position.y = Tob(1, 3);
    odom_msg.pose.pose.position.z = Tob(2, 3);
    Eigen::Quaterniond q(Tob.block<3, 3>(0, 0));  // 旋转矩阵转四元数
    odom_msg.pose.pose.orientation.x = q.x();
    odom_msg.pose.pose.orientation.y = q.y();
    odom_msg.pose.pose.orientation.z = q.z();
    odom_msg.pose.pose.orientation.w = q.w();

    Eigen::Matrix3d Rbw = Twb.block<3, 3>(0, 0).inverse().eval();
    Eigen::Vector3d linear_v = Rbw * lv;
    Eigen::Vector3d angular_v = Rbw * av;
    odom_msg.twist.twist.linear.x = linear_v.x();
    odom_msg.twist.twist.linear.y = linear_v.y();
    odom_msg.twist.twist.linear.z = linear_v.z();
    odom_msg.twist.twist.angular.x = angular_v.x();
    odom_msg.twist.twist.angular.y = angular_v.y();
    odom_msg.twist.twist.angular.z = angular_v.z();
    odom_writer_->write(odom_msg);

    if (publish_pose_) {
        static double last_publish_time = rosa::Time::now().seconds();
        if (rosa::Time::now().seconds() - last_publish_time > 0.1) {
            last_publish_time = rosa::Time::now().seconds();
            geometry_msgs::msg::PoseStamped pose;
            pose.header = odom_msg.header;
            pose.header.frame_id = "map";
            pose.pose = odom_msg.pose.pose;
            pose_writer_->write(pose);
        }
    }

    if (publish_tf_) {
        geometry_msgs::msg::TransformStamped msg;
        msg.header.stamp = rosa::Time::now();

        // 发布  odom --> map
        if (publish_map_odom_tf_) {
            ULOGI("pub map odom tf");
            msg.header.frame_id = "/map";
            msg.child_frame_id = "/odom";
            msg.transform.translation.x = 0;
            msg.transform.translation.y = 0;
            msg.transform.translation.z = 0;
            msg.transform.rotation.x = 0;
            msg.transform.rotation.y = 0;
            msg.transform.rotation.z = 0;
            msg.transform.rotation.w = 1;
            tb_->sendTransform(msg);
        }

        // 发布 base_footprint --> odom
        if (publish_odom_base_footprint_tf_) {
            ULOGI("pub odom base_footprint tf");
            msg.header.frame_id = "odom";
            msg.child_frame_id = "base_footprint";
            msg.transform.translation.x = Tob(0, 3);
            msg.transform.translation.y = Tob(1, 3);
            msg.transform.translation.z = 0.0;
            msg.transform.rotation.x = q.x();
            msg.transform.rotation.y = q.y();
            msg.transform.rotation.z = q.z();
            msg.transform.rotation.w = q.w();
            tb_->sendTransform(msg);
        }

        if (static_tb_) {
            // 发布 sensor_base --> base_link
            msg.header.frame_id = "base_link";
            msg.child_frame_id = "sensor_base";
            msg.transform.translation.x = 0;
            msg.transform.translation.y = 0;
            msg.transform.translation.z = 0.7;
            msg.transform.rotation.x = 0;
            msg.transform.rotation.y = 0;
            msg.transform.rotation.z = 0;
            msg.transform.rotation.w = 1;
            static_tb_->sendTransform(msg);

            // 发布 base_link --> base_footprint
            msg.header.frame_id = "base_footprint";
            msg.child_frame_id = "base_link";
            msg.transform.translation.x = 0;
            msg.transform.translation.y = 0;
            msg.transform.translation.z = t.z();
            msg.transform.rotation.x = 0;
            msg.transform.rotation.y = 0;
            msg.transform.rotation.z = 0;
            msg.transform.rotation.w = 1;
            static_tb_->sendTransform(msg);
        }
    }
}

void MecanumWheelRobot::publishHeadTF()
{
    geometry_msgs::msg::TransformStamped sensor_to_pitch_msg, pitch_to_yaw_msg;
    sensor_to_pitch_msg.header.stamp = pitch_to_yaw_msg.header.stamp = rosa::Time::now();

    // 绕两个轴选旋转 转四元数
    double pitch = head_pitch_pos_sensor_->getValue();
    double yaw = head_yaw_pos_sensor_->getValue();
    Eigen::Quaterniond q_pitch(std::cos(pitch / 2), 0, std::sin(pitch / 2), 0);
    Eigen::Quaterniond q_yaw(std::cos(yaw / 2), 0, 0, std::sin(yaw / 2));

    sensor_to_pitch_msg.header.frame_id = "/sensor_base";
    sensor_to_pitch_msg.child_frame_id = "/head_pitch_link";
    sensor_to_pitch_msg.transform.translation.x = 0;
    sensor_to_pitch_msg.transform.translation.y = 0;
    sensor_to_pitch_msg.transform.translation.z = 0.5;
    sensor_to_pitch_msg.transform.rotation.x = q_pitch.x();
    sensor_to_pitch_msg.transform.rotation.y = q_pitch.y();
    sensor_to_pitch_msg.transform.rotation.z = q_pitch.z();
    sensor_to_pitch_msg.transform.rotation.w = q_pitch.w();
    tb_->sendTransform(sensor_to_pitch_msg);

    pitch_to_yaw_msg.header.frame_id = "/head_pitch_link";
    pitch_to_yaw_msg.child_frame_id = "/head_yaw_link";
    pitch_to_yaw_msg.transform.translation.x = 0;
    pitch_to_yaw_msg.transform.translation.y = 0;
    pitch_to_yaw_msg.transform.translation.z = 0;
    pitch_to_yaw_msg.transform.rotation.x = q_yaw.x();
    pitch_to_yaw_msg.transform.rotation.y = q_yaw.y();
    pitch_to_yaw_msg.transform.rotation.z = q_yaw.z();
    pitch_to_yaw_msg.transform.rotation.w = q_yaw.w();
    tb_->sendTransform(pitch_to_yaw_msg);

    // pub topic
    auto message = sensor_msgs::msg::JointState();
    message.header.stamp = sensor_to_pitch_msg.header.stamp;
    message.name = {"head_pitch_joint", "head_yaw_joint"};
    message.position = {pitch, yaw};
    message.velocity = {0.0, 0.0};
    message.effort = {0.0, 0.0};
    head_joint_writer_->write(message);
}

void MecanumWheelRobot::keyboard()
{
    static double vx = 0.5;
    static double vy = 0.5;
    static double vz = 0.5;

    static double curr_x = 0.0;
    static double curr_y = 0.0;
    static double curr_z = 0.0;

    static double curr_pitch = 0.0;
    static double curr_yaw = 0.0;

    static double max_linear_v = 0.70;
    static double max_angular_v = 2.0;
    static double min_linear_v = 0.1;
    static double min_angular_v = 0.1;

    int key = kb_.getKey();

    if (key == -1) {
        return;
    }

    // x方向线速度加减速
    if (key == 81) {  // q x方向加速
        if (vx < max_linear_v) {
            vx += 0.1;
            ULOGI_S() << "increase vx 0.1 to " << vx;
        } else {
            ULOGE_S() << "can not increase vx because it exceeds max velocity = " << max_linear_v;
        }
    }
    if (key == 90) {  // z x方向加速
        if (vx > min_linear_v) {
            vx -= 0.1;
            ULOGI_S() << "decrease vx 0.1 to " << vx;
        } else {
            ULOGE_S() << "can not decrease vx because it exceeds min velocity = " << max_linear_v;
        }
    }

    // y方向线速度加减速
    if (key == 87) {  // w y方向加速
        if (vy < max_linear_v) {
            vy += 0.1;
            ULOGI_S() << "increase vy 0.1 to " << vy;
        } else {
            ULOGE_S() << "can not increase vy because it exceeds max velocity = " << max_linear_v;
        }
    }
    if (key == 88) {  // x y方向减速
        if (vy > min_linear_v) {
            vy -= 0.1;
            ULOGI_S() << "decrease vy 0.1 to " << vy;
        } else {
            ULOGE_S() << "can not decrease vy because it exceeds min velocity = " << max_linear_v;
        }
    }

    // 角速度加减速
    if (key == 69) {  // e angular加速
        if (vz < max_angular_v) {
            vz += 0.1;
            ULOGI_S() << "increase vz 0.1 to " << vz;
        } else {
            ULOGE_S() << "can not increase vz because it exceeds max velocity = " << max_angular_v;
        }
    }
    if (key == 67) {  // c angular减速
        if (vz > min_angular_v) {
            vz -= 0.1;
            ULOGI_S() << "decrease vz 0.1 to " << vz;
        } else {
            ULOGE_S() << "can not decrease vz because it exceeds min velocity = " << min_angular_v;
        }
    }

    // 修改运动状态
    if (key == 315) {  // 上
        state_ = MOTION_STATE::FORWARD;
    } else if (key == 317) {  // 下
        state_ = MOTION_STATE::BACKWARD;
    } else if (key == 314) {  // 左
        state_ = MOTION_STATE::LEFT;
    } else if (key == 316) {  // 右
        state_ = MOTION_STATE::RIGHT;
    } else if (key == 65) {  // a 左转
        state_ = MOTION_STATE::LEFT_TURN;
    } else if (key == 68) {  // d 右转
        state_ = MOTION_STATE::RIGHT_TURN;
    } else if (key == 32) {  // space 停止
        state_ = MOTION_STATE::STOP;
    } else if (key == 73) {  // I 头部向上
        state_ = MOTION_STATE::HEAD_PITCH_UP;
    } else if (key == 75) {  // K 头部向上
        state_ = MOTION_STATE::HEAD_PITCH_DOWN;
    } else if (key == 76) {  // L 头部向左
        state_ = MOTION_STATE::HEAD_YAW_LEFT;
    } else if (key == 74) {  // J 头部向右
        state_ = MOTION_STATE::HEAD_YAW_RIGHT;
    }
    switch (state_) {
        case MOTION_STATE::FORWARD:
            curr_x = vx;
            curr_y = 0.0;
            curr_z = 0.0;
            break;
        case MOTION_STATE::BACKWARD:
            curr_x = -vx;
            curr_y = 0.0;
            curr_z = 0.0;
            break;
        case MOTION_STATE::LEFT:
            curr_x = 0.0;
            curr_y = vy;
            curr_z = 0.0;
            break;
        case MOTION_STATE::RIGHT:
            curr_x = 0.0;
            curr_y = -vy;
            curr_z = 0.0;
            break;
        case MOTION_STATE::LEFT_TURN:
            curr_x = 0.0;
            curr_y = 0.0;
            curr_z = vz;
            break;
        case MOTION_STATE::RIGHT_TURN:
            curr_x = 0.0;
            curr_y = 0.0;
            curr_z = -vz;
            break;
        case MOTION_STATE::STOP:
            curr_x = 0.0;
            curr_y = 0.0;
            curr_z = 0.0;
            curr_pitch = 0.0;
            curr_yaw = 0.0;
            break;
        case MOTION_STATE::HEAD_PITCH_UP:
            curr_pitch = 0.8;
            break;
        case MOTION_STATE::HEAD_PITCH_DOWN:
            curr_pitch = -0.8;
            break;
        case MOTION_STATE::HEAD_YAW_LEFT:
            curr_yaw = -0.8;
            break;
        case MOTION_STATE::HEAD_YAW_RIGHT:
            curr_yaw = 0.8;
            break;
        default:
            break;
    }
    Eigen::Vector3d linear(curr_x, curr_y, 0.0);
    Eigen::Vector3d angular(0.0, 0.0, curr_z);
    setVelocity(linear, angular);
    setHeadVelocity(curr_pitch, curr_yaw, 0.3, 0.3);
}

std::string MecanumWheelRobot::motionStateToString(MOTION_STATE state)
{
    switch (state) {
        case MOTION_STATE::FORWARD:
            return "FORWARD";
        case MOTION_STATE::BACKWARD:
            return "BACKWARD";
        case MOTION_STATE::LEFT:
            return "LEFT";
        case MOTION_STATE::RIGHT:
            return "RIGHT";
        case MOTION_STATE::LEFT_TURN:
            return "LEFT_TURN";
        case MOTION_STATE::RIGHT_TURN:
            return "RIGHT_TURN";
        case MOTION_STATE::STOP:
            return "STOP";
        case MOTION_STATE::HEAD_PITCH_UP:
            return "HEAD_PITCH_UP";
        case MOTION_STATE::HEAD_PITCH_DOWN:
            return "HEAD_PITCH_DOWN";
        case MOTION_STATE::HEAD_YAW_LEFT:
            return "HEAD_YAW_LEFT";
        case MOTION_STATE::HEAD_YAW_RIGHT:
            return "HEAD_YAW_RIGHT";
        default:
            return "UNKNOWN";
    }
}
}  // namespace simulation
