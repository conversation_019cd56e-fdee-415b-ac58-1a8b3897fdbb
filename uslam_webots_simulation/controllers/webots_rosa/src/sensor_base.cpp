#include "sensor_base.hpp"

using namespace webots;

namespace simulation {
SensorBase::SensorBase(webots::Robot *robot, int step, const YAML::Node &param) :
    robot_(robot), sensor_step_(step), param_(param), super_robot_((webots::Supervisor *const)robot)
{
    if (param_["publish_tf"]) publish_tf_ = param_["publish_tf"].as<bool>();
    if (param_["parent_frame_id"]) parent_frame_id_ = param_["parent_frame_id"].as<std::string>();
}

bool SensorBase::init()
{
    if (onInit()) inited_ = true;
    return inited_;
}

void SensorBase::publishValue(const rosa::Time &time_stamp)
{
    if (!isStepReady()) return;
    onPublish(time_stamp);
}

bool SensorBase::isStepReady()
{
    if (!inited_) {
        ULOGE("sensor not inited");
        return false;
    }

    if (last_time_ == 0) {
        last_time_ = robot_->getTime();
        return true;
    }
    if ((robot_->getTime() - last_time_) * 1000.0 > sensor_step_ * 1.0 - 1e-6) {
        last_time_ = robot_->getTime();
        return true;
    } else {
        return false;
    }
}
}  // namespace simulation
