%YAML:1.0

# twist_in_topic: "/twist_cmd_vel"         # 速度输入topic
twist_in_topic: "/vnav/cmd_vel"         # 速度输入topic

RobotModel:                             
  type: DiffWheelRobot                   # 差分底盘
  odom_out_topic: "odom"
  publish_tf: true
  publish_map_odom_tf: true             # 是否发布odom到maptf
  use_real_odom_and_pose: true          # 是否使用全局真实位姿

  # publish_odom_base_footprint_tf: false # 是否发布base_footprint到odom的tf
  # odom_out_topic: "/mc/leg/walking_odom" # 里程计输出topic
  # pose_out_topic: "/vnav/webots/robot_pose"          # 机器人位姿输出topic
  
sensor_list: [Lidar,RGBD1,IMU]
# 为了正常初始化，配置必须设定  publish_tf；parent_frame_id； 这两项！！！ 
Lidar:
  type: Lidar2d
  lidar_name: "lidar"
  topic_name: "scan"
  link_name: "laser_link"
  publish_tf: true
  sensor_freq_ratio: 10
  parent_frame_id: base_link   # 这里的parent frame根据实际需要进行配置

RGBD1:
  type: RGBD
  rgbd_name: "rgbd"
  rgb_topic_name: "rgbd_rgb"
  depth_topic_name: "rgbd_depth"  
  enable_depth: true
  enable_rgb: false
  link_name: "camera_link"
  sensor_freq_ratio: 10
  enable_ident_frame: true
  enable_mix_frame: true
  enable_depth_points: false
  publish_tf: true
  parent_frame_id: base_link   

IMU:
  type: IMU
  imu_name: "imu"
  topic_name: "imu_data"
  link_name: "imu_link"
  publish_tf: true
  parent_frame_id: base_link

