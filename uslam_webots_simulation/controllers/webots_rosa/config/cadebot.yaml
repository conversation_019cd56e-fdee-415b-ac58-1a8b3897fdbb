%YAML:1.0

twist_in_topic: "cmd_vel"               # 速度输入topic

RobotModel:                             # 机器人底盘模型
  type: DiffWheelRobot                  # 差分底盘
  odom_out_topic: "odom"                # 里程计输出topic
  publish_tf: true                      # 是否发布tf
  publish_map_odom_tf: true            # 是否发布odom到maptf
  use_real_odom_and_pose: true        # 是否使用全局真实位姿

# 传感器列表，和下边每个传感器的配置名称相对应
sensor_list: [RGBD2,RGBD1,IMU]

Lidar:                                  # 2D激光雷达
  type: Lidar2d
  lidar_name: "lidar"                   # 雷达在webots模型中的名称
  topic_name: "LaserScan"                    # 雷达topic
  link_name: "top_laser_link"               # 雷达tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速
  parent_frame_id: base_link

RGBD1:
  type: RGBD
  rgbd_name: "rgbd_bottom"              # RGBD在webots模型中的名称
  rgb_topic_name: "rgbd_bottom_rgb"     # rgb图像输出topic
  depth_topic_name: "rgbd_bottom_depth" # depth图像输出topic
  enable_depth: true                    # 是否输出depth图像
  enable_depth_points: true
  enable_rgb: true                    # 是否输出rgb图像
  enable_ident_frame: true
  enable_mix_frame: true
  link_name: "bottom_depth_image_data"              # rgbd tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速
  parent_frame_id: base_link

RGBD2:
  type: RGBD
  rgbd_name: "rgbd_waist"
  rgb_topic_name: "rgbd_waist_rgb"
  depth_topic_name: "rgbd_waist_depth"
  enable_depth: true
  enable_depth_points: true
  enable_rgb: true
  enable_ident_frame: true
  enable_mix_frame: true
  link_name: "waist_depth_image_data"
  publish_tf: true                      # whether publish tf
  sensor_freq_ratio: 10
  parent_frame_id: base_link

IMU:                                    
  type: IMU
  imu_name: "imu"                       # IMU在webots模型中的名称
  topic_name: "imu_data"                # IMU topic
  link_name: "imu_link"                 # IMU tf frame id
  publish_tf: true                     # whether publish tf
  parent_frame_id: base_link
