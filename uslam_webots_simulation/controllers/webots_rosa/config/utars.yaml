%YAML:1.0

twist_in_topic: "/cmd_vel"               # 速度输入topic

RobotModel:                             # 机器人底盘
  type: DiffWheelRobot               # 表示传感器类型，在代码中已绑定
  publish_tf: false                      # 是否发布tf
  publish_pose: false
  publish_map_odom_tf: false             # 是否发布odom到maptf
  use_real_odom_and_pose: false          # 是否使用全局真实位姿
  odom_out_topic: "/mc/odom"                # 里程计输出topic
  pose_out_topic: "robot_pose"         # 机器人位姿输出topic

# 传感器列表，和下边每个传感器的配置名称相对应
# sensor_list: [IMU,Stereo,FisheyeLeft,FisheyeRight,RGBD_HEAD_FRONT,RGBD_WAIST_FRONT,RGBD_HEAD_BACK,RGBD_WAIST_BACK]
# sensor_list: [IMU,FisheyeLeft,FisheyeRight,Stereo,RGBD_HEAD_FRONT,RGBD_WAIST_FRONT]
# sensor_list: [<PERSON><PERSON>_<PERSON>,<PERSON><PERSON>_Back,IMU,FisheyeLeft,FisheyeRight,Stereo,RGBD_HEAD_FRONT,RGBD_WAIST_FRONT]
sensor_list: [Lidar_Front,Lidar_Back,IMU]

# Lidar:                                  # 2D激光雷达
#   type: Lidar2d
#   lidar_name: "lidar"                   # 雷达在webots模型中的名称
#   topic_name: "LaserScan"                    # 雷达topic
#   link_name: "top_laser_link"               # 雷达tf frame id
#   publish_tf: false                     # whether publish tf
#   sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

Lidar_Front:                                  # 2D激光雷达
  type: Lidar2d
  lidar_name: "lidar_front"                   # 雷达在webots模型中的名称
  topic_name: "/sensor/lidar/front"                    # 雷达topic
  link_name: "front_lidar_link"               # 雷达tf frame id
  publish_tf: false                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

Lidar_Back:                                  # 2D激光雷达
  type: Lidar2d
  lidar_name: "lidar_back"                   # 雷达在webots模型中的名称
  topic_name: "/sensor/lidar/back"                    # 雷达topic
  link_name: "back_lidar_link"               # 雷达tf frame id
  publish_tf: false                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

Lidar3D:                                  # 3D激光雷达
  type: Lidar3d
  lidar_name: "lidar3d"                   # 雷达在webots模型中的名称
  topic_name: "/sensor/lidar3d"                    # 雷达topic
  link_name: "lidar3d_link"               # 雷达tf frame id
  publish_tf: false                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速
  horizontalResolution: 1800
  fieldOfView: 6.28318
  verticalFieldOfView: 0.3491
  numberOfLayers: 16
  minRange: 1
  maxRange: 100
  projection: "cylindrical"
  type: "rotating"
  noise: 0.003 # 0.03 / 100
  defaultFrequency: 5
  minFrequency: 5
  maxFrequency: 20

IMU:
  type: IMU
  imu_name: "imu"                       # IMU在webots模型中的名称
  topic_name: "/sensor/imu/x86"        # IMU topic
  link_name: "imu_link"                 # IMU tf frame id
  publish_tf: true                     # whether publish tf

Stereo:
  type: Stereo
  stereo_name: "stereo_camera"                # RGBD在webots模型中的名称
  left_image_topic_name: "/sensor/camera/stereo_left/image/raw"    # rgb图像输出topic
  right_image_topic_name: "/sensor/camera/stereo_right/image/raw" # depth图像输出topic
  left_link_name: "stereo_left_link"    # left image tf frame id
  right_link_name: "stereo_right_link"  # right image tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 5                        # 发布频率倍速，为当前仿真运行频率的倍速

FisheyeLeft:
  type: Camera
  camera_name: "fisheye_left"           # 鱼眼相机在webots模型中的名称
  image_topic_name: "/sensor/camera/fisheye_left/image/raw"   # 图像话题名称
  link_name: "fisheye_left_link"  # image tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

FisheyeRight:
  type: Camera
  camera_name: "fisheye_right"         # 鱼眼相机在webots模型中的名称
  image_topic_name: "/sensor/camera/fisheye_right/image/raw"   # 图像话题名称
  link_name: "fisheye_right_link"  # image tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

RGBD_HEAD_FRONT:
  type: RGBD
  rgbd_name: "rgbd_head_front"                # RGBD在webots模型中的名称
  rgb_topic_name: "/sensor/camera/head_front_rgbd/color/raw"     # rgb图像输出topic
  depth_topic_name: "/sensor/camera/head_front_rgbd/depth/raw" # depth图像输出topic
  depth_info_topic_name: "/sensor/camera/head_front_rgbd_info"
  enable_depth: true                    # 是否输出depth图像
  enable_depth_points: false
  enable_rgb: false                      # 是否输出rgb图像
  link_name: "head_front_rgbd_link"  # rgbd tf frame id
  publish_tf: true                      # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

RGBD_WAIST_FRONT:
  type: RGBD
  rgbd_name: "rgbd_waist_front"
  rgb_topic_name: "/sensor/camera/waist_front_rgbd/color/raw"
  depth_topic_name: "/sensor/camera/waist_front_rgbd/depth/raw"
  depth_info_topic_name: "/sensor/camera/waist_front_rgbd_info"
  enable_depth: true
  enable_depth_points: false
  enable_rgb: false
  link_name: "waist_front_rgbd_link"
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10

RGBD_HEAD_BACK:
  type: RGBD
  rgbd_name: "rgbd_head_back"                # RGBD在webots模型中的名称
  rgb_topic_name: "/sensor/camera/head_back_rgbd/color/raw"     # rgb图像输出topic
  depth_topic_name: "/sensor/camera/head_back_rgbd/depth/raw" # depth图像输出topic
  enable_depth: true                    # 是否输出depth图像
  enable_depth_points: false
  enable_rgb: true                      # 是否输出rgb图像
  link_name: "head_back_rgbd_link"  # rgbd tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

RGBD_WAIST_BACK:
  type: RGBD
  rgbd_name: "rgbd_waist_back"
  rgb_topic_name: "/sensor/camera/waist_back_rgbd/color/raw"
  depth_topic_name: "/sensor/camera/waist_back_rgbd/depth/raw"
  enable_depth: true
  enable_depth_points: false
  enable_rgb: false
  link_name: "waist_back_rgbd_link"
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 10
