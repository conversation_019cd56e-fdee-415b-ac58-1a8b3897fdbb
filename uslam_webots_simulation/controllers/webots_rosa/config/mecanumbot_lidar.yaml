%YAML:1.0

# twist_in_topic: "/vnav/cmd_vel"         # 速度输入topic
twist_in_topic: "/mc/cmd_vel"  

RobotModel:                             # 机器人底盘
  type: MecanumWheelRobot               # 表示传感器类型，在代码中已绑定 
  publish_tf: true                      # 是否发布tf
  publish_pose: true
  # publish_odom_base_footprint_tf: false # 是否发布base_footprint到odom的tf
  # publish_map_odom_tf: false            # 是否发布odom到map的tf
  publish_odom_base_footprint_tf: true # 是否发布base_footprint到odom的tf
  publish_map_odom_tf: false            # 是否发布odom到map的tf
  use_real_odom_and_pose: false          # 是否使用全局真实位姿
  #odom_out_topic: "/mc/leg/walking_odom" # 里程计输出topic
  odom_out_topic: "/mc/odom"
  pose_out_topic: "/vnav/webots/robot_pose"          # 机器人位姿输出topic

# 传感器列表，和下边每个传感器的配置名称相对应
# sensor_list: [IMU,Stereo,FisheyeLeft,FisheyeRight,RGBD_HEAD_FRONT,RGBD_WAIST_FRONT,RGBD_HEAD_BACK,RGBD_WAIST_BACK]
#sensor_list: [IMU,FisheyeLeft,FisheyeRight,Stereo,RGBD_HEAD_FRONT,RGBD_WAIST_FRONT]
sensor_list: [Lidar_Front,Lidar_Back,IMU,FisheyeLeft,FisheyeRight,Stereo,RGBD_HEAD_FRONT,RGBD_WAIST_FRONT]

# Lidar:                                  # 2D激光雷达
#   type: Lidar2d
#   lidar_name: "lidar"                   # 雷达在webots模型中的名称
#   topic_name: "LaserScan"                    # 雷达topic
#   link_name: "top_laser_link"               # 雷达tf frame id
#   publish_tf: false                     # whether publish tf
#   sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

Lidar_Front:                                  # 2D激光雷达
  type: Lidar2d
  lidar_name: "lidar_front"                   # 雷达在webots模型中的名称
  topic_name: "laser_front"                    # 雷达topic
  link_name: "front_lidar_link"               # 雷达tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 3                 # 发布频率倍速，为当前仿真运行频率的倍速
  parent_frame_id: sensor_base

Lidar_Back:                                  # 2D激光雷达
  type: Lidar2d
  lidar_name: "lidar_back"                   # 雷达在webots模型中的名称
  topic_name: "laser_back"                    # 雷达topic
  link_name: "back_lidar_link"               # 雷达tf frame id
  publish_tf: true                     # whether publish tf
  sensor_freq_ratio: 3                 # 发布频率倍速，为当前仿真运行频率的倍速
  parent_frame_id: sensor_base

IMU:                                    
  type: IMU
  imu_name: "imu"                       # IMU在webots模型中的名称
  topic_name: "/sensor/imu/orin"        # IMU topic
  link_name: "imu_link"                 # IMU tf frame id
  publish_tf: false                     # whether publish tf
  parent_frame_id: head_yaw_link

Stereo:
  type: Stereo
  stereo_name: "stereo_camera"                # RGBD在webots模型中的名称
  left_image_topic_name: "/sensor/camera/stereo_left/image/raw"    # rgb图像输出topic
  right_image_topic_name: "/sensor/camera/stereo_right/image/raw" # depth图像输出topic
  left_link_name: "stereo_left_rgb_link"    # left image tf frame id
  right_link_name: "stereo_right_rgb_link"  # right image tf frame id
  publish_tf: true                     # whether publish tf
  parent_frame_id: head_yaw_link
  sensor_freq_ratio: 10                   # 发布频率倍速，为当前仿真运行频率的倍速

FisheyeLeft:
  type: Camera
  camera_name: "fisheye_left"           # 鱼眼相机在webots模型中的名称
  image_topic_name: "/sensor/camera/fisheye_left/image/raw"   # 图像话题名称
  link_name: "head_left_fisheye_optical_link"  # image tf frame id
  publish_tf: true                     # whether publish tf
  parent_frame_id: head_yaw_link
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

FisheyeRight:
  type: Camera
  camera_name: "fisheye_right"         # 鱼眼相机在webots模型中的名称
  image_topic_name: "/sensor/camera/fisheye_right/image/raw"   # 图像话题名称
  link_name: "head_right_fisheye_optical_link"  # image tf frame id
  publish_tf: true                     # whether publish tf
  parent_frame_id: head_yaw_link
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

RGBD_HEAD_FRONT:
  type: RGBD
  rgbd_name: "head_front_rgbd"                # RGBD在webots模型中的名称
  enable_ident_frame: true                    # 是否输出单独的彩色和深度图像
  enable_mix_frame: true                      # 是否输出合并的彩色和深度图像
  enable_depth_points: true                  # 是否输出深度点云
  link_name: "head_front_rgbd_depth_optical_frame"           # rgbd tf frame id
  publish_tf: true                            # whether publish tf
  parent_frame_id: head_yaw_link
  sensor_freq_ratio: 10                       # 发布频率倍速，为当前仿真运行频率的倍速

RGBD_WAIST_FRONT:
  type: RGBD
  rgbd_name: "waist_front_rgbd"
  enable_ident_frame: true                    # 是否输出单独的彩色和深度图像
  enable_mix_frame: true                      # 是否输出合并的彩色和深度图像
  enable_depth_points: true                  # 是否输出深度点云
  link_name: "waist_front_rgbd_depth_optical_frame"
  publish_tf: true                            # whether publish tf
  parent_frame_id: sensor_base
  sensor_freq_ratio: 10

RGBD_HEAD_BACK:
  type: RGBD
  rgbd_name: "head_back_rgbd"                # RGBD在webots模型中的名称
  enable_ident_frame: true                    # 是否输出单独的彩色和深度图像
  enable_mix_frame: true                      # 是否输出合并的彩色和深度图像
  enable_depth_points: false                  # 是否输出深度点云
  link_name: "head_back_rgbd_depth_optical_link"  # rgbd tf frame id
  publish_tf: true                     # whether publish tf
  parent_frame_id: head_yaw_link
  sensor_freq_ratio: 10                 # 发布频率倍速，为当前仿真运行频率的倍速

RGBD_WAIST_BACK:
  type: RGBD
  rgbd_name: "waist_back_rgbd"
  enable_ident_frame: true                    # 是否输出单独的彩色和深度图像
  enable_mix_frame: true                      # 是否输出合并的彩色和深度图像
  enable_depth_points: false                  # 是否输出深度点云
  link_name: "waist_back_rgbd_depth_optical_link"
  publish_tf: true                     # whether publish tf
  parent_frame_id: sensor_base
  sensor_freq_ratio: 10
