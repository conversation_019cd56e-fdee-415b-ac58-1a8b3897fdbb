"""utars_motor controller."""

# You may need to import some classes of the controller module. Ex:
#  from controller import Robot, Motor, DistanceSensor
from controller import Robot

# create the Robot instance.
robot = Robot()

# get the time step of the current world.
timestep = int(robot.getBasicTimeStep())

# You should insert a getDevice-like function in order to get the
# instance of a device of the robot. Something like:
lifter_pitch_1_joint = robot.getDevice('lifter_pitch_1_joint')
lifter_pitch_2_joint = robot.getDevice('lifter_pitch_2_joint')
lifter_pitch_3_joint = robot.getDevice('lifter_pitch_3_joint')
ds = robot.getDevice('lifter_pitch_1_joint_sensor')
ds.enable(timestep)

# Main loop:
# - perform simulation steps until <PERSON><PERSON> is stopping the controller
while robot.step(timestep) != -1:
    # Read the sensors:
    # Enter here functions to read sensor data, like:
    val = ds.getValue()
    print("val:{}".format(val))

    # Process sensor data here.

    # Enter here functions to send actuator commands, like:
    lifter_pitch_1_joint.setPosition(-0.2)
    lifter_pitch_2_joint.setPosition(0.4)
    lifter_pitch_3_joint.setPosition(-0.2)
    pass

# Enter here exit cleanup code.
