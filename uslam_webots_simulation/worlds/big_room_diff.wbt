#VRML_SIM R2025a utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Roughcast.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Parquetry.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Pavement.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/PorcelainChevronTiles.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/CementTiles.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/cabinet/protos/Cabinet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/cabinet/protos/CabinetHandle.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/PaintedWood.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/solids/protos/SolidBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/fridge/protos/Fridge.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/components/protos/Sink.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/oven/protos/Oven.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/components/protos/HotPlate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/components/protos/Worktop.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/stairs/protos/StraightStairs.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/CarpetFibers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/stairs/protos/StraightStairsRail.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/BrushedAluminium.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/toys/protos/PaperBoat.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/breakfast/protos/BiscuitBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/breakfast/protos/CerealBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/breakfast/protos/HoneyJar.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/breakfast/protos/JamJar.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/WallPlug.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/Laptop.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/CardboardBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/manhole/protos/SquareManhole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pipes/protos/PipeSection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/garden/protos/Barbecue.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/toys/protos/RubberDuck.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/school_furniture/protos/Book.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/bedroom/protos/Bed.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/BlanketFabric.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/chairs/protos/WoodenChair.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/tables/protos/RoundTable.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Cookware.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Lid.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/WoodenSpoon.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/tables/protos/Desk.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Wall.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Window.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Radiator.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/DesktopComputer.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/Keyboard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/Monitor.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/ComputerMouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Door.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/DoorLever.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/lights/protos/CeilingLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/lights/protos/FloorLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/tables/protos/Table.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Carafe.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Plate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Knife.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Glass.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/drinks/protos/WaterBottle.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Fork.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/utensils/protos/Wineglass.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/chairs/protos/Chair.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/fruits/protos/FruitBowl.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/fruits/protos/Apple.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/fruits/protos/Orange.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/drinks/protos/BeerBottle.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/drinks/protos/Can.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/living_room_furniture/protos/Armchair.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/living_room_furniture/protos/Sofa.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/living_room_furniture/protos/Carpet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/plants/protos/BunchOfSunFlowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/plants/protos/PottedTree.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/paintings/protos/PortraitPainting.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/paintings/protos/LandscapePainting.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/television/protos/Television.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/fire_extinguisher/protos/FireExtinguisher.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/school_furniture/protos/Clock.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/telephone/protos/OfficeTelephone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/bathroom/protos/WashingMachine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/bathroom/protos/BathroomSink.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/bathroom/protos/Bathtube.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/bathroom/protos/Toilet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/lights/protos/CeilingSpotLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/trees/protos/Cypress.proto"
EXTERNPROTO "../protos/Cruzr.proto"

WorldInfo {
  info [
    "Simulation of the KUKA youBot robot."
    "This model includes an accurate and fast simulation of the Meccanum wheels."
    "The robot can be equiped with various sensors (including the Microsoft Kinect) and configured with zero, one or two arms."
  ]
  title "KUKA youBot"
  basicTimeStep 10
  optimalThreadCount 2
  contactProperties [
    ContactProperties {
      material1 "InteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation -0.785398 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
    ContactProperties {
      material1 "ExteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation 0.785398 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
  ]
}
Viewpoint {
  orientation 0.26406149955228864 -0.03260943101589663 -0.9639544332918524 3.324573054600002
  position 3.065804031457116 -5.63904619416749 7.858723927637797
  exposure 0.5
  follow "youBot"
  ambientOcclusionRadius 1
}
TexturedBackground {
  texture "empty_office"
  skybox FALSE
  skyColor [
    0.2 0.2 0.2
  ]
}
DEF ROOF Group {
  children [
    Pose {
      translation -4.83 -6.49 2.4
      rotation -1 0 0 -3.1415923071795864
      children [
        Shape {
          appearance Roughcast {
            textureTransform TextureTransform {
              scale 13 10
            }
          }
          geometry Plane {
            size 10 13
          }
        }
      ]
    }
    Pose {
      translation -11.13 -1.71 2.4
      rotation -1 0 0 -3.1415923071795864
      children [
        Shape {
          appearance Roughcast {
            textureTransform TextureTransform {
              scale 3.4 2.6
            }
          }
          geometry Plane {
            size 2.6 3.4
          }
        }
      ]
    }
  ]
}
DEF FLOOR Solid {
  translation -4.96 -6.5 0
  children [
    DEF LIVING_ROOM_1 Pose {
      translation -1.91 3.2 0
      children [
        Shape {
          appearance Parquetry {
            textureTransform TextureTransform {
              scale 6.3 6.26
            }
          }
          geometry Plane {
            size 6.3 6.26
          }
        }
      ]
    }
    DEF LIVING_ROOM_2 Pose {
      translation -6.24 4.84 0
      rotation 0 0 1 1.5708
      children [
        Shape {
          appearance Parquetry {
            textureTransform TextureTransform {
              scale 3.2 2.4
            }
          }
          geometry Plane {
            size 3.2 2.4
          }
        }
      ]
    }
    DEF GARDEN Pose {
      translation -6.29 -1.87 0
      rotation 0 0 1 1.5708
      children [
        Shape {
          appearance Pavement {
            type "slate"
            textureTransform TextureTransform {
              scale 9.8 2.6
            }
          }
          geometry Plane {
            size 9.8 2.6
          }
        }
      ]
    }
    DEF KITCHEN Pose {
      translation 3.02 3.2 0
      rotation 0 0 1 1.5708
      children [
        Shape {
          appearance PorcelainChevronTiles {
            textureTransform TextureTransform {
              rotation 1.5708
              scale 3.6 6.3
            }
          }
          geometry Plane {
            size 6.3 3.6
          }
        }
      ]
    }
    DEF CORRIDOR Pose {
      translation 0.15 -1.014 0
      rotation 0 0 1 1.5708
      children [
        Shape {
          appearance CementTiles {
            textureTransform TextureTransform {
              scale 2.135 9.933
            }
          }
          geometry Plane {
            size 2.135 9.933
          }
        }
      ]
    }
    DEF BATHROOM_1 Pose {
      translation 0.48 -4.391 0
      children [
        Shape {
          appearance CementTiles {
            textureTransform TextureTransform {
              scale 2 4.6185
            }
          }
          geometry Plane {
            size 2 4.6185
          }
        }
      ]
    }
    DEF ROOM_1 Pose {
      translation -2.74 -4.391 0
      rotation 0 0 1 1.5708
      children [
        Shape {
          appearance Parquetry {
            textureTransform TextureTransform {
              scale 4.6185 4.3
            }
          }
          geometry Plane {
            size 4.6185 4.3
          }
        }
      ]
    }
    DEF ROOM_2 Pose {
      translation 3.3 -4.391 0
      rotation 0 0 1 1.5708
      children [
        Shape {
          appearance Parquetry {
            textureTransform TextureTransform {
              scale 4.6185 3.6
            }
          }
          geometry Plane {
            size 4.6185 3.6
          }
        }
      ]
    }
  ]
  name "floor"
  model "floor"
  boundingObject Plane {
    size 13 10
  }
}
DEF KITCHEN_BLOCK Pose {
  translation -2.09 -2.3 0
  rotation 0 0 -1 -1.5707953071795862
  children [
    DEF KITCHEN_TILES_1 Pose {
      translation 2.147 0.4 0.9
      rotation 0 -1 0 1.5708
      children [
        Shape {
          appearance PorcelainChevronTiles {
            textureTransform TextureTransform {
              rotation 1.5708
              scale 2.6 1.8
            }
          }
          geometry Plane {
            size 1.8 2.6
          }
        }
      ]
    }
    DEF KITCHEN_TILES_2 Pose {
      translation 1.19 1.648 0.9
      rotation -0.5773502691896258 -0.5773502691896258 0.5773502691896258 -2.094395307179586
      children [
        Shape {
          appearance PorcelainChevronTiles {
            textureTransform TextureTransform {
              rotation 1.5708
              scale 2.5 1.8
            }
          }
          geometry Plane {
            size 1.8 2.5
          }
        }
      ]
    }
    Cabinet {
      hidden position_6_0 -5.64588631934487e-05
      hidden translation_7 -1.1235313769527338e-05 3.171665152734704e-10 0
      hidden rotation_7 0 0 -1 5.645886175436675e-05
      translation 2.15 -1 0.83
      rotation 0 0 1 3.14159
      name "cabinet(6)"
      depth 0.7
      outerThickness 0.02
      rowsHeights [
        0.2, 0.2, 0.2, 0.2, 0.2, 0.34
      ]
      columnsWidths [
        0.4
      ]
      layout [
        "LeftSidedDoor (1, 1, 1, 6, 1.5)"
        "Shelf (1,2, 1, 0)"
        "Shelf (1,3, 1, 0)"
        "Shelf (1,4, 1, 0)"
        "Shelf (1,5, 1, 0)"
        "Shelf (1,6, 1, 0)"
      ]
      handle CabinetHandle {
        translation 0 -0.3 0
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
    }
    Cabinet {
      hidden position_6_0 6.208491037140947e-05
      hidden translation_7 -1.3590386871570756e-05 -4.2187897619783143e-10 0
      hidden rotation_7 0 0 0.9999999999999999 6.208491158123177e-05
      hidden position_10_0 6.20849103688951e-05
      hidden translation_11 -1.3590386871020371e-05 -4.2187897619783143e-10 0
      hidden rotation_11 0 0 1 6.208491158123177e-05
      translation -0.11 1.65 0.15
      rotation 0 0 1 -1.5707953071795862
      name "cabinet(2)"
      depth 0.67
      outerThickness 0.02
      rowsHeights [
        0.68, 0.25, 0.25, 0.25, 0.25, 0.34
      ]
      columnsWidths [
        0.44
      ]
      layout [
        "RightSidedDoor (1, 2, 1, 3, 1.5)"
        "RightSidedDoor (1, 5, 1, 2, 1.5)"
        "Shelf (1,2, 1, 0)"
        "Shelf (1,3, 1, 0)"
        "Shelf (1,4, 1, 0)"
        "Shelf (1,5, 1, 0)"
        "Shelf (1,6, 1, 0)"
      ]
      handle CabinetHandle {
        translation 0 -0.15 0
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.827451 0.843137 0.811765
      }
    }
    Cabinet {
      hidden position_6_0 6.208325143827086e-05
      hidden translation_7 -1.359002373110743e-05 -4.2185624438140223e-10 0
      hidden rotation_7 0 0 1 6.208325207860783e-05
      hidden position_10_0 -6.209646520314257e-05
      hidden translation_11 -1.3592916224232264e-05 4.2203598948908905e-10 0
      hidden rotation_11 0 0 -1 6.20964624934256e-05
      hidden position_14_0 -6.209646520301811e-05
      hidden translation_15 -1.3592916224205017e-05 4.2203598948908905e-10 0
      hidden rotation_15 0 0 -1 6.20964624934256e-05
      translation 2.15 -0.1 1.4
      rotation 0 0 1 3.14159
      name "cabinet(3)"
      outerThickness 0.02
      rowsHeights [
        0.21, 0.21, 0.22
      ]
      columnsWidths [
        0.44, 0.44, 0.44
      ]
      layout [
        "RightSidedDoor (1, 1, 1, 3, 1.5)"
        "LeftSidedDoor (2, 1, 1, 3, 1.5)"
        "LeftSidedDoor (3, 1, 1, 3, 1.5)"
        "Shelf (1, 2, 3, 0)"
        "Shelf (1, 3, 2, 0)"
        "Shelf (1, 1, 0, 1)"
        "Shelf (2, 1, 0, 3)"
      ]
      handle CabinetHandle {
        translation 0 -0.2 0
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.827451 0.843137 0.811765
      }
    }
    Cabinet {
      hidden position_6_0 6.20832514385111e-05
      hidden translation_7 -1.3590023731160017e-05 -4.2185624438140223e-10 0
      hidden rotation_7 0 0 1 6.208325207860783e-05
      translation 0.37 1.647 1.4
      rotation 0 0 1 -1.5707953071795862
      name "cabinet(4)"
      outerThickness 0.02
      rowsHeights [
        0.21, 0.21, 0.22
      ]
      columnsWidths [
        0.44
      ]
      layout [
        "RightSidedDoor (1, 1, 1, 3, 1.5)"
        "Shelf (1, 2 ,1 , 0)"
        "Shelf (1, 3 ,1 , 0)"
      ]
      handle CabinetHandle {
        translation 0 -0.2 0
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.827451 0.843137 0.811765
      }
    }
    Cabinet {
      hidden position_6_0 6.208325143829933e-05
      hidden translation_7 -1.359002373111366e-05 -4.2185624438140223e-10 0
      hidden rotation_7 0 0 1 6.208325207860783e-05
      translation -0.59 1.647 1.4
      rotation 0 0 1 -1.5707953071795862
      name "cabinet(1)"
      outerThickness 0.02
      rowsHeights [
        0.21, 0.21, 0.22
      ]
      columnsWidths [
        0.44
      ]
      layout [
        "RightSidedDoor (1, 1, 1, 3, 1.5)"
        "Shelf (1, 2 ,1 , 0)"
        "Shelf (1, 3 ,1 , 0)"
      ]
      handle CabinetHandle {
        translation 0 -0.2 0
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.827451 0.843137 0.811765
      }
    }
    Cabinet {
      hidden position_6_0 0.009995338484401563
      hidden translation_7 -0.0023371211049498034 -1.1680255506407722e-05 0
      hidden rotation_7 0 0 1 0.009995338484402651
      hidden position_10_0 7.868635172500123e-06
      hidden translation_11 0.004235771226983686 3.836892988803853e-05 0
      hidden rotation_11 0 0 1 0.018116125000697852
      hidden position_14_0 4.384120258459677e-05
      hidden translation_15 -7.415739414808987e-06 -1.6255718993107848e-10 0
      hidden rotation_15 0 0 1 4.384120165905086e-05
      translation 0.79 1.48 0.15
      rotation 0 0 1 -1.5707953071795862
      name "cabinet(5)"
      outerThickness 0.02
      rowsHeights [
        0.21, 0.21, 0.22
      ]
      columnsWidths [
        0.34, 0.47, 0.47
      ]
      layout [
        "RightSidedDoor (2, 1, 1, 3, 1.5)"
        "LeftSidedDoor (3, 1, 1, 3, 1.5)"
        "RightSidedDoor (1, 1, 1, 3, 1.5)"
        "Shelf (1, 2, 1, 0)"
        "Shelf (1, 3, 1, 0)"
        "Shelf (1, 1, 0, 3)"
        "Shelf (2, 1, 0, 3)"
      ]
      handle CabinetHandle {
        translation 0 0 0.1
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
    }
    Cabinet {
      hidden position_6_0 -5.890124471160802e-05
      hidden translation_7 -2.046934572500203e-05 7.798132695135962e-10 0
      hidden rotation_7 0 0 -1 7.619326694704964e-05
      hidden position_10_0 -6.067963033080485e-05
      hidden translation_11 -1.2980889910551455e-05 3.9383771244239085e-10 0
      hidden rotation_11 0 0 -0.9999999999999999 6.067962885481541e-05
      hidden position_14_0 6.067963034264823e-05
      hidden translation_15 -1.2980889913085047e-05 -3.9383771244239085e-10 0
      hidden rotation_15 0 0 1 6.067962885481541e-05
      translation 2.15 -0.12 0.15
      rotation 0 0 1 3.14159
      name "cabinet(7)"
      depth 0.7
      outerThickness 0.02
      rowsHeights [
        0.25, 0.25, 0.14
      ]
      columnsWidths [
        0.76, 0.54, 0.43, 0.43
      ]
      layout [
        "LeftSidedDoor (2, 1, 1, 2, 1.5)"
        "LeftSidedDoor (4, 1, 1, 3, 1.5)"
        "RightSidedDoor (3, 1, 1, 3, 1.5)"
        "Drawer (1, 1, 1, 1, 3.5)"
        "Drawer (1, 2, 1, 1, 3.5)"
        "Drawer (1, 3, 1, 1, 3.5)"
        "Drawer (2, 3, 1, 1, 3.5)"
        "Shelf (1, 2, 1, 0)"
        "Shelf (1, 3, 2, 0)"
        "Shelf (3, 2, 2, 0)"
        "Shelf (1, 1, 0, 3)"
        "Shelf (2, 1, 0, 3)"
        "Shelf (3, 1, 0, 1)"
      ]
      handle CabinetHandle {
        handleLength 0.1
        handleRadius 0.008
        handleColor 0.427451 0.513725 0.533333
      }
      primaryAppearance PaintedWood {
        colorOverride 0.2 0.2 0.2
      }
      secondaryAppearance PaintedWood {
        colorOverride 0.827451 0.843137 0.811765
      }
    }
    SolidBox {
      translation 1.55 -0.12 0.07
      name "box(1)"
      size 0.2 2.2 0.16
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 4 4
        }
      }
    }
    SolidBox {
      translation 0.65 1.08 0.07
      rotation 0 0 1 1.5708
      name "box(2)"
      size 0.2 2 0.16
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 4 4
        }
      }
    }
    Solid {
      translation 2.01 1.48 2.09
      rotation 0 0 1 0.785398
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0.8 0.8 0.8
            baseColorMap ImageTexture {
              url [
                "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/roughcast.jpg"
              ]
            }
            roughness 0.5
            metalness 0
          }
          geometry Box {
            size 0.45 0.35 0.61
          }
        }
      ]
      name "solid(1)"
    }
    SolidBox {
      translation 1.9 -1.12 0.07
      rotation 0 0 -1 4.71239
      name "box(3)"
      size 0.2 0.5 0.16
      appearance PBRAppearance {
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 4 4
        }
      }
    }
    SolidBox {
      translation -0.25 1.415 0.07
      rotation 0 0 -1 6.28318
      name "box(4)"
      size 0.2 0.47 0.16
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 4 4
        }
      }
    }
    Fridge {
      hidden position_0_0 -0.0003872985338753303
      hidden position_0_1 0.0002994009753976819
      hidden rotation_1 0 0 -1 0.00038729853356316154
      hidden rotation_2 0 0 1 0.00038729851865693446
      translation 1.8 -1.57 0
      rotation 0 0 -1 3.14159
      name "fridge(1)"
      mainColor 0.6666666666666666 0 0
    }
    Solid {
      translation 1.8 1.3 1.742
      rotation 0 0 -1 5.89049
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0.8 0.8 0.8
            baseColorMap ImageTexture {
              url [
                "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/wood.jpg"
              ]
            }
            roughness 0.5
            metalness 0
          }
          geometry Cylinder {
            height 0.1
            radius 0.35
            subdivision 8
          }
        }
      ]
      name "solid(2)"
    }
    Solid {
      translation 1.8 1.3 1.692
      rotation 0 0 1 0.261799
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0.8 0.8 0.8
            baseColorMap ImageTexture {
              url [
                "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/exhaust_hood.jpg"
              ]
            }
            roughness 0.5
            metalness 0
          }
          geometry Cylinder {
            height 0.001
            radius 0.27
            subdivision 8
          }
        }
      ]
    }
    Sink {
      translation 0.78 1.33 0.87
      rotation 0 0 1 4.71239
      name "sink(1)"
    }
    Oven {
      translation -0.11 1.24 0.17
      rotation 0 0 1 -1.5707953071795862
      name "oven(1)"
    }
    Oven {
      hidden position_0_0 4.513553406984888e-23
      translation 1.81 -0.46 0.89
      rotation 0 0 1 -3.14156
      name "oven(2)"
      mainColor 0.643137 0 0
      type "microwave"
    }
    HotPlate {
      translation 1.62912 1.14971 0.86
      rotation 0 0 -1 5.49779
      name "hot plate(1)"
    }
    Worktop {
      translation 0.345 1.31 0.86
      rotation 0 0 1 4.71239
      name "worktop(1)"
      size 0.7 0.43 0.06
    }
    Worktop {
      translation 0.78 1.531 0.86
      rotation 0 0 1 4.71239
      name "worktop(2)"
      size 0.237 0.44 0.06
    }
    Worktop {
      translation 0.780001 1.003 0.86
      rotation 0 0 1 4.71239
      name "worktop(3)"
      size 0.085 0.442 0.06
    }
    Worktop {
      translation 1.22 1.306 0.86
      rotation 0 0 -1 1.5708
      name "worktop(4)"
      size 0.69 0.44 0.06
    }
    Worktop {
      translation 1.79 0.435 0.86
      size 0.7 2.43 0.06
    }
  ]
}
StraightStairs {
  translation -4.32594 -5.824913 0
  stepSize 0.25 1.27 0.19
  stepRise 0.18
  nSteps 12
  stepAppearance CarpetFibers {
    type "synthetic"
  }
  stringerAppearance Roughcast {
    textureTransform TextureTransform {
      scale 1 2.4
    }
  }
  leftRail [
    StraightStairsRail {
      run 3
      rise 2.16
      newelHeight 0.94
      appearance BrushedAluminium {
      }
    }
  ]
  rightRail []
}
PaperBoat {
  translation -9.432886658100445 -5.4016438778455225 -3.065624999881028e-05
  rotation -5.005492427654307e-17 2.503990601465312e-17 -1 1.1088699737291734
}
BiscuitBox {
  translation -3.6496 -1.06782 0.8899509500000019
  rotation 2.123194704035967e-17 1.5513585470447956e-16 -1 0.2618053071795865
}
BiscuitBox {
  translation -6.12057 -2.46595 0.5499509500000019
  rotation 5.273497899824335e-17 -1.543755930376596e-17 -1 0.2618053071795865
  name "biscuit box(1)"
}
CerealBox {
  translation -1.3122400000000003 -3.1174 0.7598773750000043
  rotation 7.936362030285005e-16 -3.420221368211457e-17 0.9999999999999999 2.35619
}
CerealBox {
  translation -2.16698 -0.283733 0.8898773750000041
  rotation 1.0479119441246129e-17 -8.014120797115229e-18 1 -3.1415853071795787
  name "cereal box(1)"
}
CerealBox {
  translation -2.26473 -0.288104 0.8898773750000041
  rotation -0.10427667371513377 -0.22541464330335356 -0.9686664100211845 1.4171839155364487e-16
  name "cereal box(2)"
}
CerealBox {
  translation -2.39953 -0.270551 0.8898773750000041
  rotation -4.350323310640193e-18 -8.561235396265305e-18 0.9999999999999999 2.618
  name "cereal box(3)"
}
HoneyJar {
  translation -2.10201 -3.51496 0.7594693437500012
  rotation -0.9903175222566327 -0.13684683503427264 0.023326998362119503 1.0042824366231296e-15
}
JamJar {
  translation -1.64286 -2.98499 0.7594693437500012
  rotation 3.458308987177557e-16 -1.9182901092081607e-16 1 2.0944
}
WallPlug {
  translation -9.98 -0.15 0.13
  rotation 0 0 1 -1.5707953071795862
}
WallPlug {
  translation -7.73 -0.15 0.13
  rotation 0 0 1 -1.5707953071795862
  name "wall plug(1)"
}
WallPlug {
  translation -6.82 -8.01 0.13
  name "wall plug(2)"
}
WallPlug {
  translation -3.73915 -3.21929 0.13
  name "wall plug(3)"
}
WallPlug {
  translation -0.149129 -6.88575 0.13
  rotation 0 0 1 -3.1415853071795863
  name "wall plug(4)"
}
Laptop {
  hidden position_0_0 0.0001779829947381872
  hidden translation_1 -0.10002060971159243 -0.013999999999999953 0.12999191773792793
  hidden rotation_1 0.5773411840631086 -0.577441177902032 -0.5772684325751073 2.094562771662804
  translation -6.046813899061053 -3.009979632184495 0.****************
  rotation -0.0003576731219435169 -0.0003136065960110716 -0.999999886860414 1.8325887716481857
  controller "<none>"
}
CardboardBox {
  translation -0.52547 -5.50157 0.3
  rotation 0 0 1 3.14159
}
SquareManhole {
  translation -0.959202 -5.85302 0
  rotation 0 0 1 3.14159
}
PipeSection {
  translation -3.58714 -12.8957 1.2
  rotation 0 0 1 1.5708
  height 2.4
  radius 0.04
  appearance Roughcast {
  }
}
CardboardBox {
  translation -0.52547 -5.50157 0.77
  rotation 0 0 1 2.0944
  name "cardboard box(1)"
  size 0.5 0.4 0.3
}
Barbecue {
  translation -11.7017 -5.08633 0
}
WallPlug {
  translation -4.03587 -4.80291 0.13
  rotation 0 0 1 -3.1415853071795863
  name "wall plug(5)"
}
Cabinet {
  translation -3.52837 -9.4958 0
  rotation 0 0 1 3.14159
  name "cabinet 4(3)"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.32, 0.24, 0.24
  ]
  columnsWidths [
    0.81
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
  ]
}
Cabinet {
  translation -3.52837 -10.4358 0
  rotation 0 0 1 3.14159
  name "cabinet 4(6)"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.32, 0.24, 0.24
  ]
  columnsWidths [
    0.81
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
  ]
}
RubberDuck {
  translation -5.38744 -11.0827 0.****************
  rotation 2.274749596735473e-19 -1.3588908972081422e-18 1 0.7853969999999998
}
RubberDuck {
  translation -3.84199 -10.7519 0.8604806306890213
  rotation 1.5451976604012272e-17 9.650440725206216e-18 1 2.3562
  name "rubber duck(1)"
}
Cabinet {
  translation -7.2623999999999995 -6.39914 0
  rotation 0 0 1 1.5708
  name "cabinet 4(4)"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.32, 0.24, 0.24
  ]
  columnsWidths [
    0.81
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
  ]
  primaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
  secondaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
}
Cabinet {
  translation -0.180166 -4.63275 0
  rotation 0 0 1 3.14159
  name "cabinet 4(2)"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.4, 0.4, 0.4, 0.4, 0.4
  ]
  columnsWidths [
    0.4
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
    "Shelf  (1, 4, 1, 0)"
    "Shelf  (1, 5, 1, 0)"
  ]
  primaryAppearance PaintedWood {
    colorOverride 0.827451 0.843137 0.811765
  }
  secondaryAppearance PaintedWood {
    colorOverride 0.827451 0.843137 0.811765
  }
}
Cabinet {
  translation -8.2024 -6.39914 0
  rotation 0 0 1 1.5708
  name "cabinet 4(5)"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.32, 0.24, 0.24, 0.24, 0.24
  ]
  columnsWidths [
    0.96
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
    "Shelf  (1, 4, 1, 0)"
    "Shelf  (1, 5, 1, 0)"
  ]
  primaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
  secondaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
}
Book {
  translation -8.232840019344495 -6.125840018476558 0.029963212500001408
  rotation -0.6947466663305955 0.18615622268688303 0.6947466663305957 2.773493663416303
  name "book(2)"
}
Book {
  translation -5.82219995574668 -2.494350004001125 0.5599632125000015
  rotation 0.6947464887870868 0.18615754788895486 -0.6947464887870868 2.773491101783955
  name "book(3)"
}
Book {
  translation -7.380949955746678 -6.158870004001127 0.029963212500001415
  rotation 0.6947464887870869 0.18615754788895486 -0.6947464887870867 2.773491101783955
  name "book(5)"
}
Book {
  translation -9.64659993132042 -6.231480256317236 0.11996321250000137
  rotation 0.965926099200494 0.25881802658106645 6.43291379344781e-18 3.141592653589793
  name "book(4)"
}
Cabinet {
  translation -9.22241 -6.39914 0
  rotation 0 0 1 1.5708
  name "cabinet 4(1)"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.32, 0.24, 0.24, 0.24, 0.24, 0.24, 0.24
  ]
  columnsWidths [
    0.96
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
    "Shelf  (1, 4, 1, 0)"
    "Shelf  (1, 5, 1, 0)"
    "Shelf  (1, 6, 1, 0)"
    "Shelf  (1, 7, 1, 0)"
  ]
  primaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
  secondaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
}
Cabinet {
  hidden position_6_0 5.6454038587539094e-05
  hidden translation_7 -1.1234353672952852e-05 -3.171123919010199e-10 0
  hidden rotation_7 0 0 1 5.64540398663138e-05
  hidden position_10_0 -4.7994427222530907e-05
  hidden translation_11 -8.118257361574417e-06 1.948154415565284e-10 0
  hidden rotation_11 0 0 -1 4.799442649338196e-05
  translation -0.16999999999999998 -9.12 0
  rotation 0 0 1 3.14159
}
Bed {
  translation -1.657084 -11.861072 0
  rotation 0 0 1 1.5708
  name "bed(1)"
  mattressAppearance PBRAppearance {
    baseColor 0.533333 0.541176 0.521569
    roughness 1
    metalness 0
  }
  pillowAppearance PBRAppearance {
    baseColor 0.569696 0.598779 0.73666
    roughness 1
    metalness 0
  }
  blanketAppearance BlanketFabric {
    textureTransform TextureTransform {
      scale 4 4
    }
  }
}
Cabinet {
  hidden position_6_0 5.645403858647332e-05
  hidden translation_7 -1.1234353672740763e-05 -3.171123919010199e-10 0
  hidden rotation_7 0 0 0.9999999999999999 5.64540398663138e-05
  hidden position_10_0 -4.79944272213606e-05
  hidden translation_11 -8.118257361376463e-06 1.948154415565284e-10 0
  hidden rotation_11 0 0 -0.9999999999999999 4.799442649338196e-05
  translation -5.445295 -9.996606 0
  name "cabinet(9)"
  depth 0.3
  rowsHeights [
    0.7
  ]
  layout [
    "RightSidedDoor (1, 1, 1, 1, 1.5)"
    "LeftSidedDoor (2, 1, 2, 1, 1.5)"
  ]
}
Cabinet {
  hidden position_6_0 5.6454038588001526e-05
  hidden translation_7 -1.1234353673044875e-05 -3.171123919010199e-10 0
  hidden rotation_7 0 0 1 5.64540398663138e-05
  hidden position_10_0 -4.7994427221502344e-05
  hidden translation_11 -8.11825736140044e-06 1.948154415565284e-10 0
  hidden rotation_11 0 0 -0.9999999999999999 4.799442649338196e-05
  translation -5.80579 -10.1734 0
  rotation 0 0 1 3.14159
  name "cabinet(8)"
}
WoodenChair {
  translation -3.40405 -2.90904 0
  name "wooden chair(4)"
}
WoodenChair {
  translation -2.355698 -3.611985 0
  name "wooden chair(6)"
}
WoodenChair {
  translation -0.595698 -3.611985 0
  rotation 0 0 1 3.14159
  name "wooden chair(1)"
}
WoodenChair {
  translation -1.4680959999999998 -2.749503 0
  rotation 0 0 1 -1.5707953071795862
  name "wooden chair(3)"
}
WoodenChair {
  translation -1.4680959999999998 -4.449503 0
  rotation 0 0 1 1.5708
  name "wooden chair(5)"
}
RoundTable {
  translation -1.430469 -3.615372 0
}
Cookware {
  translation -1.5711588301513466 -0.45852692107794646 1.1597363168946981
  rotation 0.0004694573827513591 -0.0006485793103083239 0.9999996794772705 0.5236052372955179
}
Cookware {
  translation -3.2007809618548873 -0.5894496379250278 0.890086926620887
  rotation 0.00019478399100591938 -7.685317414852503e-05 0.9999999780763931 3.141590238319859
  name "cookware(1)"
}
Lid {
  translation -2.80078 -0.58945 0.8869883506250005
  rotation 0.32899016599334857 -0.9443323015289755 0.0014053357808550431 1.551506852659972e-14
}
WoodenSpoon {
  translation -2.97078055358911 -0.7894499999985337 0.889235468369911
  rotation 0.0003639311791552709 -4.828619228314494e-10 0.9999999337770462 3.1415900000006665
}
WoodenChair {
  translation -6.88 -12.059999999999999 0
  rotation 0 0 1 -1.5707953071795862
  name "wooden chair(2)"
}
Bed {
  translation -8.67447 -10.2627 0
}
Desk {
  translation -6.905085 -12.493314 0
  rotation 0 0 1 -1.5707963071795863
  name "desk(2)"
}
Wall {
  translation 0 -0.65 0
  name "wall 1"
  size 0.3 1 2.4
}
Window {
  translation 0 -1.65 0
  name "window 1"
  size 0.3 1 2.4
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation -9.9 -7.46 0
  rotation 0 0 1 -3.1415923071795864
  name "window 1(3)"
  size 0.3 0.7 2.4
  bottomWallHeight 1.4
  windowHeight 0.8
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation -4.4 -13.12 0
  rotation 0 0 1 1.570797
  name "window 1(7)"
  size 0.3 0.7 2.4
  bottomWallHeight 1.4
  windowHeight 0.8
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation -7.95 -13.12 0
  rotation 0 0 1 -1.5707963071795863
  name "window 1(1)"
  size 0.3 1 2.4
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation 0 -10.87 0
  name "window 1(4)"
  size 0.3 1 2.4
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation -11.15 -3.48 0
  rotation 0 0 1 1.570797
  name "window 1(2)"
  size 0.3 2.2 2.4
  bottomWallHeight 0
  windowHeight 2.2
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation -9.9 -4.73 0
  name "window 1(6)"
  size 0.3 2.2 2.4
  bottomWallHeight 0
  windowHeight 2.2
  frameAppearance BrushedAluminium {
  }
}
Window {
  translation -8.25 0 0
  rotation 0 0 1 1.570797
  name "window 1(5)"
  size 0.3 1 2.4
  frameAppearance BrushedAluminium {
  }
}
Radiator {
  translation -4.24028 -8.71399 0.36
  rotation 0 0 1 -1.5707953071795862
}
Radiator {
  translation -0.28465799999999997 -10.58667 0.36
  rotation 0 0 1 3.14159
  name "radiator(5)"
}
Radiator {
  translation -3.23759 -6.88139 0.36
  rotation 0 0 1 -1.5707953071795862
  name "radiator(4)"
  numberOfFins 20
}
Radiator {
  translation -7.731897999999999 -12.834878 0.36
  rotation 0 0 -1 -1.5707953071795862
  name "radiator(3)"
}
Radiator {
  translation -8.531595 -0.282246 0.36
  rotation 0 0 1 -1.5707953071795862
  name "radiator(2)"
}
DesktopComputer {
  translation -7.25806 -12.3974 0
  rotation 0 0 1 1.5707959999999999
}
Keyboard {
  translation -6.961739 -12.493036 0.71
  rotation 0 0 -1 -1.3089963071795863
}
Monitor {
  translation -6.9690579999999995 -12.713728999999999 0.71
  rotation 0 0 1 1.5707959999999999
}
ComputerMouse {
  translation -7.181789990485555 -12.288086838807072 0.7099877375001017
  rotation -1.4094191081748514e-06 1.8366025516671252e-06 -0.9999999999973201 1.832496742870322
}
Radiator {
  translation -0.27999999999999997 -1.53 0.36
  rotation 0 0 1 3.14159
  name "radiator(1)"
}
Wall {
  translation 0 -4.57 0
  name "wall 2"
  size 0.3 4.85 2.4
}
Wall {
  translation 0 -12.32 0
  name "wall 2(1)"
  size 0.3 1.9 2.4
}
Wall {
  translation 0 -9.18 0
  name "wall 2(2)"
  size 0.3 2.4 2.4
}
Wall {
  translation -9.9 -3.48 0
  name "wall 3(3)"
  size 0.3 0.3 2.4
}
Wall {
  translation -9.9 -10.41 0
  name "wall 3(1)"
  size 0.3 5.2 2.4
}
Wall {
  translation -9.9 -6.47 0
  name "wall 3(4)"
  size 0.3 1.28 2.4
}
Wall {
  translation -3.8 0 0
  rotation 0 0 1 1.5708
  name "wall 5"
  size 0.3 7.9 2.4
}
Wall {
  translation -10.65 0 0
  rotation 0 0 1 1.5708
  name "wall 6"
  size 0.3 3.8 2.4
}
Wall {
  translation -12.399999999999999 -1.88 0
  name "wall 3(2)"
  size 0.3 3.5 2.4
}
Wall {
  translation -6.969989999999999 -8.139999999999999 0
  name "wall 7"
  size 0.3 0.8 2.4
}
Wall {
  translation -8.42 -6.6 0
  rotation 0 0 1 -1.5707933071795868
  name "wall 7(2)"
  size 0.3 3.2 2.4
}
Wall {
  translation -3.369987 -10.679991 0
  rotation 0 0 1 -3.1415893071795864
  name "wall 7(6)"
  size 0.3 4.6 2.4
}
Wall {
  translation -5.58999 -10.73 0
  rotation 0 0 1 -3.1415893071795864
  name "wall 7(9)"
  size 0.3 4.9 2.4
}
Wall {
  translation -9.25 -13.12 0
  rotation 0 0 1 1.5708
  name "wall 7(7)"
  size 0.3 1.6 2.4
}
Wall {
  translation -6.1 -13.12 0
  rotation 0 0 1 1.5708
  name "wall 7(12)"
  size 0.3 2.7 2.4
}
Wall {
  translation -2.1 -13.12 0
  rotation 0 0 1 1.5708
  name "wall 7(13)"
  size 0.3 3.9 2.4
}
Wall {
  translation -8.42 -8.43 0
  rotation 0 0 1 -1.5707933071795868
  name "wall 7(3)"
  size 0.3 3.2 2.4
}
Door {
  hidden translation_5 0.0016261499416398706 -0.00019727789492124392 6.661338147750939e-16
  hidden rotation_5 0 0 1 0.0013179365287545593
  translation -6.24 -8.43 0
  rotation 0 0 1 -1.5707953071795862
  size 0.3 1 2.4
  position 0.0013179365287050375
  jointAtLeft FALSE
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
  doorHandle DoorLever {
    jointAtLeft FALSE
  }
}
Door {
  hidden translation_5 0.6128076848923888 -0.2882952540123004 2.4424906541753444e-15
  hidden rotation_5 0 0 -1 1.4924750624759948
  translation -3.89 -3.95 0
  name "door(6)"
  size 0.3 1 2.4
  position -1.4924750624759948
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
}
Door {
  hidden translation_5 0.5542231030100574 -0.18253439655415815 8.881784197001252e-16
  hidden rotation_5 0 0 -1 1.2491575763782836
  translation -4.94 -8.43 0
  rotation 0 0 1 -1.5707953071795862
  name "door(5)"
  size 0.3 1 2.4
  position -1.2491575763782836
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
}
Door {
  hidden translation_5 0.0016261499416389547 0.00019727789492052228 6.661338147750939e-16
  hidden rotation_5 0 0 -1 0.0013179365287545593
  translation -6.97 -7.24 0
  rotation 0 0 1 3.14159
  name "door(3)"
  size 0.3 1 2.4
  position -0.0013179365287029356
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
}
Door {
  hidden translation_5 0.0016261499416385106 0.00019727789492041126 6.661338147750939e-16
  hidden rotation_5 0 0 -1 0.0013179365287545593
  translation -0.91 -6.6 0
  rotation 0 0 1 1.5708
  name "door(4)"
  size 0.3 1 2.4
  position -0.0013179365287020687
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
}
Door {
  hidden translation_5 0.0016261499416382053 0.00019727789492035575 6.661338147750939e-16
  hidden rotation_5 0 0 -1 0.0013179365287545593
  translation 0 -7.49 0
  name "door(1)"
  size 0.3 1 2.4
  position -0.001317936528701552
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
}
Door {
  hidden translation_5 0.5582513774315316 -0.18817023240235342 1.4432899320127035e-15
  hidden rotation_5 0 0 -1 1.2630648495800056
  translation -2.14977 -8.43 0
  rotation 0 0 1 -1.5707953071795862
  name "door(2)"
  size 0.3 1 2.4
  position -1.2630648495800056
  frameSize 0.001 0.05 0.05
  frameAppearance BrushedAluminium {
  }
}
Wall {
  translation -2.93 -6.6 0
  rotation 0 0 1 1.5708
  name "wall 7(1)"
  size 0.3 3.045 2.4
}
Wall {
  translation -0.29 -6.6 0
  rotation 0 0 1 1.5708
  name "wall 7(14)"
  size 0.3 0.3 2.4
}
Wall {
  translation -1.94 -5.02 0
  rotation 0 0 1 1.5708
  name "wall 7(8)"
  size 0.3 3.6 2.4
}
Wall {
  translation -3.837798 -5.2702 0.26217999999999997
  rotation 0 -1 0 3.77
  name "wall 7(10)"
  size 1 0.2 0.4
}
Wall {
  translation -0.8 -8.43 0
  rotation 0 0 1 1.5708
  name "wall 7(4)"
  size 0.3 1.7 2.4
}
Wall {
  translation -3.55 -8.43 0
  rotation 0 0 1 1.5708
  name "wall 7(5)"
  size 0.3 1.8 2.4
}
Wall {
  translation -6.78 -8.43 0
  rotation 0 0 1 1.5708
  name "wall 7(11)"
  size 0.3 0.08 2.4
}
Wall {
  translation -3.89 -1.8 0
  name "wall 9"
  size 0.3 3.3 2.4
}
Wall {
  translation -3.89 -4.81 0
  name "wall 9(1)"
  size 0.3 0.72 2.4
}
CeilingLight {
  translation -1.3341 -2.47061 2.4
  name "ceiling light 1"
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightRadius 3.9
}
CeilingLight {
  translation -10.1237 -1.47922 2.4
  name "ceiling light 2"
  pointLightIntensity 2
  pointLightRadius 4
}
CeilingLight {
  translation -7.411517 -4.344069 2.4
  name "ceiling light 2(5)"
  pointLightIntensity 4
  pointLightRadius 10
}
CeilingLight {
  translation -1.7941829999999999 -7.551591999999999 2.4
  name "ceiling light 2(1)"
  pointLightColor 0.447059 0.623529 0.811765
  pointLightIntensity 3
  pointLightRadius 4
}
CeilingLight {
  translation -5.364183 -7.551591999999999 2.4
  name "ceiling light 2(2)"
  pointLightColor 0.447059 0.623529 0.811765
  pointLightIntensity 3
  pointLightRadius 4
}
CeilingLight {
  translation -7.622198 -10.602905999999999 2.4
  name "ceiling light 2(3)"
  pointLightIntensity 3
  pointLightRadius 5
}
CeilingLight {
  translation -1.945004 -10.56734 2.4
  name "ceiling light 2(4)"
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightRadius 5
}
FloorLight {
  translation -4.7700309999999995 -0.49615699999999996 0
  pointLightColor 0.913725 0.72549 0.431373
  pointLightRadius 3
}
FloorLight {
  translation -0.590395 -12.404582999999999 0
  name "floor light(1)"
  pointLightRadius 2
}
Pose {
  translation -9.51375 -11.8794 0.4
  children [
    FloorLight {
      translation -0.08583 0.580924 0
      name "floor light(2)"
      bulbColor 0.937255 0.16078399999999998 0.16078399999999998
      scale 0.4
      pointLightColor 1 0.5799949999999999 0.5799949999999999
      pointLightIntensity 0.7
      pointLightRadius 3
    }
  ]
}
Pose {
  translation -2.73375 -12.6994 0.4
  children [
    FloorLight {
      translation 0 -0.08 0
      name "floor light(4)"
      scale 0.4
      pointLightIntensity 0.7
      pointLightRadius 3
    }
  ]
}
Pose {
  translation -9.51375 -9.6494 0.4
  children [
    FloorLight {
      translation 0 0.524 0
      name "floor light(3)"
      bulbColor 0.937255 0.16078399999999998 0.16078399999999998
      scale 0.4
      pointLightColor 1 0.5799949999999999 0.5799949999999999
      pointLightIntensity 0.7
      pointLightRadius 3
    }
  ]
}
Table {
  translation -10.415072 -1.632191 0
  rotation 0 0 1 1.5708
  name "table(1)"
  size 1 2.5 0.74
}
Carafe {
  translation -11.138300159239154 -1.5941306066487548 0.7389543406254432
  rotation -0.00024388927299466657 -0.00018959046605571505 -0.9999999522867375 1.8325953510632795
}
Plate {
  translation -11.5061 -1.63786 0.7399816062500006
  rotation -0.9999332095308366 -0.011557514204586443 1.851406476499948e-05 1.2201812878220364e-14
}
Plate {
  translation -2.28227 -3.63168 0.7599816062500007
  rotation -0.09022159461975719 -0.994496780098896 0.05325615680084381 2.469127704176551e-16
  name "plate(9)"
}
Plate {
  translation -1.44694 -2.75324 0.7599816062500007
  rotation -0.4727229800649003 -0.8810800141967577 -0.015198444051971157 2.721407584239081e-16
  name "plate(10)"
}
Knife {
  translation -2.286861383271536 -3.7512697805705635 0.7591301721217537
  rotation 0.9999874025195279 9.293967379848035e-07 -0.005019442337976039 -3.1415807557545463
}
Glass {
  translation -1.51333 -2.90459 0.7589895768750005
  rotation 0.8739974145359212 -0.38404179512536357 -0.2977254086929992 1.8099377981109444e-15
}
WaterBottle {
  translation -1.7021799999999998 -3.2776 0.7579386875000023
  rotation 0.3601934476810349 -0.8707492099563321 -0.33474840344365075 1.2554230653606838e-15
}
Glass {
  translation -2.13663 -3.69948 0.7589895768750005
  rotation 0.10601875938149863 0.984464049119347 -0.13996627683391408 1.8502058685298833e-15
  name "glass(8)"
}
Knife {
  translation -1.5752397789123274 -2.7672886196459605 0.7591301721217537
  rotation 0.7071034998739092 -0.7071011757388369 -0.003545100114662439 -3.1344856672254684
  name "knife(1)"
}
Fork {
  translation -2.2610488787957643 -3.511019999999299 0.7611587165091641
  rotation 7.272578620265675e-12 -1 2.3610871011545304e-09 0.007021169650312643
}
Fork {
  translation -1.3232600000076502 -2.7678411212042175 0.7611587165091641
  rotation -0.003510555982007215 -0.00351055956137947 -0.9999876759081899 1.5708076313582462
  name "fork(1)"
}
Plate {
  translation -9.3661 -1.63786 0.7399816062500006
  rotation 0.03903082424233614 -0.9992377670677272 0.0006925492520717548 1.0886210158098402e-14
  name "plate(8)"
}
Plate {
  translation -11.1892 -1.29784 0.7399816062500006
  rotation -0.999972758258671 0.007328030175993588 0.0008847114134361225 1.1945624293180548e-14
  name "plate(2)"
}
Plate {
  translation -11.1892 -1.92784 0.7399816062500006
  rotation -0.9997824377561453 -0.020225381109180707 -0.005100109152545282 8.822542345224561e-15
  name "plate(3)"
}
Plate {
  translation -10.4792 -1.92784 0.7399816062500006
  rotation 0.9642156527392012 0.2650561427980813 -0.005780672773933656 6.122038262787734e-15
  name "plate(4)"
}
Plate {
  translation -10.4792 -1.30784 0.7399816062500006
  rotation -0.9999332095308366 -0.011557514204586443 1.851406476499948e-05 1.2201812878220364e-14
  name "plate(5)"
}
Plate {
  translation -9.7692 -1.30784 0.7399816062500006
  rotation -0.9717732305503428 0.2359168616530313 0.00015090902763949737 1.2291837837938903e-14
  name "plate(6)"
}
Plate {
  translation -9.7692 -1.94784 0.7399816062500006
  rotation -0.8099532913829396 0.5864939200016285 -0.0007399858553992741 8.083491413591317e-15
  name "plate(7)"
}
Wineglass {
  translation -9.6817 -1.8196700000000028 0.7395877375000006
  rotation -0.9945555285482333 0.09316965742600543 -0.04667671335114448 3.092304157738462e-14
}
Wineglass {
  translation -9.49352 -1.5504800000000016 0.7395877375000006
  rotation -0.9560260730531666 0.2667373160786749 -0.12192354921706833 1.8278169755898324e-14
  name "wine glass(7)"
}
Wineglass {
  translation -10.3817 -1.8196700000000028 0.7395877375000006
  rotation -0.991139866824437 0.11188810689612176 -0.07157384945941554 3.075850143501983e-14
  name "wine glass(1)"
}
Wineglass {
  translation -11.0617 -1.8196700000000026 0.7395877375000006
  rotation -0.9921892458200701 0.11314877880404557 -0.0525152771501192 3.0075412399937676e-14
  name "wine glass(2)"
}
Wineglass {
  translation -11.405 -1.7557100000000014 0.7395877375000006
  rotation -0.9934860827469872 -0.11356465550404851 -0.009405977264118213 1.4057530736842851e-14
  name "wine glass(3)"
}
Wineglass {
  translation -11.3017 -1.4196700000000042 0.7395877375000006
  rotation -0.9990109530543365 -0.03472298039632978 -0.02777463428854061 4.7390629150528274e-14
  name "wine glass(6)"
}
Wineglass {
  translation -10.6317 -1.4196700000000029 0.7395877375000006
  rotation -0.9898879814740269 0.12412966708455458 -0.06865573452198978 3.2249532799873805e-14
  name "wine glass(4)"
}
Wineglass {
  translation -9.8817 -1.4196700000000029 0.7395877375000006
  rotation -0.9996623375726228 0.004204878368658588 -0.025642344603109563 3.047568477606122e-14
  name "wine glass(5)"
}
Carafe {
  translation -9.78070955552515 -1.6415395589919612 0.7389542888181949
  rotation 0.00024071596323704017 -0.0005911128315270639 0.9999997963207019 0.7853981398188705
  name "carafe(1)"
}
Table {
  translation -9.55609 -9.09224 0
  name "table(2)"
  size 0.3 0.3 0.4
  feetSize 0.04 0.05
  legAppearance BrushedAluminium {
  }
}
Table {
  translation -9.56036 -11.3804 0
  name "table(3)"
  size 0.3 0.3 0.4
  feetSize 0.04 0.05
  legAppearance BrushedAluminium {
  }
}
Table {
  translation -2.74109 -12.7454 0
  name "table(4)"
  size 0.3 0.3 0.4
  feetSize 0.04 0.05
  legAppearance BrushedAluminium {
  }
}
Book {
  translation -2.7337499885433405 -12.63939444997725 0.40996071482199764
  rotation -0.7071067800658808 5.630025729185611e-05 0.7071067800658847 -3.1414774077557315
}
Chair {
  translation -11.745298 -1.636645 -0.0014028299999955252
  rotation -0.007942946184363914 0.15772886393006622 0.9874505127292419 4.1338411172456553e-16
  name "chair 1(1)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -11.172214 -1.29268 -0.0014028299999955807
  rotation 4.0524678641545837e-19 -3.673873829569826e-20 -0.9999999999999999 1.5707953071795862
  name "chair 1(2)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -11.160617 -1.997738 -0.0014028299999955252
  rotation -3.098319504831045e-17 -3.154108576501763e-17 1 1.570705307179586
  name "chair 1(5)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -10.450617 -1.997809 -0.0014028299999955252
  rotation -3.280367664176213e-17 -2.864008042001776e-17 1 1.570705307179586
  name "chair 1(6)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -9.750617 -1.997879 -0.0014028299999955252
  rotation -3.280367664176213e-17 -2.864008042001776e-17 1 1.570705307179586
  name "chair 1(7)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -9.774799 -1.2626819999999999 -0.0014028299999955807
  rotation 1.5652669674379937e-18 -2.1666139774897036e-18 -0.9999999999999999 1.5707953071795862
  name "chair 1(3)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -10.485493 -1.2858399999999999 -0.0014028299999955807
  rotation 1.5652669674379937e-18 -2.1666139774897036e-18 -0.9999999999999999 1.5707953071795862
  name "chair 1(4)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
Chair {
  translation -9.203993745096895 -1.5946675079417745 -0.0014028299999956362
  rotation 2.9334563762636207e-17 -5.652961843516032e-19 0.9999999999999999 -3.139980018150082
  name "chair 4(1)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
  physics Physics {
    centerOfMass [
      0 0 0.45
    ]
  }
}
FruitBowl {
  translation -10.127322797715369 -1.6417478650055675 0.7399019000000158
  rotation -9.826278986680476e-05 -0.00020990203355472832 -0.9999999731427799 0.9448573937270819
  color 0.541176 0.886275 0.203922
}
Apple {
  translation -10.207307174068577 -1.6288860082554126 0.799815848090535
  rotation 0.3248299970584636 -0.9087761871002157 -0.2619376161810142 0.48988848403783597
}
Orange {
  translation -1.4360132825400325 -3.5077282900457054 0.8198080103758457
  rotation -0.11421083639669045 0.9904654842704587 0.07703252117420356 0.3462426590914182
}
Orange {
  translation -1.4584349167574013 -3.647360804736548 0.8198080024128921
  rotation 0.5449002328192941 0.5700636495461188 0.6149074497350305 0.8215137082555553
  name "orange(2)"
}
Orange {
  translation -1.3774077701575305 -3.588755376081472 0.8197948639397019
  rotation 0.3330654469639277 0.8509618985912218 0.4061172923980732 0.1849066912940436
  name "orange(3)"
}
Orange {
  translation -10.117673112901464 -1.5640883696367203 0.799816654826191
  rotation -0.8093873947098146 -0.3815209215499001 -0.44646817546671075 1.4170732933667205
  name "orange(1)"
}
FruitBowl {
  translation -1.4573954094671517 -3.57590093599676 0.7598835062499838
  rotation -6.323552385847404e-05 0.00019553006181244838 -0.9999999788846317 0.9446150396485713
  name "fruit bowl(1)"
  color 0.6666666666666666 0 0
}
Table {
  translation -11.0871 -6.84245 0.02
  rotation 0 0 1 -1.5707953071795862
  size 0.8 1.2 0.53
  trayAppearance BrushedAluminium {
  }
  legAppearance BrushedAluminium {
  }
}
Table {
  translation -6.014093 -2.658387 0.02
  name "table(5)"
  size 0.8 1.2 0.53
}
BeerBottle {
  translation -6.25675 -2.276679999999996 0.5504754750000009
  rotation 2.0439179271409723e-14 -2.1742377466204538e-14 1 1.5708000000000004
}
Can {
  translation -6.28676 -2.55469 0.6110785406250008
  rotation 0.3736778740830942 0.9267716037383508 -0.03820001210712585 5.627412420982475e-15
}
BeerBottle {
  translation -5.83642 -2.1972500000000075 0.5504754750000009
  rotation 5.252274045378371e-15 3.760965860354575e-14 1 -2.8797953071795805
  name "beer bottle(1)"
}
BeerBottle {
  translation -6.31964 -2.887870000000008 0.5504754750000009
  rotation -3.0016673602785066e-13 2.416717107557987e-14 1 0.261797
  name "beer bottle(2)"
}
Armchair {
  translation -2.57082 -9.80788 0
  rotation 0 0 1 -0.2115153071795861
  name "armchair(1)"
  color 0.827451 0.843137 0.811765
}
Armchair {
  translation -6.495947 -4.354153999999999 0
  rotation 0 0 -1 -1.3592853071795865
  name "armchair(4)"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
}
Armchair {
  translation -9.0281 -12.1858 0
  rotation 0 0 -1 -0.5738853071795864
  name "armchair(3)"
  color 0.827451 0.843137 0.811765
}
Sofa {
  translation -5.9207339999999995 -0.6887059999999999 0
  rotation 0 0 1 -1.5707953071795862
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
}
Sofa {
  translation -7.308742 -2.513508 0
  name "sofa 2"
  color 0.13333333333333333 0.13333333333333333 0.13333333333333333
}
Sofa {
  translation -11.1117 -8.1052 0
  rotation 0 0 1 1.5708
  name "sofa 2(1)"
  color 0.827451 0.843137 0.811765
}
Carpet {
  translation -5.803488 -2.55795 -0.015
  rotation 0 0 1 -1.5707953071795862
  color 0.13725490196078433 0.13725490196078433 0.13725490196078433
}
BunchOfSunFlowers {
  translation -7.069851 -0.484029 0
}
Cabinet {
  translation -4.073264 -0.6770999999999999 0
  rotation 0 0 1 3.14159
  name "cabinet 4"
  depth 0.4
  outerThickness 0.02
  rowsHeights [
    0.52, 0.44, 0.44, 0.44, 0.44
  ]
  columnsWidths [
    0.96
  ]
  layout [
    "Shelf  (1, 2, 1, 0)"
    "Shelf  (1, 3, 1, 0)"
    "Shelf  (1, 4, 1, 0)"
    "Shelf  (1, 5, 1, 0)"
  ]
  primaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
  secondaryAppearance PaintedWood {
    colorOverride 0.13333333333333333 0.13333333333333333 0.13333333333333333
  }
}
PottedTree {
  translation -11.870923999999999 -0.53674 0
}
PottedTree {
  translation -6.025363 -12.409485 0
  name "potted tree(1)"
}
PortraitPainting {
  translation -12.209999999999999 -1.7999999999999998 1.5999999999999999
}
PortraitPainting {
  translation -3.28921 -6.76837 1.6
  rotation 0 0 1 -1.5707953071795862
  name "portrait painting(1)"
}
LandscapePainting {
  translation -10.3223 -0.178447 1.62868
  rotation 0 0 1 -1.5707953071795862
}
LandscapePainting {
  translation -2.295442 -4.8501449999999995 1.62868
  rotation 0 0 1 1.5708
  name "landscape painting(1)"
}
Television {
  translation -4.04287 -2.45471 1.5595999999999999
  rotation 0 0 1 3.14159
  controller "<none>"
}
FireExtinguisher {
  translation -0.44006826786189435 -8.158107107196612 -0.0014715004212821903
  rotation 0.0006419367713871949 0.0008158001546852411 0.9999994611934995 2.0943957766053765
}
Clock {
  translation -6.165 -0.155075 1.72
  rotation 0 0 1 -1.5707953071795862
}
OfficeTelephone {
  translation -4.496782 -6.58 1.0611279999999998
  rotation -0.2907400269219462 0 0.9568020885979639 3.14159
  enablePhysics FALSE
}
WashingMachine {
  translation -2.00701 -5.63126 0.51
}
BathroomSink {
  translation -5.443874 -9.989588999999999 1.07
  name "sink(2)"
}
Bathtube {
  translation -4.96001 -11.9954 0
}
Toilet {
  translation -9.266226999999999 -7.456646999999999 0
}
Toilet {
  translation -3.97349 -12.5259 0
  rotation 0 0 1 1.57079
  name "toilet(1)"
  lidColor 1 0.721569 0.721569
}
BathroomSink {
  translation -8.35 -6.75 1.07
  rotation 0 0 1 -1.5707953071795862
}
CeilingSpotLight {
  translation -7.72 -7.51 2.36
  spotLightIntensity 5
  spotLightRadius 3
}
CeilingSpotLight {
  translation -4.28 -6.46 0.22999999999999998
  rotation -1 0 0 -1.5707963071795863
  name "ceiling light(5)"
  spotLightIntensity 2
  spotLightRadius 2
}
CeilingSpotLight {
  translation -3.78 -6.46 0.61
  rotation -1 0 0 -1.5707963071795863
  name "ceiling light(6)"
  spotLightIntensity 2
  spotLightRadius 2
}
CeilingSpotLight {
  translation -3.02 -6.46 1.14
  rotation -1 0 0 -1.5707963071795863
  name "ceiling light(7)"
  spotLightIntensity 2
  spotLightRadius 2
}
CeilingSpotLight {
  translation -2.29 -6.46 1.69
  rotation -1 0 0 -1.5707963071795863
  name "ceiling light(8)"
  spotLightIntensity 2
  spotLightRadius 2
}
CeilingSpotLight {
  translation -4.52 -9.1 2.36
  name "ceiling light(2)"
  spotLightIntensity 5
  spotLightRadius 3
}
CeilingSpotLight {
  translation -4.52 -10.4 2.36
  name "ceiling light(3)"
  spotLightIntensity 5
  spotLightRadius 3
}
CeilingSpotLight {
  translation -4.52 -11.68 2.36
  name "ceiling light(4)"
  spotLightIntensity 5
  spotLightRadius 3
}
CeilingSpotLight {
  translation -8.7 -7.51 2.36
  name "ceiling light(1)"
  spotLightIntensity 5
  spotLightRadius 3
}
DEF TREES Group {
  children [
    Cypress {
      translation -12.53 -4.26 0
      rotation 0 0 1 -0.5235983071795864
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -4.76 0
      name "cypress tree(1)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -5.279999999999999 0
      rotation 0 0 1 0.7853979999999999
      name "cypress tree(2)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -5.85 0
      rotation 0 0 1 -0.26179930717958655
      name "cypress tree(3)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -6.37 0
      rotation 0 0 1 -1.0471973071795864
      name "cypress tree(4)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -6.85 0
      rotation 0 0 1 1.308997
      name "cypress tree(5)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -7.319999999999999 0
      rotation 0 0 1 -0.7853983071795865
      name "cypress tree(6)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -7.9799999999999995 0
      rotation 0 0 1 0.5235989999999999
      name "cypress tree(7)"
      scale 0.6
      enableBoundingObject FALSE
    }
    Cypress {
      translation -12.53 -8.5 0
      name "cypress tree(8)"
      scale 0.6
      enableBoundingObject FALSE
    }
  ]
}
Solid {
  translation -4.1046 -3.17 1.29322
  rotation -0.5773469358512743 0.5773529358506075 0.5773509358508296 -2.094395307179586
  children [
    Shape {
      appearance Appearance {
        material Material {
        }
        texture ImageTexture {
          url [
            "textures/tag36h11_000.jpg"
          ]
        }
      }
      geometry Plane {
        size 0.2 0.2
      }
    }
  ]
  name "tag36h11_000"
}
Solid {
  translation -8.2846 -0.4 1.20322
  rotation -0.9999999999911531 2.3757199999789822e-06 3.4712999999692897e-06 -1.5707953071795862
  children [
    Shape {
      appearance Appearance {
        material Material {
        }
        texture ImageTexture {
          url [
            "textures/tag36h11_027.jpg"
          ]
        }
      }
      geometry Plane {
        size 0.2 0.2
      }
    }
  ]
  name "tag36h11_027.jpg"
}
Solid {
  translation -0.3046 -7.49001 1.20322
  rotation -0.5773476025217157 0.5773516025189619 0.5773516025189619 -2.094395307179586
  children [
    Shape {
      appearance Appearance {
        material Material {
        }
        texture ImageTexture {
          url [
            "textures/tag36h11_028.jpg"
          ]
        }
      }
      geometry Plane {
        size 0.2 0.2
      }
    }
  ]
  name "tag36h11_028.jpg"
}
Solid {
  translation -4.2146 -6.83001 1.20322
  rotation 0.9999999999931786 -1.2753299999913004e-06 -3.4664999999763536e-06 1.57079
  children [
    Shape {
      appearance Appearance {
        material Material {
        }
        texture ImageTexture {
          url [
            "textures/tag36h11_050.jpg"
          ]
        }
      }
      geometry Plane {
        size 0.2 0.2
      }
    }
  ]
  name "tag36h11_050"
}
Solid {
  translation -8.7096 -2.99042 0.949171
  rotation -0.6786000574733556 -0.2810860238063007 -0.6785960574730169 -1.096045307179586
  children [
    Shape {
      appearance Appearance {
        material Material {
        }
        texture ImageTexture {
          url [
            "textures/tag36h11_120.jpg"
          ]
        }
      }
      geometry Plane {
        size 0.2 0.2
      }
    }
  ]
  name "tag36h11_120"
}
Cruzr {
  translation -7.194 -5.35957 -3.55271e-15
  controller "webots_rosa"
  controllerArgs [
    "cruzr.yaml"
  ]
}
