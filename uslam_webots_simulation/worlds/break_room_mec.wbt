#VRML_SIM R2025a utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/floors/protos/Floor.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Parquetry.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/lights/protos/CeilingSpotLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/lights/protos/CeilingLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Wall.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Roughcast.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Window.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/MattePaint.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Door.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/living_room_furniture/protos/Sofa.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/plants/protos/PottedTree.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/cabinet/protos/Cabinet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/cabinet/protos/CabinetHandle.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/GlossyPaint.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/solids/protos/SolidBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/kitchen/components/protos/Sink.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/tables/protos/Table.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/VarnishedPine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/Monitor.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/computers/protos/Keyboard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/chairs/protos/OfficeChair.proto"
EXTERNPROTO "../protos/MecanumBotLidar.proto"

WorldInfo {
  info [
    "An office break room, surrounded by office desks."
    "This world contains the domestic objects included in Webots."
    "Doors are interactive."
    "This means that a robot could open a drawer, leave an object inside it, and close it."
  ]
  title "Break Room"
  basicTimeStep 16
  FPS 30
  contactProperties [
    ContactProperties {
      coulombFriction [
        -1
      ]
    }
        ContactProperties {
      material1 "ExteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation 0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
    ContactProperties {
      material1 "InteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation -0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
  ]
}
Viewpoint {
  orientation 0.14458679055488113 0.8033980283232004 -0.5776212150565743 1.1939816942687311
  position -11.381475495075136 13.675105102877673 25.694963934217302
}
TexturedBackground {
  texture "entrance_hall"
  luminosity 0.5
  skybox FALSE
}
TexturedBackgroundLight {
  texture "entrance_hall"
}
Floor {
  translation 0.243 0.93 0
  rotation 0 0 1 1.5708
  size 7.7 12.86
  appearance Parquetry {
    type "light strip"
    textureTransform TextureTransform {
      rotation 1.57
      scale 0.4 0.4
    }
  }
}
CeilingSpotLight {
  translation -1.82 -2.27 2
  name "ceiling spot light"
  spotLightColor 0.913725 0.72549 0.431373
  spotLightIntensity 4
  spotLightCastShadows TRUE
}
CeilingLight {
  translation 4.78 -0.87 2.5
  bulbColor 0.913725 0.72549 0.431373
  supportColor 0.533333 0.541176 0.521569
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightCastShadows TRUE
}
CeilingLight {
  translation 4.78 2.74 2.5
  name "ceiling light(5)"
  bulbColor 0.913725 0.72549 0.431373
  supportColor 0.533333 0.541176 0.521569
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightCastShadows TRUE
}
CeilingLight {
  translation 0.31 -0.87 2.5
  name "ceiling light(1)"
  bulbColor 0.913725 0.72549 0.431373
  supportColor 0.533333 0.541176 0.521569
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightCastShadows TRUE
}
CeilingLight {
  translation 0.31 2.74 2.5
  name "ceiling light(4)"
  bulbColor 0.913725 0.72549 0.431373
  supportColor 0.533333 0.541176 0.521569
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightCastShadows TRUE
}
CeilingLight {
  translation -3.52 -0.87 2.5
  name "ceiling light(2)"
  bulbColor 0.913725 0.72549 0.431373
  supportColor 0.533333 0.541176 0.521569
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightCastShadows TRUE
}
CeilingLight {
  translation -3.52 2.74 2.5
  name "ceiling light(3)"
  bulbColor 0.913725 0.72549 0.431373
  supportColor 0.533333 0.541176 0.521569
  pointLightColor 0.913725 0.72549 0.431373
  pointLightIntensity 3
  pointLightCastShadows TRUE
}
Wall {
  translation -2.65 0 0
  size 0.05 5 0.62
  appearance DEF SMALL_WALLS Roughcast {
    colorOverride 0.890196 0.803922 1
    textureTransform TextureTransform {
      scale 1 2.4
    }
  }
}
Wall {
  translation 2.65 -0.77 0
  name "wall(1)"
  size 0.05 3.55 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation 0 -2.52 0
  rotation 0 0 1 1.5708
  name "wall(2)"
  size 0.05 5.35 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation -1.65 2.524 0
  rotation 0 0 1 1.5708
  name "wall(3)"
  size 0.05 2.05 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation -0.65 3.05 0
  rotation 0 0 1 -3.14159
  name "wall(4)"
  size 0.05 1 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation 0.4 3.05 0
  rotation 0 0 1 -3.14159
  name "wall(5)"
  size 0.05 1 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation 1.876 2.524 0
  rotation 0 0 1 1.5708
  name "wall(6)"
  size 0.05 3 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation 3.026 0.979 0
  rotation 0 0 1 1.5708
  name "wall(7)"
  size 0.05 0.7 0.62
  appearance USE SMALL_WALLS
}
Wall {
  translation -6.17 0.92 0
  name "wall(A)"
  size 0.06 7.7 2.5
  appearance DEF SIDE_WALLS Roughcast {
    textureTransform TextureTransform {
      scale 3 3
    }
  }
}
Wall {
  translation -5.94 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(B)1"
  size 0.06 0.4 2.5
  appearance DEF INTER_WALLS Roughcast {
    textureTransform TextureTransform {
      scale 0.3 2.2
    }
  }
}
Window {
  translation -5.19 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)2"
  size 0.06 1.1 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance DEF WINDOWS_WOOD MattePaint {
    baseColor 0.133333 0.0666667 0
  }
}
Window {
  translation -3.84 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)3"
  size 0.06 1.6 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Wall {
  translation -2.84 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(B)4"
  size 0.06 0.4 2.5
  appearance USE INTER_WALLS
}
Window {
  translation -2.09 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)5"
  size 0.06 1.1 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Window {
  translation -0.74 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)6"
  size 0.06 1.6 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Wall {
  translation 0.26 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(B)7"
  size 0.06 0.4 2.5
  appearance USE INTER_WALLS
}
Window {
  translation 1 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)8"
  size 0.06 1.1 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Window {
  translation 2.35 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)9"
  size 0.06 1.6 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Wall {
  translation 3.35 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(B)10"
  size 0.06 0.4 2.5
  appearance USE INTER_WALLS
}
Window {
  translation 4.1 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)11"
  size 0.06 1.1 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Window {
  translation 5.45 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "window(B)12"
  size 0.06 1.6 2.5
  bottomWallHeight 1
  frameSize 0.02 0.05 0.02
  windowSillSize 0.3 0.05
  frameAppearance USE WINDOWS_WOOD
}
Wall {
  translation 6.45 -2.9 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(B)13"
  size 0.06 0.4 2.5
  appearance USE INTER_WALLS
}
Wall {
  translation 6.68 0.92 0
  name "wall(C)"
  size 0.06 7.7 2.5
  appearance USE SIDE_WALLS
}
Wall {
  translation 3.6 4.8 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(D)1"
  size 0.06 6.2 2.5
}
Door {
  translation 0 4.8 0
  rotation 0 0 -1 1.5708
  size 0.06 1 2.5
  frameSize 0.01 0.05 0.05
  frameAppearance MattePaint {
    baseColor 0.133333 0.0666667 0
  }
}
Wall {
  translation -3.35 4.8 0
  rotation 0 0 1 -1.5708003061004252
  name "wall(D)2"
  size 0.06 5.7 2.5
}
Sofa {
  translation 1.22999 -2 0
  rotation 0 0 -1 -1.5707953071795862
}
PottedTree {
  translation 2.38 -2.15 0
}
PottedTree {
  translation 6.23 1.42 0
  name "potted tree(1)"
}
PottedTree {
  translation -5.78 -2.17 0
  name "potted tree(2)"
}
Cabinet {
  translation -0.01 -2.49 0
  rotation 0 0 1 1.57079
  outerThickness 0.02
  rowsHeights [
    0.3, 0.4, 0.3, 0.3
  ]
  columnsWidths [
    0.6
  ]
  layout [
    "Drawer (1, 1, 1, 1, 3.5)"
    "Shelf (1, 4, 1, 0)"
    "Shelf (1, 3, 1, 0)"
    "Shelf (1, 2, 1, 0)"
    "Shelf (1, 1, 1, 1)"
  ]
  handle CabinetHandle {
    handleColor 0.533333 0.541176 0.521569
  }
  primaryAppearance MattePaint {
    baseColor 0.729412 0.741176 0.713725
  }
  secondaryAppearance GlossyPaint {
    baseColor 0.643137 0 0
  }
}
Cabinet {
  translation 2.62 0.22 0
  rotation 0 0 1 3.14159
  name "cabinet(1)"
  outerThickness 0.02
  rowsHeights [
    0.1, 0.25
  ]
  columnsWidths [
    0.5, 0.5
  ]
  layout [
    "Drawer (2, 2, 1, 1, 1.5)"
    "Drawer (1, 2, 1, 1, 1.5)"
    "Shelf (1, 1, 0, 2)"
    "Shelf (2, 2, 1, 0)"
    "Shelf (1, 2, 1, 0)"
    "Shelf (1, 1, 0, 1)"
  ]
  handle CabinetHandle {
    handleColor 0.533333 0.541176 0.521569
  }
  primaryAppearance MattePaint {
    baseColor 0.729412 0.741176 0.713725
  }
  secondaryAppearance GlossyPaint {
    baseColor 0.643137 0 0
  }
}
Cabinet {
  translation 6.65 -0.7 1.6
  rotation 0 0 1 3.14159
  name "cabinet(2)"
  outerThickness 0.02
  rowsHeights [
    0.8
  ]
  columnsWidths [
    0.8, 0.8, 0.8, 0.8
  ]
  layout [
    "Drawer (1, 1, 1, 1, 1.5)"
    "Drawer (2, 1, 1, 1, 1.5)"
    "Drawer (3, 1, 1, 1, 1.5)"
    "Drawer (4, 1, 1, 1, 1.5)"
    "Shelf (1, 1, 0, 1)"
    "Shelf (2, 1, 0, 1)"
    "Shelf (3, 1, 0, 1)"
  ]
  handle CabinetHandle {
    handleColor 0.533333 0.541176 0.521569
  }
  primaryAppearance MattePaint {
    baseColor 0.729412 0.741176 0.713725
  }
  secondaryAppearance MattePaint {
    baseColor 0.666667 0.333333 0
  }
}
Cabinet {
  translation -6.14 -0.7 1.6
  name "cabinet(3)"
  outerThickness 0.02
  rowsHeights [
    0.8
  ]
  columnsWidths [
    0.8, 0.8, 0.8, 0.8
  ]
  layout [
    "Drawer (1, 1, 1, 1, 1.5)"
    "Drawer (2, 1, 1, 1, 1.5)"
    "Drawer (3, 1, 1, 1, 1.5)"
    "Drawer (4, 1, 1, 1, 1.5)"
    "Shelf (1, 1, 0, 1)"
    "Shelf (2, 1, 0, 1)"
    "Shelf (3, 1, 0, 1)"
  ]
  handle CabinetHandle {
    handleColor 0.533333 0.541176 0.521569
  }
  primaryAppearance MattePaint {
    baseColor 0.729412 0.741176 0.713725
  }
  secondaryAppearance MattePaint {
    baseColor 0.666667 0.333333 0
  }
}
Cabinet {
  translation 1.4 2.5 0
  rotation 0 0 1 -1.5707953071795862
  name "cabinet(4)"
  outerThickness 0.02
  rowsHeights [
    0.1, 0.4
  ]
  columnsWidths [
    0.5, 0.5
  ]
  layout [
    "Drawer (2, 2, 1, 1, 1.5)"
    "Drawer (1, 2, 1, 1, 1.5)"
    "Shelf (1, 1, 0, 2)"
    "Shelf (2, 2, 1, 0)"
    "Shelf (1, 2, 1, 0)"
    "Shelf (1, 1, 0, 1)"
  ]
  handle CabinetHandle {
    handleColor 0.533333 0.541176 0.521569
  }
  primaryAppearance MattePaint {
    baseColor 0.729412 0.741176 0.713725
  }
  secondaryAppearance GlossyPaint {
    baseColor 0.643137 0 0
  }
}
Cabinet {
  translation -0.66 -2.49 0
  rotation 0 0 1 1.57079
  name "cabinet(5)"
  outerThickness 0.02
  rowsHeights [
    0.7, 0.3, 0.3
  ]
  columnsWidths [
    0.6
  ]
  layout [
    "LeftSidedDoor (1, 1, 1, 1, 1.5)"
    "Shelf (1, 3, 1, 0)"
    "Shelf (1, 2, 1, 0)"
    "Shelf (1, 1, 1, 1)"
  ]
  handle CabinetHandle {
    handleColor 0.533333 0.541176 0.521569
  }
  primaryAppearance MattePaint {
    baseColor 0.729412 0.741176 0.713725
  }
  secondaryAppearance GlossyPaint {
    baseColor 0.643137 0 0
  }
}
Cabinet {
  translation -1.52 -2.5 0.1
  rotation 0 0 -1 -1.5707853071795865
  name "cabinet(6)"
  outerThickness 0.02
  rowsHeights [
    0.22, 0.22, 0.22
  ]
  columnsWidths [
    0.5, 0.5
  ]
  layout [
    "LeftSidedDoor (2, 1, 1, 3, 1.5)"
    "LeftSidedDoor (1, 1, 1, 3, 1.5)"
    "Shelf (1, 1, 0, 3)"
    "Shelf (2, 1, 0, 3)"
  ]
  handle CabinetHandle {
    translation 0 0.26 -0.02
    handleLength 0.1
    handleRadius 0.008
    handleColor 0.427451 0.513725 0.533333
  }
  primaryAppearance MattePaint {
    baseColor 0.94667 0.925551 0.852003
  }
  secondaryAppearance MattePaint {
    baseColor 0.94667 0.925551 0.852003
  }
}
Cabinet {
  translation -2.31 -2.5 0.1
  rotation 0 0 1 1.57079
  name "cabinet(7)"
  outerThickness 0.02
  rowsHeights [
    0.22, 0.22, 0.22
  ]
  columnsWidths [
    0.5
  ]
  layout [
    "Drawer (1, 3, 1, 1, 1)"
    "Drawer (1, 2, 1, 1, 1)"
    "Drawer (1, 1, 1, 1, 1)"
    "Shelf (1, 1, 0, 3)"
  ]
  handle CabinetHandle {
    translation -0.01 0 0.02
    handleLength 0.1
    handleRadius 0.008
    handleColor 0.427451 0.513725 0.533333
  }
  primaryAppearance MattePaint {
    baseColor 0.94667 0.925551 0.852003
  }
  secondaryAppearance MattePaint {
    baseColor 0.94667 0.925551 0.852003
  }
}
SolidBox {
  translation -1.79 -2.25 0.05
  rotation 0 0 1 1.5708
  size 0.5 1.58 0.1
  appearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      scale 4 4
    }
  }
}
Solid {
  translation -1.525 -2.25 0.83
  rotation 0.5773489358556708 0.5773509358554485 0.5773509358554485 2.0944
  children [
    Shape {
      appearance PBRAppearance {
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_base_color.jpg"
          ]
        }
        roughnessMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_roughness.jpg"
          ]
        }
        metalness 0
        normalMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_normal.jpg"
          ]
        }
        normalMapFactor 0.2
      }
      geometry IndexedFaceSet {
        coord Coordinate {
          point [
            0.25 -0.03 0.525
            -0.25 -0.03 0.525
            -0.25 -0.03 -0.525
            0.25 -0.03 -0.525
            0.25 0.03 -0.525
            -0.25 0.03 -0.525
            -0.25 0.03 0.525
            0.25 0.03 0.525
            0.25 0.03 0.525
            -0.25 0.03 0.525
            -0.25 -0.03 0.525
            0.25 -0.03 0.525
            -0.25 0.03 -0.525
            0.25 0.03 -0.525
            0.25 -0.03 -0.525
            -0.25 -0.03 -0.525
            0.25 0.03 -0.525
            0.25 0.03 0.525
            0.25 -0.03 0.525
            0.25 -0.03 -0.525
            -0.25 0.03 0.525
            -0.25 0.03 -0.525
            -0.25 -0.03 -0.525
            -0.25 -0.03 0.525
          ]
        }
        texCoord TextureCoordinate {
          point [
            0 0
            0.47619047619048 0
            0.47619047619048 1
            0 1
            0 0
            0.47619047619048 0
            0.47619047619048 1
            0 1
            0 0
            0.47619047619048 0
            0.47619047619048 0.057142857142857
            0 0.057142857142857
            0 0
            0.47619047619048 0
            0.47619047619048 0.057142857142857
            0 0.057142857142857
            0 0
            1 0
            1 0.057142857142857
            0 0.057142857142857
            0 0
            1 0
            1 0.057142857142857
            0 0.057142857142857
          ]
        }
        coordIndex [
          0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
        ]
        creaseAngle 1.5
      }
    }
  ]
  model "box"
  boundingObject Box {
    size 0.5 0.06 1.05
  }
}
Solid {
  translation -2.35 -2.021 0.83
  rotation 0.5773509358560258 -0.577349935856137 -0.577349935856137 2.09439
  children [
    Shape {
      appearance PBRAppearance {
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_base_color.jpg"
          ]
        }
        roughnessMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_roughness.jpg"
          ]
        }
        metalness 0
        normalMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_normal.jpg"
          ]
        }
        normalMapFactor 0.2
      }
      geometry IndexedFaceSet {
        coord Coordinate {
          point [
            0.02 -0.03 0.3
            -0.02 -0.03 0.3
            -0.02 -0.03 -0.3
            0.02 -0.03 -0.3
            0.02 0.03 -0.3
            -0.02 0.03 -0.3
            -0.02 0.03 0.3
            0.02 0.03 0.3
            0.02 0.03 0.3
            -0.02 0.03 0.3
            -0.02 -0.03 0.3
            0.02 -0.03 0.3
            -0.02 0.03 -0.3
            0.02 0.03 -0.3
            0.02 -0.03 -0.3
            -0.02 -0.03 -0.3
            0.02 0.03 -0.3
            0.02 0.03 0.3
            0.02 -0.03 0.3
            0.02 -0.03 -0.3
            -0.02 0.03 0.3
            -0.02 0.03 -0.3
            -0.02 -0.03 -0.3
            -0.02 -0.03 0.3
          ]
        }
        texCoord TextureCoordinate {
          point [
            0 0
            0.066666666666667 0
            0.066666666666667 1
            0 1
            0 0
            0.066666666666667 0
            0.066666666666667 1
            0 1
            0 0
            0.066666666666667 0
            0.066666666666667 0.1
            0 0.1
            0 0
            0.066666666666667 0
            0.066666666666667 0.1
            0 0.1
            0 0
            1 0
            1 0.1
            0 0.1
            0 0
            1 0
            1 0.1
            0 0.1
          ]
        }
        coordIndex [
          0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
        ]
        creaseAngle 1.5
      }
    }
  ]
  name "worktop(1)"
  model "box"
  boundingObject Box {
    size 0.04 0.06 0.6
  }
}
Solid {
  translation -2.35 -2.45 0.83
  rotation 0.5773489358556708 0.5773509358554485 0.5773509358554485 2.0944
  children [
    Shape {
      appearance PBRAppearance {
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_base_color.jpg"
          ]
        }
        roughnessMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_roughness.jpg"
          ]
        }
        metalness 0
        normalMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_normal.jpg"
          ]
        }
        normalMapFactor 0.2
      }
      geometry IndexedFaceSet {
        coord Coordinate {
          point [
            0.05 -0.03 0.3
            -0.05 -0.03 0.3
            -0.05 -0.03 -0.3
            0.05 -0.03 -0.3
            0.05 0.03 -0.3
            -0.05 0.03 -0.3
            -0.05 0.03 0.3
            0.05 0.03 0.3
            0.05 0.03 0.3
            -0.05 0.03 0.3
            -0.05 -0.03 0.3
            0.05 -0.03 0.3
            -0.05 0.03 -0.3
            0.05 0.03 -0.3
            0.05 -0.03 -0.3
            -0.05 -0.03 -0.3
            0.05 0.03 -0.3
            0.05 0.03 0.3
            0.05 -0.03 0.3
            0.05 -0.03 -0.3
            -0.05 0.03 0.3
            -0.05 0.03 -0.3
            -0.05 -0.03 -0.3
            -0.05 -0.03 0.3
          ]
        }
        texCoord TextureCoordinate {
          point [
            0 0
            0.16666666666667 0
            0.16666666666667 1
            0 1
            0 0
            0.16666666666667 0
            0.16666666666667 1
            0 1
            0 0
            0.16666666666667 0
            0.16666666666667 0.1
            0 0.1
            0 0
            0.16666666666667 0
            0.16666666666667 0.1
            0 0.1
            0 0
            1 0
            1 0.1
            0 0.1
            0 0
            1 0
            1 0.1
            0 0.1
          ]
        }
        coordIndex [
          0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
        ]
        creaseAngle 1.5
      }
    }
  ]
  name "worktop(2)"
  model "box"
  boundingObject Box {
    size 0.1 0.06 0.6
  }
}
Solid {
  translation -2.565 -2.22 0.83
  rotation 0.5773529358529169 -0.5773489358533613 -0.5773489358533613 2.09439
  children [
    Shape {
      appearance PBRAppearance {
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_base_color.jpg"
          ]
        }
        roughnessMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_roughness.jpg"
          ]
        }
        metalness 0
        normalMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/indoor/worlds/textures/rustic_veneer_wood_normal.jpg"
          ]
        }
        normalMapFactor 0.2
        textureTransform TextureTransform {
          scale 0.6 0.6
          translation 0.3 1.18
        }
      }
      geometry IndexedFaceSet {
        coord Coordinate {
          point [
            0.18 -0.03 0.085
            -0.18 -0.03 0.085
            -0.18 -0.03 -0.085
            0.18 -0.03 -0.085
            0.18 0.03 -0.085
            -0.18 0.03 -0.085
            -0.18 0.03 0.085
            0.18 0.03 0.085
            0.18 0.03 0.085
            -0.18 0.03 0.085
            -0.18 -0.03 0.085
            0.18 -0.03 0.085
            -0.18 0.03 -0.085
            0.18 0.03 -0.085
            0.18 -0.03 -0.085
            -0.18 -0.03 -0.085
            0.18 0.03 -0.085
            0.18 0.03 0.085
            0.18 -0.03 0.085
            0.18 -0.03 -0.085
            -0.18 0.03 0.085
            -0.18 0.03 -0.085
            -0.18 -0.03 -0.085
            -0.18 -0.03 0.085
          ]
        }
        texCoord TextureCoordinate {
          point [
            0 0
            1 0
            1 0.47222222222222
            0 0.47222222222222
            0 0
            1 0
            1 0.47222222222222
            0 0.47222222222222
            0 0
            1 0
            1 0.16666666666667
            0 0.16666666666667
            0 0
            1 0
            1 0.16666666666667
            0 0.16666666666667
            0 0
            0.47222222222222 0
            0.47222222222222 0.16666666666667
            0 0.16666666666667
            0 0
            0.47222222222222 0
            0.47222222222222 0.16666666666667
            0 0.16666666666667
          ]
        }
        coordIndex [
          0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
        ]
        creaseAngle 1.5
      }
    }
  ]
  name "worktop(3)"
  model "box"
  boundingObject Box {
    size 0.36 0.06 0.17
  }
}
Sink {
  translation -2.27 -2.32 0.835
  rotation 0 0 -1 -1.5708
}
Table {
  translation 3.18 -1.66 0
  size 1 1.6 0.74
  feetSize 0.05 0.05
  trayAppearance DEF TABLE_WOOD VarnishedPine {
    textureTransform TextureTransform {
      scale 10 10
    }
  }
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation 6.14 -1.66 0
  name "table(1)"
  size 1 1.6 0.74
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation -3.18 -1.66 0
  name "table(2)"
  size 1 1.6 0.74
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation 3.18 0.05 0
  name "table(3)"
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation 6.14 0.05 0
  name "table(4)"
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation -3.18 0.05 0
  name "table(5)"
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation -3.18 1.86 0
  name "table(6)"
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation -1.77 3.27 0
  rotation 0 0 1 1.5707996938995747
  name "table(7)"
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Table {
  translation -3.18 3.27 0
  rotation 0 0 1 1.5707996938995747
  name "table(8)"
  size 1 1 0.74
  feetSize 0.05 0.05
  legAppearance MattePaint {
    baseColor 0.2 0.2 0.2
  }
}
Monitor {
  translation -2.84072 -1.79048 0.76
  rotation 0 0 1 2.8797996938995745
}
Monitor {
  translation 6.47928 -1.79048 0.76
  rotation 0 0 1 -3.2724903061004254
  name "monitor(1)"
}
Monitor {
  translation -2.84277 -1.29031 0.76
  rotation 0 0 1 -2.8797996938995745
  name "monitor(2)"
}
Monitor {
  translation 6.47723 -1.29031 0.76
  rotation 0 0 1 -3.0106903061004253
  name "monitor(3)"
}
Monitor {
  translation -2.84277 -0.32031 0.76
  rotation 0 0 1 3.0106903061004253
  name "monitor(4)"
}
Monitor {
  translation -2.84277 0.16969 0.76
  rotation 0 0 1 -3.0106903061004253
  name "monitor(5)"
}
Monitor {
  translation -2.84277 1.65969 0.76
  rotation 0 0 1 3.0106996938995745
  name "monitor(6)"
}
Monitor {
  translation -2.84277 2.14969 0.76
  rotation 0 0 1 -3.0106903061004253
  name "monitor(7)"
}
Monitor {
  translation 2.87928 -1.79048 0.76
  rotation 0 0 1 0.1308996938995747
  name "monitor(8)"
}
Monitor {
  translation 2.87723 -1.29031 0.76
  rotation 0 0 1 -0.1308996938995747
  name "monitor(9)"
}
Monitor {
  translation 2.87723 -0.18031 0.76
  rotation 0 0 1 0.1308996938995747
  name "monitor(10)"
}
Monitor {
  translation 2.87723 0.35969 0.76
  rotation 0 0 1 -0.1308996938995747
  name "monitor(11)"
}
Keyboard {
  translation -3.22303 -1.73097 0.74
  rotation 0 0 1 3.0106996938995745
}
Keyboard {
  translation 6.05 -1.57 0.74
  name "keyboard(1)"
}
Keyboard {
  translation -2.99655 -0.0404275 0.74
  rotation 0 0 1 -3.0106993877991495
  name "keyboard(2)"
}
Keyboard {
  translation -3.26 1.91 0.74
  rotation 0 0 1 -3.141592653589793
  name "keyboard(3)"
}
Keyboard {
  translation 2.98306 -1.5267 0.74
  rotation 0 0 1 -0.2617916122008506
  name "keyboard(4)"
}
Keyboard {
  translation 3.07 0.1 0.74
  name "keyboard(5)"
}
OfficeChair {
  translation 4.01794 -0.0449998 0
  rotation 0 0 1 2.618
}
OfficeChair {
  translation 3.94 -1.53 0
  rotation 0 0 1 3.14159
  name "office chair(1)"
}
OfficeChair {
  translation 5.36388 -1.48338 0
  rotation 0 0 1 0.261797
  name "office chair(2)"
}
OfficeChair {
  translation -4.16876 -1.98104 0
  rotation 0 0 1 0.785397
  name "office chair(3)"
}
OfficeChair {
  translation -4 0.0700003 0
  name "office chair(4)"
}
OfficeChair {
  translation -4.07 1.85 0
  rotation 0 0 1 0.785397
  name "office chair(5)"
}
MecanumBotLidar {
  translation 0.080496 0.935016 0.100157
  rotation -0.00012811199811949475 0.00011377399832995657 0.9999999853213966 -3.103315307179586
  controller "webots_rosa"
  controllerArgs [
    "mecanumbot_lidar.yaml"
  ]
}
