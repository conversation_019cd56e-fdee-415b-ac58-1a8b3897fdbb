#VRML_SIM R2025a utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/floors/protos/Floor.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/ThreadMetalPlate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Door.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/GlossyPaint.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/CardboardBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pipes/protos/PipeSection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Wall.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Roughcast.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/hospital/protos/EmergencyExitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Window.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/lights/protos/DoubleFluorescentLamp.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/FormedConcrete.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/fire_extinguisher/protos/FireExtinguisher.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/manhole/protos/SquareManhole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/solids/protos/SolidPipe.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/WoodenBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pallet/protos/WoodenPalletStack.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/street_furniture/protos/Fence.proto"
EXTERNPROTO "../protos/MecanumBotLidar.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/PlasticCrate.proto"

WorldInfo {
  basicTimeStep 16
  optimalThreadCount 2
  contactProperties [
    ContactProperties {
      material1 "ExteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation 0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
    ContactProperties {
      material1 "InteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation -0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
  ]
}
Viewpoint {
  orientation 0.6276671670171702 0.026199178125788983 -0.7780408283079834 3.0851584340877762
  position 10.916690508089978 10.868596218409136 58.690256195302155
}
TexturedBackground {
  texture "empty_office"
}
PointLight {
  attenuation 0 0 1
  intensity 10.8
  location -5 -7 5
  castShadows TRUE
}
PointLight {
  attenuation 0 0 1
  intensity 9.6
  location 5 -7 5
}
Solid {
  translation 0 -4.3 0
  children [
    Pose {
      children [
        Shape {
          appearance ThreadMetalPlate {
            textureTransform TextureTransform {
              scale 10 10
            }
          }
          geometry Plane {
            size 20 16.4
          }
        }
      ]
    }
  ]
  name "bottom floor"
  model "floor"
  boundingObject Plane {
    size 50 30
  }
  locked TRUE
}
DEF water Solid {
  translation 0 -5.63 0
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.7 0.8 1
        transparency 0.9
        roughness 0.05
        metalness 0.2
        emissiveColor 0.7 0.8 1
        emissiveIntensity 0.5
      }
      geometry Cylinder {
        height 0.005
        radius 0.5
      }
    }
  ]
}
WoodenBox {
  translation 0 -9.88 0.3
  name "wooden box(8)"
}
WoodenBox {
  translation -0.61 -9.88 0.3
  name "wooden box(10)"
}
PlasticCrate {
  translation 0 -9.88 0.601
  size 0.6 0.5 0.3
  color 0.5 0.5 0.5
}
PlasticCrate {
  translation -3.31 -7.58 0.011
  name "plastic crate(3)"
  size 0.6 0.5 0.3
  color 0.5 0.5 0.5
}
PlasticCrate {
  translation 3.31 -7.58 0.011
  name "plastic crate(2)"
  size 0.6 0.5 0.3
  color 0.5 0.5 0.5
}
WoodenBox {
  translation 0.6 -9.88 0.3
  name "wooden box(9)"
}
WoodenBox {
  translation 4.6 -7.4 0.3
  name "wooden box(4)"
}
WoodenBox {
  translation -4.6 -7.4 0.3
  name "wooden box(11)"
}
Fence {
  translation 1.5 -10.67 0
  rotation 0 0 1 1.5708
  name "fence(5)"
  path [
    0 0 0
    0 1.5 0
  ]
}
Fence {
  translation -1.51 -10.67 0
  rotation 0 0 1 -1.5707953071795862
  name "fence(6)"
  path [
    0 0 0
    0 1.5 0
  ]
}
Window {
  translation 5.79665 -12.6023 -4.44089e-15
  rotation 0 0 1 -1.5707953071795862
  name "window(3)"
  size 0.2 8.2 3
  glassTransparency 0.9
  bottomWallHeight 0
  windowHeight 3
  frameSize 0.001 0.001 0.001
  windowSillSize 0.0001 0.0001
  frameAppearance GlossyPaint {
  }
}
Window {
  translation -5.82335 -12.6023 -4.44089e-15
  rotation 0 0 1 -1.5707953071795862
  name "window(3)"
  size 0.2 8.2 3
  glassTransparency 0.9
  bottomWallHeight 0
  windowHeight 3
  frameSize 0.001 0.001 0.001
  windowSillSize 0.0001 0.0001
  frameAppearance GlossyPaint {
  }
}
Window {
  translation -0.00335008 -9.1523 -4.44089e-15
  rotation 0 0 1 -1.5707953071795862
  name "window(3)"
  size 0.1 4 3
  glassTransparency 0.95
  bottomWallHeight 0
  windowHeight 3
  frameSize 0.001 0.001 0.001
  windowSillSize 0.0001 0.0001
  frameAppearance ThreadMetalPlate {
    textureTransform TextureTransform {
      scale 20 20
    }
  }
}
DEF box_stack Pose {
  children [
    WoodenBox {
      translation 4.59277 1.51638 0.6
      name "wooden box(1)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation 6.07994 1.47087 0.6
      name "wooden box(2)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation 6.14381 -0.611217 0.6
      name "wooden box(3)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation 3.24277 -0.61362 0.6
      name "wooden box(5)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation 3.24277 -2.26362 0.6
      name "wooden box(6)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation 4.60277 -2.26362 0.6
      name "wooden box(7)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenPalletStack {
      translation 3.43293 -4.19661 1.84741e-13
      name "wooden pallet stack(2)"
      palletNumber 3
      palletSize 1.2 0.8 0.5
      palletLathNumber 2
    }
    WoodenPalletStack {
      translation 6.64293 -7.28661 1.84741e-13
      name "wooden pallet stack(3)"
      palletNumber 3
      palletSize 1.2 0.8 0.5
      palletLathNumber 2
    }
    WoodenPalletStack {
      translation 4.68293 -4.20661 1.84741e-13
      name "wooden pallet stack(1)"
      palletNumber 3
      palletSize 1.2 0.8 0.5
      palletLathNumber 2
    }
    Fence {
      translation 2.00086 -4.83264 -0.01
      height 1.2
      path [
        0 0 0
        0 8.5 0
      ]
    }
    Fence {
      translation 7.99086 -9.1226 -0.01
      name "fence(3)"
      height 1.2
      path [
        0 0 0
        0 2.6 0
      ]
    }
    Fence {
      translation -7.99914 -9.1226 -0.01
      name "fence(4)"
      height 1.2
      path [
        0 0 0
        0 2.6 0
      ]
    }
    Fence {
      translation 2.00086 -4.83264 -0.01
      rotation 2.3464099999870814e-06 -2.3464099999870814e-06 0.9999999999944944 -1.5707953071795862
      name "fence(1)"
      height 1.2
      path [
        0 0 0
        0 6 0
      ]
    }
    Fence {
      translation 2.00086 -6.48264 -0.01
      rotation 2.3464099999870814e-06 -2.3464099999870814e-06 0.9999999999944944 -1.5707953071795862
      name "fence(2)"
      height 1.2
      path [
        0 0 0
        0 6 0
      ]
    }
    Fence {
      translation 2.00086 -9.11264 -0.01
      rotation 2.3464099999870814e-06 -2.3464099999870814e-06 0.9999999999944944 -1.5707953071795862
      name "fence(7)"
      height 1.2
      path [
        0 0 0
        0 6 0
      ]
    }
    WoodenBox {
      translation -4.59277 1.51638 0.6
      name "wooden box_m(1)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation -6.07994 1.47087 0.6
      name "wooden box_m(2)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation -6.14381 -0.611217 0.6
      name "wooden box_m(3)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation -3.24277 -0.61362 0.6
      name "wooden box_m(5)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation -3.24277 -2.26362 0.6
      name "wooden box_m(6)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenBox {
      translation -4.60277 -2.26362 0.6
      name "wooden box_m(7)"
      size 0.8 0.8 1.2
      mass 10
    }
    WoodenPalletStack {
      translation -3.43293 -4.19661 1.84741e-13
      palletNumber 3
      palletSize 1.2 0.8 0.5
      palletLathNumber 2
    }
    WoodenPalletStack {
      translation -6.64 -7.29 1.84741e-13
      name "wooden pallet stack(4)"
      palletNumber 3
      palletSize 1.2 0.8 0.5
      palletLathNumber 2
    }
    WoodenPalletStack {
      translation -4.65293 -4.15661 1.84741e-13
      name "wooden pallet stack_m(1)"
      palletNumber 3
      palletSize 1.2 0.8 0.5
      palletLathNumber 2
    }
    Fence {
      translation -2.00086 -4.83264 -0.01
      name "fence_m"
      model "fence_m"
      height 1.2
      path [
        0 0 0
        0 8.5 0
      ]
    }
    Fence {
      translation -2.00086 -4.83264 -0.01
      rotation -2.3464099999870814e-06 -2.3464099999870814e-06 0.9999999999944944 1.5708
      name "fence_m(1)"
      height 1.2
      path [
        0 0 0
        0 6 0
      ]
    }
    Fence {
      translation -2.00086 -6.48264 -0.01
      rotation -2.3464099999870814e-06 -2.3464099999870814e-06 0.9999999999944944 1.5708
      name "fence_m(2)"
      height 1.2
      path [
        0 0 0
        0 6 0
      ]
    }
    Fence {
      translation -2.00086 -9.11264 -0.01
      rotation -2.3464099999870814e-06 -2.3464099999870814e-06 0.9999999999944944 1.5708
      name "fence_m(3)"
      height 1.2
      path [
        0 0 0
        0 6 0
      ]
    }
  ]
}
Pose {
  translation 0 -28.9 0
}
Wall {
  translation 5.70012 3.80931 0
  rotation 0 0 1 -1.5707953071795862
  name "wall(1)"
  size 0.2 8.4 7
}
Wall {
  translation -3.87456e-06 3.8 3
  rotation 0 0 1 -1.5707953071795862
  name "wall(8)"
  size 0.2 3.2 4
}
Wall {
  translation 2.28804e-05 -22.44 0
  rotation 0 0 1 -1.5707953071795862
  name "wall(7)"
  size 0.2 3.2 3
}
Wall {
  translation 1.29e-05 -12.62 2.98
  rotation 0 0 1 -1.5707953071795862
  name "wall(7)"
  size 0.2 20.2 4
}
Wall {
  translation -5.70988 3.81931 0
  rotation 0 0 1 -1.5707953071795862
  name "wall(2)"
  size 0.2 8.4 7
}
DEF HALL1 Pose {
  translation 0 8.9 0
  children [
    SolidPipe {
      translation -1.39 -1.76401e-06 2.73
      rotation 1 0 0 -1.5707953071795862
      height 10
      radius 0.1
      appearance PBRAppearance {
        baseColor 0.3 0.3 1
        roughness 0.5
        metalness 0
      }
    }
    CardboardBox {
      translation -1.19501 -1.73032 0.3
      name "cardboard box(1)"
    }
    PipeSection {
      translation 1.43289 2.5192 1.5
      name "pipe section(7)"
      height 3
      radius 0.06
    }
    PipeSection {
      translation 1.43289 2.8592 1.5
      name "pipe section(8)"
      height 3
      radius 0.06
    }
    SquareManhole {
      translation 0 3.48 -0.04
      rotation 0 0 1 1.5708
    }
    Window {
      translation 1.6 0.73 0
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Window {
      translation -1.61 0.73 0
      rotation 0 0 1 3.14159
      name "window(1)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Floor {
      name "floor(3)"
      size 3 10
      tileSize 1 1
      appearance ThreadMetalPlate {
      }
    }
    FireExtinguisher {
      translation 1.35 1.8 0
    }
    FireExtinguisher {
      translation -1.35 1.8 0
      name "fire extinguisher(1)"
    }
    Solid {
      translation -4 0 3
      children [
        Shape {
          appearance FormedConcrete {
          }
          geometry Box {
            size 3.5 10 0.1
          }
        }
      ]
      name "solid(1)"
    }
    DoubleFluorescentLamp {
      translation 0 -3 3
      rotation 0 1 0 3.14159
    }
    DoubleFluorescentLamp {
      translation 0 3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(2)"
    }
    DoubleFluorescentLamp {
      translation 0 0 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(1)"
    }
    PointLight {
      attenuation 0 0 1
      intensity 0.5
      location 0 0 3
    }
    Wall {
      translation 1.6 -3 0
      name "wall(4)"
      size 0.2 4 3
    }
    Wall {
      translation -1.61 -3 0
      name "wall(6)"
      size 0.2 4 3
    }
    Wall {
      translation 1.6 3.23 0
      name "wall(5)"
      size 0.2 3.5 3
    }
    Wall {
      translation -1.61 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Door {
      translation 1.6 -0.5 0
      rotation 0 0 1 3.141592
      name "door(2)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
    Door {
      translation -1.61 -0.5 0
      rotation 0 0 1 -5.307179586466759e-06
      name "door(3)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
  ]
}
DEF HALL2 Pose {
  translation 0 18.9 0
  children [
    SolidPipe {
      translation -1.39 -1.76401e-06 2.73
      rotation 1 0 0 -1.5707953071795862
      name "pipe(2)"
      height 10
      radius 0.1
      appearance PBRAppearance {
        baseColor 0.3 0.3 1
        roughness 0.5
        metalness 0
      }
    }
    CardboardBox {
      translation -1.19501 -1.73032 0.3
      name "cardboard box(2)"
    }
    PipeSection {
      translation 1.43289 2.5192 1.5
      name "pipe section(9)"
      height 3
      radius 0.06
    }
    PipeSection {
      translation 1.43289 2.8592 1.5
      name "pipe section(10)"
      height 3
      radius 0.06
    }
    SquareManhole {
      translation 0 3.48 -0.04
      rotation 0 0 1 1.5708
      name "manhole(1)"
    }
    Window {
      translation 1.6 0.73 0
      name "window(2)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Window {
      translation -1.61 0.73 0
      rotation 0 0 1 3.14159
      name "window(3)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Floor {
      name "floor(2)"
      size 3 10
      tileSize 1 1
      appearance ThreadMetalPlate {
      }
    }
    FireExtinguisher {
      translation 1.35 1.8 0
      name "fire extinguisher(2)"
    }
    FireExtinguisher {
      translation -1.35 1.8 0
      name "fire extinguisher(3)"
    }
    Solid {
      translation -4 0 3
      children [
        Shape {
          appearance FormedConcrete {
          }
          geometry Box {
            size 3.5 10 0.1
          }
        }
      ]
      name "solid(2)"
    }
    DoubleFluorescentLamp {
      translation 0 -3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(2)"
    }
    DoubleFluorescentLamp {
      translation 0 3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(2)"
    }
    DoubleFluorescentLamp {
      translation 0 0 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    PointLight {
      attenuation 0 0 1
      intensity 0.5
      location 0 0 3
    }
    Wall {
      translation 1.6 -3 0
      name "wall(6)"
      size 0.2 4 3
    }
    Wall {
      translation -1.61 -3 0
      name "wall(6)"
      size 0.2 4 3
    }
    Wall {
      translation 1.6 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Wall {
      translation -1.61 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Door {
      translation 1.6 -0.5 0
      rotation 0 0 1 3.141592
      name "door(4)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
    Door {
      translation -1.61 -0.5 0
      rotation 0 0 1 -5.307179586466759e-06
      name "door(5)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
  ]
}
DEF HALL3 Pose {
  translation 0 28.9 0
  children [
    SolidPipe {
      translation -1.39 -1.76401e-06 2.73
      rotation 1 0 0 -1.5707953071795862
      name "pipe(3)"
      height 10
      radius 0.1
      appearance PBRAppearance {
        baseColor 0.3 0.3 1
        roughness 0.5
        metalness 0
      }
    }
    CardboardBox {
      translation -1.19501 -1.73032 0.3
      name "cardboard box(3)"
    }
    PipeSection {
      translation 1.43289 2.5192 1.5
      name "pipe section(9)"
      height 3
      radius 0.06
    }
    PipeSection {
      translation 1.43289 2.8592 1.5
      name "pipe section(10)"
      height 3
      radius 0.06
    }
    SquareManhole {
      translation 0 3.48 -0.04
      rotation 0 0 1 1.5708
      name "manhole(2)"
    }
    Window {
      translation 1.6 0.73 0
      name "window(2)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Window {
      translation -1.61 0.73 0
      rotation 0 0 1 3.14159
      name "window(3)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Floor {
      name "floor(4)"
      size 3 10
      tileSize 1 1
      appearance ThreadMetalPlate {
      }
    }
    FireExtinguisher {
      translation 1.35 1.8 0
      name "fire extinguisher(2)"
    }
    FireExtinguisher {
      translation -1.35 1.8 0
      name "fire extinguisher(3)"
    }
    Solid {
      translation -4 0 3
      children [
        Shape {
          appearance FormedConcrete {
          }
          geometry Box {
            size 3.5 10 0.1
          }
        }
      ]
      name "solid(3)"
    }
    DoubleFluorescentLamp {
      translation 0 -3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(2)"
    }
    DoubleFluorescentLamp {
      translation 0 3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(2)"
    }
    DoubleFluorescentLamp {
      translation 0 0 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    PointLight {
      attenuation 0 0 1
      intensity 0.5
      location 0 0 3
    }
    Wall {
      translation 1.6 -3 0
      name "wall(6)"
      size 0.2 4 3
    }
    Wall {
      translation -1.61 -3 0
      name "wall(6)"
      size 0.2 4 3
    }
    Wall {
      translation 1.6 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Wall {
      translation -1.61 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Door {
      translation 1.6 -0.5 0
      rotation 0 0 1 3.141592
      name "door(4)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
    Door {
      translation -1.61 -0.5 0
      rotation 0 0 1 -5.307179586466759e-06
      name "door(5)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
  ]
}
DEF HALL4 Pose {
  translation 0 38.9 0
  children [
    SolidPipe {
      translation -1.39 -1.76401e-06 2.73
      rotation 1 0 0 -1.5707953071795862
      name "pipe(4)"
      height 10
      radius 0.1
      appearance PBRAppearance {
        baseColor 0.3 0.3 1
        roughness 0.5
        metalness 0
      }
    }
    CardboardBox {
      translation -1.19501 -1.73032 0.3
      name "cardboard box(4)"
    }
    PipeSection {
      translation 1.43289 2.5192 1.5
      name "pipe section(10)"
      height 3
      radius 0.06
    }
    PipeSection {
      translation 1.43289 2.8592 1.5
      name "pipe section(10)"
      height 3
      radius 0.06
    }
    SquareManhole {
      translation 0 3.48 -0.04
      rotation 0 0 1 1.5708
      name "manhole(3)"
    }
    Window {
      translation 1.6 0.73 0
      name "window(3)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Window {
      translation -1.61 0.73 0
      rotation 0 0 1 3.14159
      name "window(3)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Floor {
      name "floor(5)"
      size 3 10
      tileSize 1 1
      appearance ThreadMetalPlate {
      }
    }
    Wall {
      translation 1.15 4.88 0
      rotation 0 0 1 -1.5707953071795862
      name "wall(7)"
      size 0.2 0.8 3
    }
    Wall {
      translation -1.14 4.88 0
      rotation 0 0 1 -1.5707953071795862
      name "wall(7)"
      size 0.2 0.8 3
    }
    FireExtinguisher {
      translation 1.35 1.8 0
      name "fire extinguisher(3)"
    }
    FireExtinguisher {
      translation -1.35 1.8 0
      name "fire extinguisher(3)"
    }
    Solid {
      translation -4 0 3
      children [
        Shape {
          appearance FormedConcrete {
          }
          geometry Box {
            size 3.5 10 0.1
          }
        }
      ]
      name "solid(4)"
    }
    DoubleFluorescentLamp {
      translation 0 -3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    DoubleFluorescentLamp {
      translation 0 3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    DoubleFluorescentLamp {
      translation 0 0 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    PointLight {
      attenuation 0 0 1
      intensity 0.5
      location 0 0 3
    }
    Wall {
      translation 1.6 -3 0
      name "wall(7)"
      size 0.2 4 3
    }
    Wall {
      translation -1.61 -3 0
      name "wall(7)"
      size 0.2 4 3
    }
    Wall {
      translation 1.6 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Wall {
      translation -1.61 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Door {
      translation 1.6 -0.5 0
      rotation 0 0 1 3.141592
      name "door(5)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
    Door {
      translation -1.61 -0.5 0
      rotation 0 0 1 -5.307179586466759e-06
      name "door(5)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
    Door {
      translation 0 4.88 0
      rotation 0 0 1 1.57079
      name "door(1)"
      size 0.2 1.5 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
  ]
}
DEF HALL5 Pose {
  translation 0 -17.5 0
  children [
    DEF banner Solid {
      translation 0 -1.56 1.5
      children [
        Shape {
          appearance PBRAppearance {
            baseColorMap ImageTexture {
              url [
                "textures/banner.png"
              ]
            }
            transparency 0.1
            roughness 0.8
            metalness 0
            textureTransform TextureTransform {
              center 0 -0.5
              scale 1 0.5
            }
          }
          geometry Box {
            size 3.1 0.002 0.4
          }
        }
      ]
      name "solid(13)"
    }
    DEF banner2 Solid {
      translation 0 61.05 1.99
      children [
        Shape {
          appearance PBRAppearance {
            baseColorMap ImageTexture {
              url [
                "textures/banner2.png"
              ]
            }
            transparency 0.1
            roughness 0.8
            metalness 0
            textureTransform TextureTransform {
              center 0 -0.5
              scale 1 0.5
            }
          }
          geometry Box {
            size 3.1 0.002 0.4
          }
        }
      ]
      name "solid(14)"
    }
    DEF wire Solid {
      translation -9.88028e-07 2.4 0.0399984
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0 0 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(7)"
    }
    DEF wire2 Solid {
      translation -9.6716e-07 2.39 0.0199984
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0 0 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(8)"
    }
    DEF wire2 Solid {
      translation -9.81072e-07 2.41 0.0199984
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0 0 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(6)"
    }
    DEF wire3 Solid {
      translation -9.60204e-07 2.38 0.0199984
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0 0 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(9)"
    }
    DEF wire4 Solid {
      translation -9.46292e-07 2.37 0.0099984
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0.8 0 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(10)"
    }
    DEF wire5 Solid {
      translation -9.39336e-07 2.36 0.00999839
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0.8 0.8 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(11)"
    }
    DEF wire5 Solid {
      translation -9.3238e-07 2.35 0.00999838
      rotation 0.577349935856137 0.5773509358560258 -0.577349935856137 -2.094395307179586
      children [
        Shape {
          appearance PBRAppearance {
            baseColor 0 0.8 0
            roughness 0.6
            metalness 0
          }
          geometry Cylinder {
            height 3
            radius 0.003
          }
        }
      ]
      name "solid(12)"
    }
    SolidPipe {
      translation -1.39 -1.76401e-06 2.73
      rotation 1 0 0 -1.5707953071795862
      name "pipe(5)"
      height 10
      radius 0.1
      appearance PBRAppearance {
        baseColor 0.3 0.3 1
        roughness 0.5
        metalness 0
      }
    }
    CardboardBox {
      translation -1.19501 -1.73032 0.3
      name "cardboard box(5)"
    }
    SquareManhole {
      translation 0 3.48 -0.048
      rotation 0 0 1 1.5708
      name "manhole(4)"
    }
    Window {
      translation 1.6 0.73 0
      name "window(3)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Window {
      translation -1.61 0.73 0
      rotation 0 0 1 3.14159
      name "window(3)"
      size 0.2 1.5 3
      windowHeight 1.3
      wallAppearance GlossyPaint {
      }
      frameAppearance GlossyPaint {
        baseColor 1 1 0.7
      }
    }
    Floor {
      name "floor(6)"
      size 20 10
      tileSize 1 1
      appearance ThreadMetalPlate {
      }
    }
    Solid {
      translation -4 0 3
      children [
        Shape {
          appearance FormedConcrete {
          }
          geometry Box {
            size 3.5 10 0.1
          }
        }
      ]
      name "solid(5)"
    }
    DoubleFluorescentLamp {
      translation 0 -3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    DoubleFluorescentLamp {
      translation 0 3 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    DoubleFluorescentLamp {
      translation 0 0 3
      rotation 0 1 0 3.14159
      name "double fuorescent lamp(3)"
    }
    PointLight {
      attenuation 0 0 1
      intensity 0.5
      location 0 0 3
    }
    Wall {
      translation 1.6 -3 0
      name "wall(7)"
      size 0.2 4 3
    }
    Wall {
      translation -1.61 -3 0
      name "wall(7)"
      size 0.2 4 3
    }
    Wall {
      translation 1.6 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Wall {
      translation -1.61 3.23 0
      name "wall(7)"
      size 0.2 3.5 3
    }
    Door {
      translation 1.6 -0.5 0
      rotation 0 0 1 3.141592
      name "door(5)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
    Door {
      translation -1.61 -0.5 0
      rotation 0 0 1 -5.307179586466759e-06
      name "door(5)"
      size 0.2 1 3
      position -0.00014955311370250452
      doorAppearance GlossyPaint {
        baseColor 1 1 0.8
      }
      frameAppearance GlossyPaint {
        baseColor 0.8 0.8 0.8
      }
    }
  ]
}
Floor {
  translation 0 -4.22 7
  rotation 1 0 0 3.141592
  size 20 16.4
  tileSize 1 1
  appearance PBRAppearance {
    baseColorMap ImageTexture {
      url [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/asphalt.jpg"
      ]
    }
    roughness 1
    metalness 0
  }
}
Wall {
  translation -10 -4.3 0
  rotation 0 0 1 1.5708
  name "wall(3)"
  size 16.5 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 4.125 1.725
    }
  }
}
Wall {
  translation 10 -4.3 0
  rotation 0 0 1 -1.5708
  size 16.5 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 4.125 1.725
    }
  }
}
EmergencyExitSign {
  translation -0.0700337 3.64 3.34
  rotation 0.9999999999944038 3.2321499999819125e-06 8.636279999951669e-07 1.57079
}
MecanumBotLidar {
  translation 0.382814 -4.82921 0.10199
  rotation 0 0 1 1.5708
  controller "webots_rosa"
  controllerArgs [
    "mecanumbot_lidar.yaml"
  ]
}
