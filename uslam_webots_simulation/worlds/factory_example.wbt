#VRML_SIM R2025a utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/floors/protos/Floor.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/GlossyPaint.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/CardboardBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pallet/protos/WoodenPalletStack.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/WoodenBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Wall.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Roughcast.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/cabinet/protos/Cabinet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/hospital/protos/EmergencyExitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/stairs/protos/StraightStairsRail.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/stairs/protos/StraightStairs.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/fire_extinguisher/protos/FireExtinguisher.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/PlasticCrate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/conveyors/protos/ConveyorBelt.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/forklift/protos/Forklift.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/humans/pedestrian/protos/Pedestrian.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/ThreadMetalPlate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/tables/protos/Table.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/tools/protos/PlatformCart.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pipes/protos/PipeSection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/solids/protos/SolidPipe.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Pavement.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/street_furniture/protos/Fence.proto"
EXTERNPROTO "../protos/MecanumBotLidar.proto"

WorldInfo {
  contactProperties [
    ContactProperties {
      bounce 0
      softERP 0.6
      softCFM 1e-05
    }
    ContactProperties {
      material1 "ExteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation 0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
    ContactProperties {
      material1 "InteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation -0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
  ]
}
Viewpoint {
  orientation 0.4292576430897828 -0.4401470242727375 -0.7886751377296352 4.927554596032462
  position -32.421500564991014 -44.16696455340957 47.19805071199706
}
TexturedBackground {
  texture "factory"
}
DEF racks Pose {
  translation 0 -4.43 0
  children [
    PlasticCrate {
      translation 18.04 -1.5 0
      name "plastic crate(3)"
      size 0.6 0.6 0.4
      color 0.5 0.5 0.5
      mass 5
    }
    PlasticCrate {
      translation 18.04 -0.5 0
      name "plastic crate(3)"
      size 0.6 0.6 0.4
      color 0.5 0.5 0.5
      mass 5
    }
    Pose {
      translation -5.01 0 0
      children [
        PlasticCrate {
          translation -0.65 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          mass 2
        }
        PlasticCrate {
          translation 0.51 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          color 0.2 0.2 0.8
          mass 2
        }
        Table {
          translation 0 3 0
          rotation 0 0 1 -1.5707953071795862
          name "table(7)"
          size 1 2 0.74
        }
        WoodenBox {
          translation 1.06 3.03 0.3
          name "wooden box(4)"
        }
      ]
    }
    Pose {
      translation -10.29 0 0
      children [
        WoodenBox {
          translation -1.52 2.63 0.3
          name "wooden box(4)"
        }
        PlasticCrate {
          translation -0.65 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          mass 2
        }
        PlasticCrate {
          translation 0.51 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          color 0.2 0.2 0.8
          mass 2
        }
        Table {
          translation 0 3 0
          rotation 0 0 1 -1.5707953071795862
          name "table(8)"
          size 1 2 0.74
        }
      ]
    }
    Pose {
      translation 15.01 0 0
      children [
        PlasticCrate {
          translation -0.65 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          mass 2
        }
        PlasticCrate {
          translation 0.51 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          color 0.2 0.2 0.8
          mass 2
        }
        Table {
          translation 0 3 0
          rotation 0 0 1 -1.5707953071795862
          name "table(6)"
          size 1 2 0.74
        }
        WoodenBox {
          translation -1.82 2.95 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation -1.82 2.95 0.9
          name "wooden box(4)"
          mass 10
        }
      ]
    }
    Pose {
      translation 5.05 0 0
      children [
        WoodenBox {
          translation 1.57 2.95 0.3
          name "wooden box(4)"
          mass 10
        }
        PlasticCrate {
          translation -0.65 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          mass 2
        }
        PlasticCrate {
          translation 0.51 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          color 0.2 0.2 0.8
          mass 2
        }
        Table {
          translation 0 3 0
          rotation 0 0 1 -1.5707953071795862
          name "table(4)"
          size 1 2 0.74
        }
      ]
    }
    Pose {
      translation 9.99 0 0
      children [
        PlasticCrate {
          translation -0.65 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          mass 2
        }
        PlasticCrate {
          translation 0.51 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          color 0.2 0.2 0.8
          mass 2
        }
        Table {
          translation 0 3 0
          rotation 0 0 1 -1.5707953071795862
          name "table(5)"
          size 1 2 0.74
        }
        WoodenBox {
          translation 0 3.13 0.3
          name "wooden box(4)"
          mass 10
        }
      ]
    }
    Pose {
      children [
        WoodenBox {
          translation 1.46 3.18 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation 2.86 13.83 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation 3.47 13.83 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation 3.47 13.13 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation 4.13 13.13 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation 3.5 12.31 0.3
          name "wooden box(4)"
          mass 10
        }
        WoodenBox {
          translation 2.86 13.83 0.9
          name "wooden box(4)"
          mass 10
        }
        PlasticCrate {
          translation -0.65 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          mass 2
        }
        PlasticCrate {
          translation 0.51 3.06 0.74
          name "plastic crate(3)"
          size 0.6 0.6 0.3
          color 0.2 0.2 0.8
          mass 2
        }
        Table {
          translation 0 3 0
          rotation 0 0 1 -1.5707953071795862
          name "table(3)"
          size 1 2 0.74
        }
      ]
    }
    Pose {
      translation 5.01 -2.01 0
      rotation 0 0 1 3.1415926
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation -1.38778e-17 -2.01 0
      rotation 0 0 1 3.1415926
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation -5.01 -2.01 0
      rotation 0 0 1 3.1415926
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation -10.02 -2.01 0
      rotation 0 0 1 3.1415926
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation 10.02 -2.01 0
      rotation 0 0 1 3.1415926
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation 15.03 -2.01 0
      rotation 0 0 1 3.1415926
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation 10.02 0 0
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation 15.03 0 0
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation 5.01 0 0
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(1)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(3)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(3)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(3)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(3)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(3)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(3)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(16)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(15)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(7)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(6)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(5)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(3)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(2)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation -5.02 0 0
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(3)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(3)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(3)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(3)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(3)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(3)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(3)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
    Pose {
      translation -10.03 0 0
      children [
        Fence {
          translation -2 0.99999 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 -8.32008e-06 0
          rotation 0 0 1 -1.5707953071795862
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          height 1.2
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1.06537e-06 0
          rotation 0 0 1 1.5708
          name "fence(4)"
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 2 1 0.67
          rotation -0.5773509358554485 0.5773489358556708 -0.5773509358554485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation -2 1 0.67
          rotation -0.5773492691885823 -0.5773512691895147 0.5773502691890485 -2.094395307179586
          name "fence(4)"
          height 1
          path [
            0 0 0
            0 2 0
          ]
        }
        Fence {
          translation 1.96 1.04 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
        Fence {
          translation -1.96 1.05 0
          rotation 0 0 1 -3.1415853071795863
          name "fence(4)"
          path [
            0 0 0
            0 1 0
          ]
        }
      ]
    }
  ]
}
PointLight {
  attenuation 0 0 1
  intensity 10.8
  location -4 0.5 5
  castShadows TRUE
}
PointLight {
  attenuation 0 0 1
  intensity 9.6
  location 5 0.5 5
}
Solid {
  translation 0 -4.3 0
  children [
    Pose {
      children [
        Shape {
          appearance ThreadMetalPlate {
            textureTransform TextureTransform {
              scale 10 10
            }
          }
          geometry Plane {
            size 50 30
          }
        }
      ]
    }
  ]
  name "bottom floor"
  model "floor"
  boundingObject Plane {
    size 50 30
  }
  locked TRUE
}
WoodenBox {
  translation 16.76 -9 0.3
  name "wooden box(4)"
  size 0.6 1.2 0.6
  mass 3
}
WoodenBox {
  translation -18.92 -12.65 0.3
  name "wooden box(4)"
  size 0.6 1.2 0.6
  mass 3
}
WoodenBox {
  translation 15.18 0 0.79
  name "wooden box(4)"
  size 0.6 0.6 1.5
  mass 3
}
WoodenBox {
  translation 20 -1.95 0.3
  name "wooden box(4)"
  size 1.2 1 0.6
  mass 3
}
DEF pillars Pose {
  translation 0 -5 0
  children [
    Solid {
      translation 16 -6.5 3.5
      children [
        DEF pillar Shape {
          appearance Pavement {
          }
          geometry Box {
            size 0.5 0.5 7
          }
        }
      ]
      name "solid(7)"
      boundingObject USE pillar
    }
    Solid {
      translation 16 6.5 3.5
      children [
        DEF pillar Shape {
          appearance Pavement {
          }
          geometry Box {
            size 0.5 0.5 7
          }
        }
      ]
      name "solid(6)"
      boundingObject USE pillar
    }
    Solid {
      translation -16 -6.5 3.5
      children [
        DEF pillar Shape {
          appearance Pavement {
          }
          geometry Box {
            size 0.5 0.5 7
          }
        }
      ]
      name "solid(5)"
      boundingObject USE pillar
    }
    Solid {
      translation -16 6.5 3.5
      children [
        DEF pillar Shape {
          appearance Pavement {
          }
          geometry Box {
            size 0.5 0.5 7
          }
        }
      ]
      name "solid(4)"
      boundingObject USE pillar
    }
  ]
}
SolidPipe {
  translation 0.0096 -18.1141 5.52994
  rotation 0 1 0 -1.5707953071795862
  height 50
  radius 0.2
  appearance PBRAppearance {
    baseColor 0 0 1
    baseColorMap ImageTexture {
      url [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/tagged_wall.jpg"
      ]
    }
    roughness 0.5
    metalness 0
  }
}
Cabinet {
  translation -24.6669 8.00907 2.75335e-14
  name "cabinet(2)"
  columnsWidths [
    1.2, 1.2, 0.17
  ]
}
PipeSection {
  translation -24.3686 3.47926 3.47
  height 7
  radius 0.1
}
PipeSection {
  translation -24.3862 2.88923 3.5
  name "pipe section(1)"
  height 7
  radius 0.1
}
Pedestrian {
  translation -7.92902 -14.2509 1.27
  name "pedestrian(2)"
  enableBoundingObject TRUE
}
Pedestrian {
  translation -16.6897 -5.27369 1.27
  name "pedestrian(3)"
  controllerArgs [
    "--speed= 1.5"
    "--trajectory= -16.7 -5.27, 18 -5.27, -20 -5.27, 19.5 -5.27, -21 -5.27"
  ]
  enableBoundingObject TRUE
}
PlatformCart {
  translation -7.93642 5.61793 1.42109e-14
  rotation 0 0 1 -1.5707953071795862
  name "platform cart(6)"
}
Table {
  translation -23.8746 -9.74004 1.77636e-15
  size 1.5 3 0.74
}
Table {
  translation -21.5992 7.54513 1.77636e-15
  rotation 0 0 1 1.5708
  name "table(2)"
  size 1.5 3 0.74
}
Table {
  translation -21.5992 5.99513 1.77636e-15
  rotation 0 0 1 1.5708
  name "table(9)"
  size 1.5 3 0.74
}
Table {
  translation 23.5212 5.76433 1.77636e-15
  name "table(1)"
  size 1.5 3 0.74
}
Pedestrian {
  translation -10.4017 -0.309151 1.27
  controllerArgs [
    "--speed= 2"
    "--trajectory= -20 0, 20 0, 20 -10, 20 0"
  ]
  enableBoundingObject TRUE
}
Pedestrian {
  translation 23 -9 1.27
  rotation 0 0 1 3.14159
  name "pedestrian(1)"
  controllerArgs [
    "--speed= 2"
    "--trajectory= 23 -9, -5 -9"
  ]
  enableBoundingObject TRUE
}
FireExtinguisher {
  translation 24.25089994356668 -2.6767903921454193 -0.004708800000003233
  rotation 0.0005627268540230284 0.003905946678556088 0.9999922134292009 -3.1415853032139816
}
DEF DOUBLE_DOOR Solid {
  translation 24.8706 -4.13767 -2.13163e-14
  rotation 0 0 1 6.283185307179586
  children [
    Solid {
      translation 0 0 3.38
      children [
        Shape {
          appearance Plaster {
            textureTransform TextureTransform {
              scale 0.9 1.35
            }
          }
          geometry Box {
            size 0.2 1.8 2.76
          }
        }
      ]
      name "mobile part"
      boundingObject Box {
        size 0.2 1.8 2.76
      }
    }
    Solid {
      translation 0 0 1.975
      rotation 0 0 1 -3.1415853071795863
      children [
        Shape {
          appearance DEF DOUBLE_DOOR_APPEARANCE GlossyPaint {
            baseColor 0.579 0.52 0.473
          }
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                -0.15 0.85 0.025
                -0.15 -0.85 0.025
                0.15 -0.85 0.025
                0.15 0.85 0.025
                0.15 0.85 -0.025
                0.15 -0.85 -0.025
                -0.15 -0.85 -0.025
                -0.15 0.85 -0.025
                -0.15 0.85 -0.025
                -0.15 -0.85 -0.025
                -0.15 -0.85 0.025
                -0.15 0.85 0.025
                0.15 -0.85 -0.025
                0.15 0.85 -0.025
                0.15 0.85 0.025
                0.15 -0.85 0.025
                0.15 0.85 -0.025
                -0.15 0.85 -0.025
                -0.15 0.85 0.025
                0.15 0.85 0.025
                -0.15 -0.85 -0.025
                0.15 -0.85 -0.025
                0.15 -0.85 0.025
                -0.15 -0.85 0.025
              ]
            }
            texCoord TextureCoordinate {
              point [
                0 0
                1 0
                1 0.17647058823529
                0 0.17647058823529
                0 0
                1 0
                1 0.17647058823529
                0 0.17647058823529
                0 0
                1 0
                1 0.029411764705882
                0 0.029411764705882
                0 0
                1 0
                1 0.029411764705882
                0 0.029411764705882
                0 0
                0.17647058823529 0
                0.17647058823529 0.029411764705882
                0 0.029411764705882
                0 0
                0.17647058823529 0
                0.17647058823529 0.029411764705882
                0 0.029411764705882
              ]
            }
            coordIndex [
              0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
            ]
            creaseAngle 1.5
          }
        }
      ]
      name "top frame"
      model "box"
      boundingObject Box {
        size 0.3 1.7 0.05
      }
    }
    Solid {
      translation 0 0.875 1
      rotation 0 0 1 -3.1415853071795863
      children [
        Shape {
          appearance USE DOUBLE_DOOR_APPEARANCE
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                -0.15 0.025 1
                -0.15 -0.025 1
                0.15 -0.025 1
                0.15 0.025 1
                0.15 0.025 -1
                0.15 -0.025 -1
                -0.15 -0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 -0.025 -1
                -0.15 -0.025 1
                -0.15 0.025 1
                0.15 -0.025 -1
                0.15 0.025 -1
                0.15 0.025 1
                0.15 -0.025 1
                0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 1
                0.15 0.025 1
                -0.15 -0.025 -1
                0.15 -0.025 -1
                0.15 -0.025 1
                -0.15 -0.025 1
              ]
            }
            texCoord TextureCoordinate {
              point [
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
              ]
            }
            coordIndex [
              0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
            ]
            creaseAngle 1.5
          }
        }
      ]
      name "left frame"
      model "box"
      boundingObject Box {
        size 0.3 0.05 2
      }
    }
    Solid {
      translation 0 -0.875 1
      rotation 0 0 1 3.14159
      children [
        Shape {
          appearance USE DOUBLE_DOOR_APPEARANCE
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                -0.15 0.025 1
                -0.15 -0.025 1
                0.15 -0.025 1
                0.15 0.025 1
                0.15 0.025 -1
                0.15 -0.025 -1
                -0.15 -0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 -0.025 -1
                -0.15 -0.025 1
                -0.15 0.025 1
                0.15 -0.025 -1
                0.15 0.025 -1
                0.15 0.025 1
                0.15 -0.025 1
                0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 1
                0.15 0.025 1
                -0.15 -0.025 -1
                0.15 -0.025 -1
                0.15 -0.025 1
                -0.15 -0.025 1
              ]
            }
            texCoord TextureCoordinate {
              point [
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
              ]
            }
            coordIndex [
              0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
            ]
            creaseAngle 1.5
          }
        }
      ]
      name "right frame"
      model "box"
      boundingObject Box {
        size 0.3 0.05 2
      }
    }
    HingeJoint {
      jointParameters HingeJointParameters {
        axis 0 0 1
        anchor -0.15 -0.875 0
        minStop -0.01
        maxStop 1.57
        dampingConstant 10
      }
      endPoint Solid {
        translation -0.0010000000000000009 0 0
        children [
          DEF BOX Pose {
            translation -0.175 -0.43 1
            rotation 0 0 1 -1.5708
            children [
              Shape {
                appearance USE DOUBLE_DOOR_APPEARANCE
                geometry Box {
                  size 0.875 0.05 1.97
                }
              }
            ]
          }
          Solid {
            translation -0.175 -0.09 1
            children [
              DEF HANDLE_SUPPORT Pose {
                rotation 0 -1 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Cylinder {
                      height 0.075
                      radius 0.0375
                    }
                  }
                ]
              }
              DEF HANDLE_AXIS Pose {
                rotation 0 -1 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Capsule {
                      height 0.18
                      radius 0.0125
                    }
                  }
                ]
              }
              DEF BACK_HANDLE Pose {
                translation 0.09 -0.06 0
                rotation -1 0 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Capsule {
                      height 0.12
                      radius 0.0125
                    }
                  }
                ]
              }
              DEF FRONT_HANDLE Pose {
                translation -0.09 -0.06 0
                rotation -1 0 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Capsule {
                      height 0.12
                      radius 0.0125
                    }
                  }
                ]
              }
            ]
            name "door lever(1)"
            model "door lever"
            boundingObject Group {
              children [
                USE HANDLE_SUPPORT
                USE HANDLE_AXIS
                USE BACK_HANDLE
                USE FRONT_HANDLE
              ]
            }
            physics Physics {
              density -1
              mass 0.7
            }
          }
        ]
        boundingObject USE BOX
        physics Physics {
          density 200
        }
      }
    }
    HingeJoint {
      jointParameters HingeJointParameters {
        axis 0 0 -1
        anchor -0.15 0.875 0
        minStop -0.01
        maxStop 1.57
        dampingConstant 10
      }
      endPoint Solid {
        translation -0.0010000000000000009 0.88 0
        rotation 0 0 -1 0
        children [
          DEF BOX Pose {
            translation -0.175 -0.43 1
            rotation 0 0 1 -1.5708
            children [
              Shape {
                appearance USE DOUBLE_DOOR_APPEARANCE
                geometry Box {
                  size 0.875 0.05 1.97
                }
              }
            ]
          }
          Pose {
            translation -0.175 -0.09 1
            children [
              Solid {
                translation 0 -0.69 0
                rotation -1 0 0 3.141592644
                children [
                  DEF HANDLE_SUPPORT Pose {
                    rotation 0 -1 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Cylinder {
                          height 0.075
                          radius 0.0375
                        }
                      }
                    ]
                  }
                  DEF HANDLE_AXIS Pose {
                    rotation 0 -1 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Capsule {
                          height 0.18
                          radius 0.0125
                        }
                      }
                    ]
                  }
                  DEF BACK_HANDLE Pose {
                    translation 0.09 -0.06 0
                    rotation -1 0 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Capsule {
                          height 0.12
                          radius 0.0125
                        }
                      }
                    ]
                  }
                  DEF FRONT_HANDLE Pose {
                    translation -0.09 -0.06 0
                    rotation -1 0 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Capsule {
                          height 0.12
                          radius 0.0125
                        }
                      }
                    ]
                  }
                ]
                name "door lever(1)"
                model "door lever"
                boundingObject Group {
                  children [
                    USE HANDLE_SUPPORT
                    USE HANDLE_AXIS
                    USE BACK_HANDLE
                    USE FRONT_HANDLE
                  ]
                }
                physics Physics {
                  density -1
                  mass 0.7
                }
              }
            ]
          }
          Pose {
            translation -0.21 -0.23 1.36
            rotation 0.5773502691896257 -0.5773502691896257 -0.5773502691896257 2.0943951024
            children [
              Shape {
                appearance PBRAppearance {
                  baseColorMap ImageTexture {
                    url [
                      "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/factory/worlds/textures/ears_protection_pannel.png"
                    ]
                  }
                  roughness 0.2
                  metalness 0
                }
                geometry Plane {
                  size 0.2 0.2
                }
              }
            ]
          }
          Pose {
            translation -0.21 -0.51 1.36
            rotation 0.5773502691896257 -0.5773502691896257 -0.5773502691896257 2.0943951024
            children [
              Shape {
                appearance PBRAppearance {
                  baseColorMap ImageTexture {
                    url [
                      "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/factory/worlds/textures/glasses_panel.png"
                    ]
                  }
                  roughness 0.2
                  metalness 0
                }
                geometry Plane {
                  size 0.2 0.2
                }
              }
            ]
          }
        ]
        name "solid(1)"
        boundingObject USE BOX
        physics Physics {
          density 200
        }
      }
    }
  ]
  name "door(3)"
  model "door"
}
DEF DOUBLE_DOOR Solid {
  translation -25.0794 -4.86978 2.13163e-14
  rotation 0 0 1 3.1415926
  children [
    Solid {
      translation 0 0 3.38
      children [
        Shape {
          appearance Plaster {
            textureTransform TextureTransform {
              scale 0.9 1.35
            }
          }
          geometry Box {
            size 0.2 1.8 2.76
          }
        }
      ]
      name "mobile part"
      boundingObject Box {
        size 0.2 1.8 2.76
      }
    }
    Solid {
      translation 0 0 1.975
      rotation 0 0 1 -3.1415853071795863
      children [
        Shape {
          appearance DEF DOUBLE_DOOR_APPEARANCE GlossyPaint {
            baseColor 0.579 0.52 0.473
          }
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                -0.15 0.85 0.025
                -0.15 -0.85 0.025
                0.15 -0.85 0.025
                0.15 0.85 0.025
                0.15 0.85 -0.025
                0.15 -0.85 -0.025
                -0.15 -0.85 -0.025
                -0.15 0.85 -0.025
                -0.15 0.85 -0.025
                -0.15 -0.85 -0.025
                -0.15 -0.85 0.025
                -0.15 0.85 0.025
                0.15 -0.85 -0.025
                0.15 0.85 -0.025
                0.15 0.85 0.025
                0.15 -0.85 0.025
                0.15 0.85 -0.025
                -0.15 0.85 -0.025
                -0.15 0.85 0.025
                0.15 0.85 0.025
                -0.15 -0.85 -0.025
                0.15 -0.85 -0.025
                0.15 -0.85 0.025
                -0.15 -0.85 0.025
              ]
            }
            texCoord TextureCoordinate {
              point [
                0 0
                1 0
                1 0.17647058823529
                0 0.17647058823529
                0 0
                1 0
                1 0.17647058823529
                0 0.17647058823529
                0 0
                1 0
                1 0.029411764705882
                0 0.029411764705882
                0 0
                1 0
                1 0.029411764705882
                0 0.029411764705882
                0 0
                0.17647058823529 0
                0.17647058823529 0.029411764705882
                0 0.029411764705882
                0 0
                0.17647058823529 0
                0.17647058823529 0.029411764705882
                0 0.029411764705882
              ]
            }
            coordIndex [
              0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
            ]
            creaseAngle 1.5
          }
        }
      ]
      name "top frame"
      model "box"
      boundingObject Box {
        size 0.3 1.7 0.05
      }
    }
    Solid {
      translation 0 0.875 1
      rotation 0 0 1 -3.1415853071795863
      children [
        Shape {
          appearance USE DOUBLE_DOOR_APPEARANCE
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                -0.15 0.025 1
                -0.15 -0.025 1
                0.15 -0.025 1
                0.15 0.025 1
                0.15 0.025 -1
                0.15 -0.025 -1
                -0.15 -0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 -0.025 -1
                -0.15 -0.025 1
                -0.15 0.025 1
                0.15 -0.025 -1
                0.15 0.025 -1
                0.15 0.025 1
                0.15 -0.025 1
                0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 1
                0.15 0.025 1
                -0.15 -0.025 -1
                0.15 -0.025 -1
                0.15 -0.025 1
                -0.15 -0.025 1
              ]
            }
            texCoord TextureCoordinate {
              point [
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
              ]
            }
            coordIndex [
              0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
            ]
            creaseAngle 1.5
          }
        }
      ]
      name "left frame"
      model "box"
      boundingObject Box {
        size 0.3 0.05 2
      }
    }
    Solid {
      translation 0 -0.875 1
      rotation 0 0 1 3.14159
      children [
        Shape {
          appearance USE DOUBLE_DOOR_APPEARANCE
          geometry IndexedFaceSet {
            coord Coordinate {
              point [
                -0.15 0.025 1
                -0.15 -0.025 1
                0.15 -0.025 1
                0.15 0.025 1
                0.15 0.025 -1
                0.15 -0.025 -1
                -0.15 -0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 -0.025 -1
                -0.15 -0.025 1
                -0.15 0.025 1
                0.15 -0.025 -1
                0.15 0.025 -1
                0.15 0.025 1
                0.15 -0.025 1
                0.15 0.025 -1
                -0.15 0.025 -1
                -0.15 0.025 1
                0.15 0.025 1
                -0.15 -0.025 -1
                0.15 -0.025 -1
                0.15 -0.025 1
                -0.15 -0.025 1
              ]
            }
            texCoord TextureCoordinate {
              point [
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 0.15
                0 0.15
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.025 0
                0.025 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
                0 0
                0.15 0
                0.15 1
                0 1
              ]
            }
            coordIndex [
              0, 1, 2, 3, -1, 4, 5, 6, 7, -1, 8, 9, 10, 11, -1, 12, 13, 14, 15, -1, 16, 17, 18, 19, -1, 20, 21, 22, 23, -1
            ]
            creaseAngle 1.5
          }
        }
      ]
      name "right frame"
      model "box"
      boundingObject Box {
        size 0.3 0.05 2
      }
    }
    HingeJoint {
      jointParameters HingeJointParameters {
        axis 0 0 1
        anchor -0.15 -0.875 0
        minStop -0.01
        maxStop 1.57
        dampingConstant 10
      }
      endPoint Solid {
        translation -0.0010000000000000009 0 0
        children [
          DEF BOX Pose {
            translation -0.175 -0.43 1
            rotation 0 0 1 -1.5708
            children [
              Shape {
                appearance USE DOUBLE_DOOR_APPEARANCE
                geometry Box {
                  size 0.875 0.05 1.97
                }
              }
            ]
          }
          Solid {
            translation -0.175 -0.09 1
            children [
              DEF HANDLE_SUPPORT Pose {
                rotation 0 -1 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Cylinder {
                      height 0.075
                      radius 0.0375
                    }
                  }
                ]
              }
              DEF HANDLE_AXIS Pose {
                rotation 0 -1 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Capsule {
                      height 0.18
                      radius 0.0125
                    }
                  }
                ]
              }
              DEF BACK_HANDLE Pose {
                translation 0.09 -0.06 0
                rotation -1 0 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Capsule {
                      height 0.12
                      radius 0.0125
                    }
                  }
                ]
              }
              DEF FRONT_HANDLE Pose {
                translation -0.09 -0.06 0
                rotation -1 0 0 1.57
                children [
                  Shape {
                    appearance BrushedAluminium {
                    }
                    geometry Capsule {
                      height 0.12
                      radius 0.0125
                    }
                  }
                ]
              }
            ]
            name "door lever(1)"
            model "door lever"
            boundingObject Group {
              children [
                USE HANDLE_SUPPORT
                USE HANDLE_AXIS
                USE BACK_HANDLE
                USE FRONT_HANDLE
              ]
            }
            physics Physics {
              density -1
              mass 0.7
            }
          }
        ]
        boundingObject USE BOX
        physics Physics {
          density 200
        }
      }
    }
    HingeJoint {
      jointParameters HingeJointParameters {
        axis 0 0 -1
        anchor -0.15 0.875 0
        minStop -0.01
        maxStop 1.57
        dampingConstant 10
      }
      endPoint Solid {
        translation -0.0010000000000000009 0.88 0
        rotation 0 0 -1 0
        children [
          DEF BOX Pose {
            translation -0.175 -0.43 1
            rotation 0 0 1 -1.5708
            children [
              Shape {
                appearance USE DOUBLE_DOOR_APPEARANCE
                geometry Box {
                  size 0.875 0.05 1.97
                }
              }
            ]
          }
          Pose {
            translation -0.175 -0.09 1
            children [
              Solid {
                translation 0 -0.69 0
                rotation -1 0 0 3.141592644
                children [
                  DEF HANDLE_SUPPORT Pose {
                    rotation 0 -1 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Cylinder {
                          height 0.075
                          radius 0.0375
                        }
                      }
                    ]
                  }
                  DEF HANDLE_AXIS Pose {
                    rotation 0 -1 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Capsule {
                          height 0.18
                          radius 0.0125
                        }
                      }
                    ]
                  }
                  DEF BACK_HANDLE Pose {
                    translation 0.09 -0.06 0
                    rotation -1 0 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Capsule {
                          height 0.12
                          radius 0.0125
                        }
                      }
                    ]
                  }
                  DEF FRONT_HANDLE Pose {
                    translation -0.09 -0.06 0
                    rotation -1 0 0 1.57
                    children [
                      Shape {
                        appearance BrushedAluminium {
                        }
                        geometry Capsule {
                          height 0.12
                          radius 0.0125
                        }
                      }
                    ]
                  }
                ]
                name "door lever(1)"
                model "door lever"
                boundingObject Group {
                  children [
                    USE HANDLE_SUPPORT
                    USE HANDLE_AXIS
                    USE BACK_HANDLE
                    USE FRONT_HANDLE
                  ]
                }
                physics Physics {
                  density -1
                  mass 0.7
                }
              }
            ]
          }
          Pose {
            translation -0.21 -0.23 1.36
            rotation 0.5773502691896257 -0.5773502691896257 -0.5773502691896257 2.0943951024
            children [
              Shape {
                appearance PBRAppearance {
                  baseColorMap ImageTexture {
                    url [
                      "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/factory/worlds/textures/ears_protection_pannel.png"
                    ]
                  }
                  roughness 0.2
                  metalness 0
                }
                geometry Plane {
                  size 0.2 0.2
                }
              }
            ]
          }
          Pose {
            translation -0.21 -0.51 1.36
            rotation 0.5773502691896257 -0.5773502691896257 -0.5773502691896257 2.0943951024
            children [
              Shape {
                appearance PBRAppearance {
                  baseColorMap ImageTexture {
                    url [
                      "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/samples/environments/factory/worlds/textures/glasses_panel.png"
                    ]
                  }
                  roughness 0.2
                  metalness 0
                }
                geometry Plane {
                  size 0.2 0.2
                }
              }
            ]
          }
        ]
        name "solid(1)"
        boundingObject USE BOX
        physics Physics {
          density 200
        }
      }
    }
  ]
  name "door(2)"
  model "door"
}
Floor {
  translation 0 -4.22 7
  rotation 1 0 0 3.141592
  name "floor(top)"
  size 50 30
  tileSize 1 1
  appearance PBRAppearance {
    baseColorMap ImageTexture {
      url [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/asphalt.jpg"
      ]
    }
    roughness 1
    metalness 0
  }
}
CardboardBox {
  translation -5.60842 -16.7511 0.93
  rotation 0 0 1 -1.5707963267948966
  mass 5
}
WoodenPalletStack {
  translation 1.50014 3.81503 -7.10543e-15
  name "wooden pallet stack(1)"
  palletNumber 2
  palletSize 1.2 0.8 0.3
  palletLathNumber 2
}
PlatformCart {
  translation 4.95753 2.659 0
}
PlatformCart {
  translation -7.25804 -14.5095 2.13163e-14
  rotation 0 0 1 -0.523595307179586
  name "platform cart(3)"
}
PlatformCart {
  translation -20.9777 -15.4411 2.13163e-14
  name "platform cart(4)"
}
PlatformCart {
  translation -5.40727 -18.2435 2.13163e-14
  name "platform cart(5)"
}
WoodenPalletStack {
  translation 0.659037 6.30829 3.55271e-15
  rotation 0 0 1 2.65072
  palletNumber 3
  palletSize 1.2 0.8 0.3
  palletLathNumber 2
}
DEF woodenbox Pose {
  translation 0 -3 0
  children [
    WoodenBox {
      translation 0.070787217 -11.12746 0.3
      rotation -0.935113119114731 0.250562820924732 0.250562820924732 4.645351474266668
      name "wooden box(1)"
      mass 5
    }
    WoodenBox {
      translation 0.14546116 -11.909219 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(2)"
      mass 5
    }
    WoodenBox {
      translation -1.1321262 -11.1489 0.3
      rotation 0.862856210520177 0.357406743058074 0.357406743058073 1.71777151624413
      name "wooden box(3)"
      mass 5
    }
    WoodenBox {
      translation 1.5463886 -11.768592 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.49883855 -11.765703 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(5)"
      mass 5
    }
    WoodenBox {
      translation 0.95220994 -10.946614 0.3
      rotation -0.677661491465059 0.519987933987584 0.519987933987584 4.332744312017075
      name "wooden box(6)"
      mass 5
    }
    WoodenBox {
      translation 0.91693757 -11.921006 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(7)"
      mass 5
    }
    WoodenBox {
      translation 0.74212598 -12.012516 0.9
      rotation 1 0 0 1.5707963267948966
      name "wooden box(8)"
      mass 5
    }
    WoodenBox {
      translation -0.5072975 -11.706525 1.52
      rotation 1 0 0 1.5707963267948966
      name "wooden box(9)"
      mass 5
    }
    WoodenBox {
      translation -0.48872581 -11.691652 0.93
      rotation 1 0 0 1.5707963267948966
      mass 5
    }
  ]
}
DEF woodenbox2 Pose {
  translation -12 3 0
  children [
    WoodenBox {
      translation -1.5614 -10.7299 0.9
      rotation -0.935113119114731 0.250562820924732 0.250562820924732 4.645351474266668
      name "wooden box(3)"
      mass 5
    }
    WoodenBox {
      translation -1.78128 -12.1559 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -1.62185 -10.7184 0.3
      rotation 0.862856210520177 0.357406743058074 0.357406743058073 1.71777151624413
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.648831 -10.4698 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.555894 -12.1777 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -3.79563 -8.30909 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -6.09011 -8.24431 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      size 0.6 0.6 1.2
      mass 5
    }
    WoodenBox {
      translation -3.4949 -10.4887 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.50302 -10.6437 0.3
      rotation -0.677661491465059 0.519987933987584 0.519987933987584 4.332744312017075
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.411 -12.1559 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.27766 -13.0974 0.37
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation 4.07658 2.49125 0.41
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.618936 -12.2137 0.94
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -1.15747 -12.1546 0.3
      rotation 1 0 0 1.5707963267948966
      name "wooden box(4)"
      mass 5
    }
  ]
}
Pose {
}
Wall {
  translation 0 10.82 0
  name "wall(1)"
  size 50 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 5 1.75
    }
  }
}
Cabinet {
  translation -23.46 10.84 0
  rotation 0 0 1 -1.5707963267948966
  name "cabinet(1)"
  columnsWidths [
    1.3, 0.17, 0.17
  ]
  layout [
    "Shelf (1, 5, 3, 0)"
    "Shelf (1, 4, 3, 0)"
    "Shelf (1, 3, 3, 0)"
    "Shelf (1, 2, 3, 0)"
  ]
}
Cabinet {
  translation 23.46 10.84 0
  rotation 0 0 1 -1.5707963267948966
  name "cabinet(3)"
  columnsWidths [
    1.3, 0.17, 0.17
  ]
  layout [
    "Shelf (1, 5, 3, 0)"
    "Shelf (1, 4, 3, 0)"
    "Shelf (1, 3, 3, 0)"
    "Shelf (1, 2, 3, 0)"
  ]
}
FireExtinguisher {
  translation -21.8715 10.5305 0
  name "fire extinguisher(1)"
}
FireExtinguisher {
  translation -24.5115 -7.1695 0
  name "fire extinguisher(2)"
}
Cabinet {
  translation -20 10.84 0
  rotation 0 0 1 -1.5707963267948966
  columnsWidths [
    1, 1
  ]
  layout [
    "RightSidedDoor (1, 1, 1, 5, 1.5)"
    "LeftSidedDoor (2, 1, 1, 5, 1.5)"
  ]
}
Wall {
  translation -0.0665665 -19.2869 2.13163e-14
  name "wall(2)"
  size 50 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 5 1.75
    }
  }
}
Wall {
  translation -25 -4.3 0
  rotation 0 0 1 1.5708
  name "wall(3)"
  size 30 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 4.125 1.725
    }
  }
}
Wall {
  translation 24.813 -4.20683 3.16192e-13
  rotation 0 0 -1 1.5708
  size 30 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 4.125 1.725
    }
  }
}
EmergencyExitSign {
  translation -24.8071 -4.96738 2.3
  rotation 0.5773489358550934 0.5773519358547601 0.5773499358549823 2.0944
  name "emergency exit sign(1)"
}
Solid {
  translation 2.0235 9.98307 3.21
  rotation 0.5773502691896258 0.5773502691896258 0.5773502691896258 2.0944
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/steel_floor.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 2 2
        }
      }
      geometry Box {
        size 1.5 0.12 2.6
      }
    }
  ]
}
StraightStairsRail {
  translation 3.34287 9.30077 3.27
  rotation 7.73585e-07 6.7404e-07 -1 -3.1415853071795863
  name "straight stairs rail(1)"
  run 2.5
  rise 0
  newelHeight 0.89
  balusterHeight 0.9
  nBalusters 9
  appearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
    }
  }
}
StraightStairsRail {
  translation 3.36481 9.32735 3.27
  rotation -3.59841e-07 -3.97997e-07 1 1.5708
  run 1.3
  rise 0
  newelHeight 0.89
  balusterHeight 0.9
  nBalusters 5
  appearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
    }
  }
}
StraightStairs {
  translation -2.07636 10.0168 2.13163e-14
  rotation 0 0 1 -5.307179586466759e-06
  stepSize 0.3 1.34 0.01
  stepRise 0.297
  nSteps 10
  stepAppearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
      scale 3 1
    }
  }
  stringerAppearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
      scale 3 3
    }
  }
  leftRail []
  rightRail [
    StraightStairsRail {
      run 3
      rise 2.97
      newelHeight 0.862
      balusterHeight 0.83
      nBalusters 12
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          rotation 1.5708
        }
      }
    }
  ]
}
StraightStairs {
  translation 18.9519 -16.2818 2.57572e-14
  rotation 0 0 1 -1.5708053071795867
  name "straight stairs(1)"
  stepSize 0.3 1.34 0.01
  nSteps 10
  stepAppearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
      scale 3 1
    }
  }
  stringerAppearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
      scale 3 3
    }
  }
  leftRail []
  rightRail [
    StraightStairsRail {
      run 3
      rise 1.5
      newelHeight 0.862
      balusterHeight 0.83
      nBalusters 12
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          rotation 1.5708
        }
      }
    }
  ]
}
WoodenBox {
  translation 0.694378 -11.906 1
  name "wooden box(10)"
  size 1 1 2
  mass 100
}
WoodenBox {
  translation -0.561504 -11.9603 1
  name "wooden box(4)"
  size 1 1 2
  mass 100
}
WoodenBox {
  translation -15.394 -9.20421 1
  name "wooden box(4)"
  size 1 1 2
  mass 100
}
DEF plastic_crate_stack Pose {
  translation -5 -13 0
  children [
    PlasticCrate {
      translation 0 0.605 -0.00039239999999995945
      rotation -0.8382744233848091 -0.391194730912908 0.379819264394058 3.039834453639516e-17
      name "plastic crate(4)"
      size 0.6 0.6 0.4
      color 0 0.6 0.2
      mass 1
    }
    PlasticCrate {
      translation 0.6050000000000004 -7.98286496003998e-18 -0.0003923999999999317
      rotation 0.9674375983574395 0.0806197998631265 -0.2399269496209613 1.0535937317864881e-17
      name "plastic crate(3)"
      size 0.6 0.6 0.4
      color 0 0.3 1
      mass 1
    }
    PlasticCrate {
      translation -3.1633522468155206e-09 1.8353932029538438e-09 0.39862660000000016
      rotation -0.00869880683324345 -0.015525065416653595 0.9998416390126421 2.179598129044762e-09
      name "plastic crate(1)"
      size 0.6 0.6 0.4
      color 0 0 1
      mass 1
    }
    PlasticCrate {
      translation 1.3677947663381929e-13 -7.204296602889816e-14 -0.0011771999999998783
      rotation -0.5135266968423525 -0.8580736163890695 -2.207185138084122e-05 3.175287870363952e-11
      size 0.6 0.6 0.4
      color 0 0 1
      mass 1
    }
  ]
}
DEF plastic_crate_stack Pose {
  translation 11.48 -13 0
  children [
    PlasticCrate {
      translation 0 0.605 -0.00039239999999995945
      rotation -0.8382744233848091 -0.391194730912908 0.379819264394058 3.039834453639516e-17
      name "plastic crate(2)"
      size 0.6 0.6 0.4
      color 0 0.6 0.2
      mass 1
    }
    PlasticCrate {
      translation 0.6050000000000004 -7.98286496003998e-18 -0.0003923999999999317
      rotation 0.9674375983574395 0.0806197998631265 -0.2399269496209613 1.0535937317864881e-17
      name "plastic crate(2)"
      size 0.6 0.6 0.4
      color 0 0.3 1
      mass 1
    }
    PlasticCrate {
      translation 1.5 5.17519e-09 0.018529
      rotation 0.0009828626487221625 0.9938846447828705 -0.11041896053595716 4.692820414042842e-06
      name "plastic crate(2)"
      size 0.6 0.6 0.4
      mass 0.2
    }
    PlasticCrate {
      translation 1.3677947663381929e-13 -7.204296602889816e-14 -0.0011771999999998783
      rotation -0.5135266968423525 -0.8580736163890695 -2.207185138084122e-05 3.175287870363952e-11
      name "plastic crate(3)"
      size 0.6 0.6 0.4
      color 0 0 1
      mass 1
    }
  ]
}
DEF plastic_crate_stack_platform Pose {
  translation 22 -17 1.6
  children [
    PlasticCrate {
      translation 1.30494 2.82015 0.038529
      rotation -8.90264e-11 -1.18608e-10 1 0.261799
      name "plastic crate(3)"
      size 0.6 0.6 0.4
      mass 0.2
    }
    PlasticCrate {
      translation -0.856639 1.10281 -0.0003924
      rotation -0.8382744233848091 -0.391194730912908 0.379819264394058 3.039834453639516e-17
      name "plastic crate(2)"
      size 0.6 0.6 0.4
      color 0 0.6 0.2
      mass 1
    }
    PlasticCrate {
      translation 0.6050000000000004 -7.98286496003998e-18 -0.0003923999999999317
      rotation 0.9674375983574395 0.0806197998631265 -0.2399269496209613 1.0535937317864881e-17
      name "plastic crate(2)"
      size 0.6 0.6 0.4
      color 0 0.3 1
      mass 1
    }
  ]
}
DEF woodenstack Pose {
  translation 0 -10 0
  children [
    WoodenBox {
      translation -14.6 15.04 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -16.1 15.04 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -16.1 15.71 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -16.1 16.92 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -17.57 16.92 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -16.1 18.39 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -14.79 18.39 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -14.79 16.89 0.3
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -14.78 16.92 0.9
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -16.1 18.34 0.9
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -16.1 15.71 0.92
      name "wooden box(4)"
      mass 10
    }
    WoodenBox {
      translation -14.6 15.04 0.91
      name "wooden box(4)"
      mass 10
    }
    WoodenPalletStack {
      translation 15.5 18 0
      name "pallet_col2_row1"
      palletNumber 3
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 15.5 16 0
      name "pallet_col2_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.8
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 12.5 18 0
      name "pallet_col3_row1"
      palletNumber 3
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 12.5 16 0
      name "pallet_col3_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.8
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 9.5 18 0
      name "pallet_col4_row1"
      palletNumber 2
      palletSize 1.2 1.5 0.8
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 9.5 16 0
      name "pallet_col4_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.8
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 6.5 18 0
      name "pallet_col5_row1"
      palletNumber 3
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation 6.5 16 0
      name "pallet_col5_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.8
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -12.5 18 0
      name "pallet_col-3_row1"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -12.5 16 0
      name "pallet_col-3_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -12.5 14 0
      name "pallet_col-3_row3"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -9.5 18 0
      name "pallet_col-4_row1"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -9.5 16 0
      name "pallet_col-4_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -9.5 14 0
      name "pallet_col-4_row3"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -6.5 18 0
      name "pallet_col-5_row1"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -6.5 16 0
      name "pallet_col-5_row2"
      palletNumber 2
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
    WoodenPalletStack {
      translation -6.5 14 0
      name "pallet_col-5_row3"
      palletNumber 1
      palletSize 1.2 1.5 0.6
      palletLathNumber 2
      palletMass 1
    }
  ]
}
DEF platform Solid {
  translation 22.1617 -15.696 1.5
  rotation 0.5773502691896258 0.5773502691896258 0.5773502691896258 2.0944
  children [
    DEF platform Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/steel_floor.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 2 2
        }
      }
      geometry Box {
        size 7 0.15 5
      }
    }
  ]
  name "solid(1)"
  boundingObject USE platform
}
Solid {
  translation 19.7908 -12.3064 0.7
  children [
    Shape {
      appearance PBRAppearance {
        roughness 1
        metalness 0.5
      }
      geometry DEF platform_pillar Box {
        size 0.15 0.15 1.5
      }
    }
  ]
  name "solid(2)"
  boundingObject USE platform_pillar
}
Solid {
  translation 24.5 -12.3064 0.7
  children [
    Shape {
      appearance PBRAppearance {
        roughness 1
        metalness 0.5
      }
      geometry Box {
        size 0.15 0.15 1.5
      }
    }
  ]
  name "solid(3)"
  boundingObject USE platform_pillar
}
Solid {
  translation 13.0945 -16.4443 1
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.5 0.4 0.4
        roughness 1
        metalness 0.6
      }
      geometry Box {
        size 6 2.4 2
      }
    }
  ]
  boundingObject Box {
    size 6 2.4 2
  }
  physics Physics {
    mass 3000
  }
}
ConveyorBelt {
  translation -12.3499 -16.7841 3.48166e-13
  size 25 0.8 0.6
}
Forklift {
  translation 9.50423 2.88823 0.8
  rotation 0 0 1 1.59044
}
Forklift {
  translation 4.76445 -14.7002 0.8
  rotation 0 0 -1 3.12195
  name "forklift(1)"
}
MecanumBotLidar {
  translation -9.75612 -10.139 0.0991947
  rotation -5.6295978179475545e-05 0.00010715313549243485 0.9999999926744844 -3.103316183938171
  controller "webots_rosa"
  controllerArgs [
    "mecanumbot_lidar.yaml"
  ]
}
