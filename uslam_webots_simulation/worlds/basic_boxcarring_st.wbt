#VRML_SIM R2025a utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/floors/protos/Floor.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/ThreadMetalPlate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Door.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/GlossyPaint.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pipes/protos/LJoint.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pallet/protos/WoodenPallet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/CardboardBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pallet/protos/WoodenPalletStack.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/containers/protos/WoodenBox.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/valves/protos/LeverValve.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/valves/protos/LargeValve.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/pipes/protos/PipeSection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/factory/valves/protos/SmallValve.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/apartment_structure/protos/Wall.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/Roughcast.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/cabinet/protos/Cabinet.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/hospital/protos/EmergencyExitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/stairs/protos/StraightStairsRail.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/stairs/protos/StraightStairs.proto"
EXTERNPROTO "../protos/MecanumBotLidar.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/CarpetFibers.proto"
EXTERNPROTO "../protos/QrPlasticCrate.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/objects/street_furniture/protos/Fence.proto"

WorldInfo {
  basicTimeStep 16
  contactProperties [
    ContactProperties {
      softERP 0.6
      softCFM 1e-05
    }
    ContactProperties {
      material1 "ExteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation 0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
    ContactProperties {
      material1 "InteriorWheelMat"
      coulombFriction [
        0, 2, 0
      ]
      frictionRotation -0.785 0
      bounce 0
      forceDependentSlip [
        10, 0
      ]
    }
  ]
}
Viewpoint {
  orientation 0.5933152799771579 0.4234614185215311 -0.6845855721305841 2.2154988394541055
  position 1.0777703619198995 4.9396907197507955 40.28725559694511
}
TexturedBackground {
  texture "empty_office"
}
PointLight {
  attenuation 0 0 1
  intensity 10.8
  location -4 -4 5
  castShadows TRUE
}
PointLight {
  attenuation 0 0 1
  intensity 9.6
  location 5 -4 5
}
Floor {
  translation 0 -4.3 0
  name "floor(1)"
  size 20 16.4
  tileSize 1 1
  appearance ThreadMetalPlate {
  }
}
Wall {
  translation 2.26 -8.01 0
  name "wall(5)"
  size 0.05 9 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
Wall {
  translation -6.91 -6.05 0
  name "wall(6)"
  size 0.05 7.5 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
Wall {
  translation -5.57 -2.28 0
  rotation 0 0 1 1.5708
  name "wall(8)"
  size 0.05 2.7 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
Wall {
  translation -5.08 -9.82 0.26
  rotation 0 0 1 1.5708
  name "wall(10)"
  size 0.05 3.7 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
Wall {
  translation -8.88 -9.82 0.26
  rotation 0 0 1 1.5708
  name "wall(11)"
  size 0.05 2 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
WoodenBox {
  translation -2.20432 -10.2419 0.3
  rotation 1 0 0 1.5707963267948966
  name "wooden box(5)"
}
Wall {
  translation -6.07 -1.27 0
  rotation 0 0 1 1.5708
  name "wall(9)"
  size 0.05 3.7 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
Wall {
  translation -7.9 -5.51 0
  name "wall(7)"
  size 0.05 8.5 1.2
  appearance Roughcast {
    colorOverride 0.7 0.7 0.5
    textureTransform TextureTransform {
      scale 2.4 1
    }
  }
}
Wall {
  translation -6.91789 2.19201 -7.10543e-15
  rotation 0 0 1 1.5708
  name "wall(4)"
  size 0.2 6 2.4
}
WoodenPalletStack {
  translation 3.3688256593025896 -2.0820927504482616 -9.809999999998986e-06
  rotation -6.828417360260549e-19 -3.0059182493527163e-19 1 0.9804955621737348
  name "wooden pallet stack(4)"
  palletNumber 3
  palletSize 1.2 0.8 0.5
  palletLathNumber 2
  palletMass 5
}
Fence {
  translation -7.91 -9.84367 3.55271e-14
  rotation 0 0 1 -5.307179586466759e-06
  height 1.8
  path [
    0 0 0
    0 8 0
  ]
}
Fence {
  translation -9.9356 -9.83304 0
  rotation 0 0 1 -1.5707953071795862
  name "fence(2)"
  height 1.8
  path [
    0 0 0
    0 2 0
  ]
}
Fence {
  translation -3.21496 -9.8302 0
  rotation 0 0 1 1.5708
  name "fence(3)"
  height 1.8
  path [
    0 0 0
    0 3.7 0
  ]
}
Fence {
  translation -6.91106 -9.81769 3.55271e-14
  rotation 0 0 1 -5.307179586466759e-06
  name "fence(1)"
  height 1.8
  path [
    0 0 0
    0 7.5 0
  ]
}
Door {
  hidden translation_5 0.001071039362594367 2.2278091735050687e-05 7.771561172376096e-16
  hidden rotation_5 0 0 -0.9999999999999999 0.00014955303658457464
  translation 10.04 -4 3.27
  rotation 0 0 1 3.141592
  name "door(1)"
  position -0.00029910615914168143
  doorAppearance GlossyPaint {
    baseColor 0.16 0.16 0.16
  }
  frameAppearance GlossyPaint {
    baseColor 0.35 0.3503166247043564 0.35
  }
}
Door {
  hidden translation_5 0.0010710393625945613 2.227809173516171e-05 7.771561172376096e-16
  hidden rotation_5 0 0 -1 0.00014955303658457464
  translation -10.05 0 0.02
  position -0.00029910615914155144
  doorAppearance GlossyPaint {
    baseColor 0.16 0.16 0.16
  }
  frameAppearance GlossyPaint {
    baseColor 0.35 0.3503166247043564 0.35
  }
}
Floor {
  translation 0 -4.22 7
  rotation 1 0 0 3.141592
  size 20 16.4
  tileSize 1 1
  appearance PBRAppearance {
    baseColorMap ImageTexture {
      url [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/asphalt.jpg"
      ]
    }
    roughness 1
    metalness 0
  }
}
LJoint {
  translation -7.571 3.19 0.712
  rotation 0 0 1 -1.5708
  name "L joint pipe(1)"
}
LJoint {
  translation -7.571 3.277 2.422
  rotation -0.577350278331609 0.577350264618634 0.577350264618634 2.094395086923155
}
WoodenPallet {
  translation -3.45 -12.1 0.52
  rotation 0.***************** 0.***************** 0.7745965212991105 -1.82348
}
CardboardBox {
  translation -2.86292 -2.45795 0.31
  rotation 0 0 1 -1.5707963267948966
}
WoodenPalletStack {
  translation -7.58842 -11.6851 -1.50991e-14
  name "wooden pallet stack(1)"
  palletNumber 2
  palletSize 1.2 0.8 0.4
  palletLathNumber 2
}
WoodenPalletStack {
  translation 8.41158 -11.7551 -1.50991e-14
  name "wooden pallet stack(6)"
  palletNumber 2
  palletSize 1.2 0.8 0.4
  palletLathNumber 2
}
WoodenPalletStack {
  translation -9.24342 -7.98964 -1.50991e-14
  rotation 0 0 1 1.0472
  name "wooden pallet stack(5)"
  palletNumber 2
  palletSize 1.2 0.8 0.4
  palletLathNumber 2
}
WoodenPalletStack {
  translation -5.17562 -10.5471 0
  palletNumber 5
  palletSize 1.2 0.8 0.4
  palletLathNumber 2
}
WoodenBox {
  translation 5.568631065421032 -2.805316173820471 0.29999673
  rotation 0.9099087348580387 -0.2933139054230498 -0.29331390542304975 1.66506736008237
  name "wooden box(1)"
  mass 5
}
WoodenBox {
  translation -2.83909 -10.132 0.3
  rotation 1 0 0 1.5707963267948966
  name "wooden box(2)"
}
WoodenBox {
  translation 4.862682365717553 -11.043197634282464 0.299999346
  rotation 0.8628562105201771 0.3574067430580736 0.35740674305807363 1.7177715162441305
  name "wooden box(5)"
  mass 1
}
WoodenBox {
  translation 8.043470172391528 -6.566099624326912 0.29999607599892036
  rotation 0.9999999999999646 7.218407751548247e-08 -2.5656893057268587e-07 1.5707989894142573
  name "wooden box(5)"
  mass 3
}
WoodenBox {
  translation -1.9652 -11.6762 0.3
  rotation 1 0 0 1.5707963267948966
  name "wooden box(5)"
}
WoodenBox {
  translation 4.475416979074424 -2.365504309691685 0.29999672999999993
  rotation 0.6777477137412438 -0.5199317438464965 -0.5199317438464961 1.950322824465019
  name "wooden box(6)"
  mass 5
}
WoodenBox {
  translation -2.20432 -10.2419 0.3
  rotation 1 0 0 1.5707963267948966
  name "wooden box(7)"
}
WoodenBox {
  translation -2.20432 -10.9719 0.3
  rotation 1 0 0 1.5707963267948966
  name "wooden box(5)"
}
WoodenBox {
  translation 8.058550397171873 -6.688236529319778 0.8999936543675746
  rotation 0.999999999999927 1.5021031506549912e-07 -3.515018991301675e-07 1.5708010881951995
  name "wooden box(8)"
  mass 3
}
WoodenBox {
  translation -1.95351 -11.7264 1.52
  rotation 1 0 0 1.5707963267948966
  name "wooden box(9)"
}
WoodenBox {
  translation -1.9634 -11.6692 0.93
  rotation 1 0 0 1.5707963267948966
}
LeverValve {
  hidden translation_1 0 1.4421768076460106e-08 -5.207639874882375e-15
  hidden rotation_1 -1 0 0 7.208220137624675e-07
  translation -7.5791585 3 0.692
  rotation 0 0 1 -1.5708
}
LargeValve {
  hidden translation_1 0 -2.2713284758758013e-08 -2.7150157122513008e-14
  hidden rotation_1 1 0 0 2.3908819103399246e-06
  translation -5.7149744 2.9726835 0.706
  rotation 0 0 1 -1.5708
}
PipeSection {
  translation -7.5740452 3 0.18
  name "pipe section(1)"
  height 1.2
}
PipeSection {
  translation -5.2940471 3 0.7
  rotation 0 1 0 1.5708
  name "pipe section(2)"
  height 0.9
  radius 0.02
}
PipeSection {
  translation -7.5720026 3.234 1.56
  name "pipe section(3)"
  height 1.6
}
PipeSection {
  translation -4.8579962 3 0.35
  name "pipe section(4)"
  height 0.7
  radius 0.02
}
PipeSection {
  translation -6.6249962 3 0.70600102
  name "pipe section(5)"
  height 1.7
}
PipeSection {
  translation -7.572 3.74 2.4659975
  rotation 1 0 0 -1.5708
  name "pipe section(6)"
  height 0.8
}
PipeSection {
  translation -5.714996 3.37 0.65600436
  rotation 1 0 0 1.5708
  height 0.8
}
SmallValve {
  hidden translation_1 0.005999999999999987 2.816042033925109e-10 -0.0204
  hidden rotation_1 1 0 0 3.141629427608358
  translation -4.8638332 3 0.707
  rotation 0 0 1 -1.5708
}
Wall {
  translation 0 3.82 0
  name "wall(1)"
  size 20 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 5 1.75
    }
  }
}
Cabinet {
  translation -0.46 3.84 0
  rotation 0 0 1 -1.5707963267948966
  name "cabinet(1)"
  columnsWidths [
    1.3, 0.17, 0.17
  ]
  layout [
    "Shelf (1, 5, 3, 0)"
    "Shelf (1, 4, 3, 0)"
    "Shelf (1, 3, 3, 0)"
    "Shelf (1, 2, 3, 0)"
  ]
}
Cabinet {
  hidden translation_7 -7.964539192356307e-06 -6.375266980995775e-11 0
  hidden rotation_7 0 0 1 1.600911854574189e-05
  hidden translation_11 -7.96453919178524e-06 6.375266980995775e-11 0
  hidden rotation_11 0 0 -1 1.600911854574189e-05
  translation 1.49 3.84 0
  rotation 0 0 1 -1.5707963267948966
  columnsWidths [
    1, 1
  ]
  layout [
    "RightSidedDoor (1, 1, 1, 5, 1.5)"
    "LeftSidedDoor (2, 1, 1, 5, 1.5)"
  ]
}
Wall {
  translation 0 -12.5 0
  name "wall(2)"
  size 20 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 5 1.75
    }
  }
}
Wall {
  translation -10 -4.3 0
  rotation 0 0 1 1.5708
  name "wall(3)"
  size 16.5 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 4.125 1.725
    }
  }
}
Wall {
  translation 10 -4.3 0
  rotation 0 0 1 -1.5708
  size 16.5 0.2 7
  appearance Roughcast {
    textureTransform TextureTransform {
      scale 4.125 1.725
    }
  }
}
EmergencyExitSign {
  translation 9.82 -4.02 5.45
  rotation 0.5773509358560258 -0.577349935856137 -0.577349935856137 2.09439
}
EmergencyExitSign {
  translation -9.83 -0.01 2.3
  rotation 0.5773489358550934 0.5773519358547601 0.5773499358549823 2.0944
  name "emergency exit sign(1)"
}
Solid {
  translation 9.25 -3.95 3.21
  rotation 1 0 0 1.5707963267948966
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/default/worlds/textures/steel_floor.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 2 2
        }
      }
      geometry Box {
        size 1.5 0.12 2.6
      }
    }
  ]
}
StraightStairsRail {
  translation 8.52 -2.73 3.27
  rotation 0 0 1 -1.5708
  name "straight stairs rail(1)"
  run 2.5
  rise 0
  newelHeight 0.89
  balusterHeight 0.9
  nBalusters 9
  appearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
    }
  }
}
StraightStairsRail {
  translation 8.57 -5.23 3.27
  run 1.3
  rise 0
  newelHeight 0.89
  balusterHeight 0.9
  nBalusters 5
  appearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
    }
  }
}
StraightStairs {
  translation 9.201 0.17 0
  rotation 0 0 1 -1.5708
  stepSize 0.3 1.34 0.01
  stepRise 0.297
  nSteps 10
  stepAppearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
      scale 3 1
    }
  }
  stringerAppearance PBRAppearance {
    baseColor 0.8 0.8 0.8
    roughness 0.5
    metalness 0
    textureTransform TextureTransform {
      rotation 1.5708
      scale 3 3
    }
  }
  leftRail []
  rightRail [
    StraightStairsRail {
      run 3
      rise 2.97
      newelHeight 0.862
      balusterHeight 0.83
      nBalusters 12
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          rotation 1.5708
        }
      }
    }
  ]
}
DEF plastic_crate_stack Pose {
  translation 0 -10 0
  children [
    Solid {
      translation 0.329 8.267 4.88498e-15
      children [
        Shape {
          appearance CarpetFibers {
            colorOverride 0.8 0.8 0.8
          }
          geometry Box {
            size 2 1.5 0.001
          }
        }
      ]
      name "solid(1)"
    }
    Solid {
      translation 0.328927 0.267045 -0.0001
      children [
        Shape {
          appearance CarpetFibers {
            colorOverride 0.8 0.8 0.8
          }
          geometry Box {
            size 2 1.5 0.001
          }
        }
      ]
      name "solid(2)"
    }
    Solid {
      translation 0.329 4.26131 -0.0001
      children [
        Shape {
          appearance CarpetFibers {
            colorOverride 0.8 0.8 0.8
          }
          geometry Box {
            size 1 6.5 0.001
          }
        }
      ]
      name "solid(3)"
    }
    QrPlasticCrate {
      translation 0.6152713688557648 0.5433521133583632 0.29999689288700476
      rotation -1.3336584226660919e-05 1.3368300015440335e-05 -0.9999999998217122 0.033380691931345984
      name "plastic crate(9)"
      size 0.6 0.5 0.3
      color 1 0.8 0
      mass 1
      children [
        Solid {
          translation 0 0.26 0.15
          rotation 0 0.7071067811865475 0.7071067811865475 3.14
          children [
            Shape {
              appearance Appearance {
                material Material {
                  ambientIntensity 0.8
                  emissiveColor 0.1 0.1 0.1
                }
                texture ImageTexture {
                  url [
                    "textures/tag36h11_027.jpg"
                  ]
                }
              }
              geometry Plane {
                size 0.2 0.2
              }
            }
          ]
          name "tag36h11_027.jpg"
        }
      ]
    }
    QrPlasticCrate {
      translation 0.6151485914751846 0.038089608382300355 0.2999969389605077
      rotation 1.0081640148039404e-05 1.762497504535166e-05 -0.9999999997938603 0.035765019538025175
      name "plastic crate(4)"
      size 0.6 0.5 0.3
      color 1 0.8 0
      mass 1
      children [
        Solid {
          translation 0 -0.26 0.15
          rotation 1 0 0 1.57
          children [
            Shape {
              appearance Appearance {
                material Material {
                  ambientIntensity 0.8
                  emissiveColor 0.1 0.1 0.1
                }
                texture ImageTexture {
                  url [
                    "textures/tag36h11_120.jpg"
                  ]
                }
              }
              geometry Plane {
                size 0.2 0.2
              }
            }
          ]
          name "tag36h11_120"
        }
      ]
    }
    QrPlasticCrate {
      translation 0.6121996095482867 0.5451961673779344 -2.616000000010832e-06
      rotation -1.1542476090025939e-05 1.1635835931469217e-05 -0.9999999998656892 0.030187776984479078
      name "plastic crate(3)"
      size 0.6 0.5 0.3
      color 0 0.3 1
      mass 1
    }
    QrPlasticCrate {
      translation 0.6045623976196203 0.03244145120218356 -2.615999999996954e-06
      rotation 3.891613069583073e-06 6.027208113699418e-06 -0.9999999999742639 0.06843118725989918
      name "plastic crate(8)"
      size 0.6 0.5 0.3
      color 0 0.3 1
      mass 1
    }
    QrPlasticCrate {
      translation 0.6161642397401493 0.013754625496691375 0.8999954827042346
      rotation -6.100034894299187e-05 -3.587652309524212e-05 -0.9999999974959164 0.03466111689174302
      name "plastic crate(7)"
      size 0.6 0.5 0.3
      color 0.5 0.5 0.5
      mass 1
    }
    QrPlasticCrate {
      translation 0.6475308033595821 0.5802535207918424 0.8999958259902993
      rotation 0.00043411701629023536 4.769247637010024e-07 -0.9999999057710899 0.004282022217402305
      name "plastic crate(11)"
      size 0.6 0.5 0.3
      color 0.5 0.5 0.5
      mass 1
    }
    QrPlasticCrate {
      translation -0.029839581994418905 0.02351691768966191 0.5999966951503549
      rotation 6.772250996694004e-07 1.3832644968828458e-05 0.9999999999040997 0.026286297032435693
      name "plastic crate(2)"
      size 0.6 0.5 0.3
      mass 1
    }
    QrPlasticCrate {
      translation -0.00582458999843214 0.5341266676827399 0.599996620533896
      rotation -4.16861313591248e-05 5.066551282650927e-06 0.9999999991182982 0.013861204099830141
      name "plastic crate(14)"
      size 0.6 0.5 0.3
      color 1 0.8 0
      mass 1
    }
    QrPlasticCrate {
      translation 0.6385604061982176 0.01749037614675153 0.5999957421929347
      rotation -5.905171744397119e-05 -4.095493435412988e-05 -0.999999997417794 0.036052353729086835
      name "plastic crate(5)"
      size 0.6 0.5 0.3
      mass 1
    }
    QrPlasticCrate {
      translation 0.6224024939575319 0.5443312638931985 0.5999965967434311
      rotation -2.6557184669765926e-05 2.805651187753353e-05 -0.9999999992537741 0.02004046958804058
      name "plastic crate(10)"
      size 0.6 0.5 0.3
      mass 1
    }
    QrPlasticCrate {
      translation -0.022133671515551564 0.025421717756243467 0.29999698478792103
      rotation 6.207050353869183e-07 7.555470378336916e-06 0.9999999999712648 0.03634473262632052
      name "plastic crate(1)"
      size 0.6 0.5 0.3
      color 0 0 1
      mass 1
      children [
        Solid {
          translation 0 -0.26 0.15
          rotation 1 0 0 1.57
          children [
            Shape {
              appearance Appearance {
                material Material {
                  ambientIntensity 0.8
                  emissiveColor 0.1 0.1 0.1
                }
                texture ImageTexture {
                  url [
                    "textures/tag36h11_028.jpg"
                  ]
                }
              }
              geometry Plane {
                size 0.2 0.2
              }
            }
          ]
          name "tag36h11_028.jpg"
        }
      ]
    }
    QrPlasticCrate {
      translation -0.004490852306313427 0.5453300479817642 0.2999969412062803
      rotation -4.532883597663512e-05 6.046769311218573e-06 0.9999999989543668 0.011489441184164697
      name "plastic crate(13)"
      size 0.6 0.5 0.3
      color 0.5 0.5 0.5
      mass 1
      children [
        Solid {
          translation 0 0.26 0.15
          rotation 0 0.7071067811865475 0.7071067811865475 3.14
          children [
            Shape {
              appearance Appearance {
                material Material {
                  ambientIntensity 0.8
                  emissiveColor 0.1 0.1 0.1
                }
                texture ImageTexture {
                  url [
                    "textures/tag36h11_050.jpg"
                  ]
                }
              }
              geometry Plane {
                size 0.2 0.2
              }
            }
          ]
          name "tag36h11_050"
        }
      ]
    }
    QrPlasticCrate {
      translation -0.008324293591474824 0.02685437976859184 0.899996495236302
      rotation -3.732825239638306e-06 1.242611974364391e-05 0.9999999999158288 0.023769517629064256
      name "plastic crate(6)"
      size 0.6 0.5 0.3
      color 0.5 0.5 0.5
      mass 1
    }
    QrPlasticCrate {
      translation -0.0034482028605376065 0.5685099146309742 0.8999963672374597
      rotation -2.4210893848005212e-05 6.0427170133902945e-06 0.999999999688659 0.012925276035626035
      name "plastic crate(15)"
      size 0.6 0.5 0.3
      color 0.5 0.5 0.5
      mass 1
    }
    QrPlasticCrate {
      translation -0.026504941172123476 0.0297246273763232 -2.6159997625757603e-06
      rotation 2.588332367238681e-06 2.426058718429041e-06 0.9999999999937074 0.05694741389920913
      size 0.6 0.5 0.3
      color 0 0 1
      mass 1
    }
    QrPlasticCrate {
      translation -0.007325571544531807 0.5393608537512034 -2.6159999999691985e-06
      rotation -1.0827470281939862e-05 2.1386384510128677e-06 0.999999999939096 0.028726633173718413
      name "plastic crate(12)"
      size 0.6 0.5 0.3
      mass 1
    }
  ]
}
DEF woodenbox2 Pose {
  translation 6.21 3 0
  children [
    WoodenBox {
      translation -1.560173534970236 -10.728489528306913 0.8999915801821523
      rotation 0.9351127869763501 -0.2505618613187371 -0.25056502007625314 1.6378353037918818
      name "wooden box(3)"
      mass 5
    }
    WoodenBox {
      translation -2.7887667628986073 -8.58097744249677 0.29999163272881013
      rotation 0.9999992201582923 -0.0008826239752951615 -0.0008835483719585297 1.5707975949091217
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -1.6218487616816901 -10.718399544211023 0.2999934599992266
      rotation 0.862856353064168 0.35740747226994635 0.35740566971217963 1.717772633693188
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.06992094034836249 -10.239297797850568 0.29999345999606847
      rotation 0.9999999999994613 -7.339825588449102e-07 7.339832420382892e-07 1.570791422149749
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.55055984009892 -12.183849196505038 0.29999345999897054
      rotation 0.9999370563297242 -0.00793472149660216 -0.007932438050028755 1.5708605564768554
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.9989094663843145 -7.739529073061961 0.29999673000000004
      rotation 0.9999999999992029 8.928470462960515e-07 8.928470462963048e-07 1.5707963267956937
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.619142 -8.61211 0.299997
      rotation 1 4.087240591606386e-18 4.841450178489677e-18 1.5707963267948968
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation 1.3747300002554272 -11.994899999873406 0.14999673
      rotation 6.697925350631196e-10 -3.6665873618422616e-09 1 2.2925297769226626e-10
      name "wooden box(4)"
      size 0.7 1 0.3
      mass 5
    }
    WoodenBox {
      translation -3.4949 -10.488699269245004 0.29999672999999993
      rotation 1 2.8610945343631224e-18 1.346208388899439e-18 1.5707963267948968
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.502713020925577 -10.643784309691684 0.29999672999999993
      rotation 0.6777477137412439 -0.5199317438464964 -0.5199317438464959 1.950322824465019
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.4108736915036286 -12.155578956537756 0.29999345999969224
      rotation 0.9999999875755083 -0.00011192292271067916 -0.00011100559574949417 1.5707974393635606
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation 0.636813 -7.51021 0.299997
      rotation 1 3.782389089702192e-18 4.9155237566905744e-18 1.5707963267948968
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.8764216568542409 -3.421832606469631 0.2999967299999999
      rotation 0.9831058400113504 0.12942740694222649 0.1294274069422265 1.587833996669093
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.11032268896202435 -10.10431049236044 0.8999891760423699
      rotation 0.999999999998614 -1.379301622189887e-06 9.324462213290421e-07 1.5707873395474792
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.7771236047507974 -8.547849461089298 0.8999865638231168
      rotation 0.9999992161088088 0.0008861449724997471 0.000884606610705964 1.5707991993644177
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation 2.831039999999999 -8.08766 0.29999673000000004
      rotation 2.1252328658563887e-18 2.5369425266076178e-18 1 0.523599
      name "wooden box(4)"
      size 1.5 0.6 0.6
      mass 5
    }
    WoodenBox {
      translation 1.7675399999999994 -7.6096 0.29999673
      rotation -0.937245809796361 -3.2866552535745726e-14 0.34866931614233515 1.212988487473903e-18
      name "wooden box(4)"
      size 1.5 0.6 0.6
      mass 5
    }
    WoodenBox {
      translation 0.8380674371855408 -4.2843482174291205 1.79998174211399
      rotation -0.14522231179623266 -0.9893121367792349 -0.013113968806667804 6.40956728681956e-06
      name "wooden box(4)"
      size 1.5 0.6 0.6
      mass 5
    }
    WoodenBox {
      translation -2.2031860430076504 -8.573417185845294 1.4999845858591698
      rotation 0.000404007124664966 -5.982651718728806e-06 0.9999999183712222 0.010821064532358022
      name "wooden box(4)"
      size 1.5 0.6 0.6
      mass 5
    }
    WoodenBox {
      translation -1.2359210686211375 -8.473730297490583 0.8999870242122955
      rotation 0.999697587688946 -0.01738955948059356 -0.017387823037200722 1.5711044889343715
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -0.6136860327687161 -12.218687871813506 0.8999899436015489
      rotation 0.9999463438171129 -0.007326792965101639 -0.0073230862097160496 1.570851992063331
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -2.436124061018112 -12.185854679350179 0.8999901179842126
      rotation 0.9999999899297034 -0.00010105694979779779 -9.963978065231566e-05 1.5707980470884333
      name "wooden box(4)"
      mass 5
    }
    WoodenBox {
      translation -1.3262915686291192 -8.401117285332772 0.2999920172690745
      rotation 0.9997882983380529 -0.01454886796189975 -0.014549534264979031 1.5710115960912272
      name "wooden box(4)"
      mass 5
    }
  ]
}
WoodenPalletStack {
  translation 2.967210004503247 -4.144529990555198 -9.809999999998986e-06
  rotation -1.2345097406442427e-10 1.4562710466856902e-10 -1 7.156269520920918e-09
  name "wooden pallet stack(2)"
  palletNumber 3
  palletSize 1.2 0.8 0.5
  palletLathNumber 2
  palletMass 5
}
WoodenPalletStack {
  translation 7.388300044637616 -1.335659921013335 -1.3079999999998648e-05
  rotation -0.28425780045419086 -0.958404341970969 -0.025663596243303954 3.2744990544544877e-06
  name "wooden pallet stack(3)"
  palletNumber 3
  palletSize 1.2 0.8 0.5
  palletLathNumber 2
  palletMass 5
}
Pedestrian {
  translation -5.90211 -8.92919 1.27
  name "pedestrian(1)"
  controllerArgs [
    "--speed= 1"
    "--trajectory= -6 -8, -6 -3.5, 2 -3.5"
  ]
  enableBoundingObject TRUE
  bodySlot [
    Solid {
      translation 0.011389898883969352 -0.0019182394864754038 -1.1348696154414908
      rotation -0.17361126734662444 0.9628754794472848 0.20671221282125576 0.0038063180112233863
      children [
        DEF ped_box_pos Pose {
        }
      ]
      name "solid(1)"
      boundingObject Box {
        size 0.5 0.5 1.8
      }
      physics Physics {
        density -1
        mass 70
        centerOfMass [
          0 0 0
        ]
      }
    }
  ]
}
Pedestrian {
  translation 6.84349 1.58758 1.37
  rotation 0 0 1 3.14159
  name "pedestrian(2)"
  controllerArgs [
    "--speed= 1"
    "--trajectory= 6.84 1.59, -6 1.59"
  ]
  enableBoundingObject TRUE
  bodySlot [
    Solid {
      translation 0.011389898883969352 -0.0019182394864754038 -1.1348696154414908
      rotation -0.17361126734662444 0.9628754794472848 0.20671221282125576 0.0038063180112233863
      children [
        DEF ped_box_pos Pose {
        }
      ]
      name "solid(1)"
      boundingObject Box {
        size 0.5 0.5 1.8
      }
      physics Physics {
        density -1
        mass 70
        centerOfMass [
          0 0 0
        ]
      }
    }
  ]
}
Pedestrian {
  translation -3 0 1.26
  controllerArgs [
    "--trajectory= -3 0, 3 0"
    "--speed=2.5"
  ]
}
MecanumBotLidar {
  translation 0.36219434465273687 -6.08662761721712 0.10207525590755165
  rotation 0.00018737895749016884 -0.0002910189569260631 0.9999999400985445 3.1240610952222805
  controller "webots_rosa"
  controllerArgs [
    "mecanumbot_lidar.yaml"
  ]
}
