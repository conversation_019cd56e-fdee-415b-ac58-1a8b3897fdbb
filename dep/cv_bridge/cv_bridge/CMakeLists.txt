cmake_minimum_required(VERSION 3.14)
project(cv_bridge)

find_package(ament_cmake REQUIRED)

# find_package(rosa REQUIRED)
find_package(rosa_cmake REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(shm_msgs REQUIRED)

find_package(OpenCV 4 QUIET
  COMPONENTS
  opencv_core
  opencv_imgproc
  opencv_imgcodecs
  CONFIG
)

if(NOT OpenCV_FOUND)
  find_package(OpenCV 3 REQUIRED
    COMPONENTS
    opencv_core
    opencv_imgproc
    opencv_imgcodecs
    CONFIG
  )
endif()

add_library(${PROJECT_NAME} SHARED
  src/cv_bridge.cpp
  src/cv_mat_sensor_msgs_image_type_adapter.cpp
  src/rgb_colors.cpp
)

# include(GenerateExportHeader)
# generate_export_header(${PROJECT_NAME} EXPORT_FILE_NAME ${PROJECT_NAME}/${PROJECT_NAME}_export.h)
target_include_directories(${PROJECT_NAME} PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

target_link_libraries(${PROJECT_NAME} PUBLIC
  ${sensor_msgs_TARGETS}
  opencv_core
  opencv_imgproc
  opencv_imgcodecs
  shm_msgs::shm_msgs
  # rosa::rosa
)

# target_link_libraries(${PROJECT_NAME} PRIVATE
# )
if(BUILD_TESTING)
  find_package(ament_cmake_gtest REQUIRED)

  ament_add_gtest(${PROJECT_NAME}_test
    tests/test_endian.cpp
    tests/test_compression.cpp
    tests/utest.cpp
    tests/utest2.cpp
    tests/test_rgb_colors.cpp
    tests/test_dynamic_scaling.cpp
  )

  target_link_libraries(${PROJECT_NAME}_test
    ${PROJECT_NAME}

    # Boost::headers
    opencv_core
    opencv_imgcodecs
    ${sensor_msgs_TARGETS}
  )
endif()

# install the include folder
install(DIRECTORY include/ DESTINATION include)

install(
  TARGETS ${PROJECT_NAME}
  EXPORT export_${PROJECT_NAME}
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib
)

ament_export_targets(export_${PROJECT_NAME} HAS_LIBRARY_TARGET)
ament_export_dependencies(
  OpenCV
  sensor_msgs
  shm_msgs
  rosa
)

ament_package(
  CONFIG_EXTRAS "cmake/cv_bridge-extras.cmake.in"
)
