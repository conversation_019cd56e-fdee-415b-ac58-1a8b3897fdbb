include:
  - project: "pub/ci_templates"
    ref: dynamic_ci
    file: "dynamic.yml"

stages:
  - webhook
  - generate
  - trigger

generate:ci:
  extends:
    - .generate:ci
  only:
    - main
    - tags
    - merge_requests

trigger:ci:
  extends:
    - .trigger:ci
  only:
    - main
    - tags
    - merge_requests

webhook:ros2:
  stage: webhook
  image: glcr.rd.ubtrobot.com/pub/hub/curl:latest
  tags:
    - docker
  script:
    - "curl -X POST --fail -F token=$ROS2_WS_CI_TOKEN -F ref=main \
      https://gitlab.rd.ubtrobot.com/api/v4/projects/3332/trigger/pipeline"
  only:
    - main
