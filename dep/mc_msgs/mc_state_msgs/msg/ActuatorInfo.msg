# This message contains the state of an actuator
# An actuator contains a motor and an encoder, and is connected to a joint by a transmission
string name
string type

uint32 id

builtin_interfaces/Time stamp

# The encoder raw position, represented by the number of encoder ticks
float64 position_raw

# The temperature of the motor, represented by c1elsius
float64 temperature

# The circle of absolute encoder
int64 circle

# The encoder position in radians
float64 position

# The encoder velocity in radians per second
float64 velocity

# The last effort that was measured by the actuator
float64 effort

# electric current
float64 current

# The control mode
int8 control_mode

# The last pos command that was requested without limit
float64 cmd_pos

# The last vel command that was requested without limit
float64 cmd_vel

# The last effort command that was requested without limit
float64 cmd_effort

# The last v1 command that was requested without limit
float64 cmd_v1

# The v2 vel command that was requested without limit
float64 cmd_v2

# The v3 effort command that was requested without limit
float64 cmd_v3

# The status of the motor
uint16 status
