######################## 任务状态 ##########################
# 抓取任务执行返回成功
uint32 SUCCEED = 1105001
string SUCCEED_STR = "Succeed"

# 抓取任务执行反馈空闲
uint32 IDLE = 1105002
string IDLE_STR = "Idle"

# 抓取任务执行反馈被占用
uint32 BUSY = 1105003
string BUSY_STR = "Busy"

# 抓取任务执行反馈被取消
uint32 CANCEL = 1105004
string CANCEL_STR = "Cancel"

# 抓取解析任务
uint32 RESOLVE_TASK = 1105005
string RESOLVE_TASK_STR = "ResolveTask"

# 抓取任务解析失败
uint32 ERROR_RESOLVE_TASK = 5105001
string ERROR_RESOLVE_TASK_STR = "ErrorResolveTask"

# 视觉数据为空
uint32 VISION_DATA_EMPTY = 7105001
string VISION_DATA_EMPTY_STR = "VisionDataEmpty"
