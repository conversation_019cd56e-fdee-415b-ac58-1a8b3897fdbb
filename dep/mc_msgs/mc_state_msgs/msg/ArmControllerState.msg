######################## 任务状态 ##########################
# 手臂任务执行返回成功
uint32 SUCCEED = 1101001
string SUCCEED_STR = "Succeed"

# 手臂任务执行反馈空闲
uint32 IDLE = 1101002
string IDLE_STR = "Idle"

# 手臂任务执行反馈被占用
uint32 BUSY = 1101003
string BUSY_STR = "Busy"

# 手臂任务执行反馈被取消
uint32 CANCEL = 1101004
string CANCEL_STR = " Cancel "

# 手臂无该任务名
uint32 NO_TASK = 4101001
string NO_TASK_STR = "NoTask"

# 手臂解析任务
uint32 RESOLVE_TASK = 1101005
string RESOLVE_TASK_STR = "ErrorResolveTask"

# 手臂解析任务失败
uint32 ERROR_RESOLVE_TASK = 5101001
string ERROR_RESOLVE_TASK_STR = "ErrorResolveTask"

# 手臂任务加载插件失败
uint32 ERROR_LOAD_PLUGINS = 5101001
string ERROR_LOAD_PLUGINS_STR = "ErrorLoadPlugins"

# 手臂任务执行错误
uint32 ERROR  = 5101001
string ERROR_STR = "Error"

# 手臂任务执行返回成功
uint32 HEAD_SUCCEED = 2101001
string HEAD_SUCCEED_STR = "Head Succeed"

# 头部任务执行中
uint32 HEAD_RUNNING  = 2101003
string HEAD_RUNNING_STR = "Head Running"

######################## 算法状态 ##########################
# 静态视觉任务没有识别到物体
uint32 STATIC_VISION_NO_OBJECT = 7101001
string STATIC_VISION_NO_OBJECT_STR = "No Object"

# 静态视觉任务目标点超出工作空间
uint32 STATIC_VISION_OVER_LIMIT = 7101002
string STATIC_VISION_OVER_LIMIT_STR = "Over Limit"

# 没有夹到箱子
uint32 NO_CLAMP = 7101003
string NO_CLAMP_STR = "NO Clamp"

#hqp求解连续失败
uint32 NO_SOLUTION = 7101004
string NO_SOLUTION_STR = "HQP solution failed"

#搬运过程中的状态异常，需要人为介入
uint32 ABNORMAL_STATUS= 9101001
string ABNORMAL_STATUS_STR = "The arm status is abnormal and requires intervention"
