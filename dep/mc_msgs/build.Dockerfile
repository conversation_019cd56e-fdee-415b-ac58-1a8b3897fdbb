ARG BASE_IMAGE
ARG CROSS_COMPILE_IMAGE
ARG UBT_3RD_IMAGE
ARG ROSA_IMAGE

FROM $UBT_3RD_IMAGE as ubt_3rd_image
FROM $ROSA_IMAGE as rosa_image
FROM $BASE_IMAGE as base_image

FROM --platform=$BUILDPLATFORM $CROSS_COMPILE_IMAGE as cross_compile_image

ARG INSTALL_PATH
ARG TARGETPLATFORM
ARG BUILDPLATFORM

USER root

COPY . /opt/src
WORKDIR /opt/src

RUN --mount=type=ssh \
    --mount=type=bind,from=base_image,source=/,target=/sysroot,rw \
    --mount=type=bind,from=ubt_3rd_image,source=/opt/ubt_3rdparty,target=/opt/ubt_3rdparty,rw \
    --mount=type=bind,from=rosa_image,source=/opt/rosa,target=/opt/rosa,rw \
    ././build.sh

FROM glcr.rd.ubtrobot.com/pub/hub/busybox:musl
ARG INSTALL_PATH
COPY --from=cross_compile_image $INSTALL_PATH $INSTALL_PATH
