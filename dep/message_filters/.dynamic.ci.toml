# 动态 ci 配置文件
# 该配置将以下文件进行整合

# 1. env 目录
# 2. build.sh 脚本
# 3. Dockerfile 交叉编译文件
# 4. toolchain.cmake 交叉编译文件
# 5. pack.yml 打包文件


# 基本 check 流程
[check]

json = true
yaml = true
toml = true
spell = true
markdown_cn = true
trailing_space = true
newline_eof = true
copy_paste_report = true
copy_paste_report_threshold = 10

# 基本 lint 流程
[lint]

# 两种格式，一种设置了 clang-format 使用的命令，目前只有 10 和 12 两种可选
cpp = true
# cpp = { enable = true, clang_format_bin = "clang-format-12" }
python = true
cmake = true

[test.cpp]

# merge_requests 开启测试， 不开启不会进行测试
merge_requests = true

# 输出测试报告
code_coverage = true
code_coverage_threshold = 0

system = ["ubuntu20.04", "ubuntu22.04", "debian12"]
# 需要按模块出测试报告，如果留空则会输出所有的测试报告。
# 如果填入内容，只会输出对应模块的测试报告
need_packages = []

# 测试时脚本处理
[test.cpp.scripts.common]

before_build = []
after_build = []
cmake_args = []

[test.cpp.scripts.arm64]

before_build = []
after_build = []
cmake_args = []

[test.cpp.scripts.amd64]

before_build = []
after_build = []
cmake_args = []

# 基本编译流程
[build.cpp]

# 允许汇入的主分支，默认为 main
main_branch = ["main"]

# 内置编译处理，只有在 merge_requests 和 tags 时才会真正的编译
merge_requests = true # 如果不开启，汇入主分支的 build 开启了也不会执行
tags = true           # 默认为 true

# 编译时脚本处理
[build.cpp.scripts.common]

before_build = []
after_build = []
cmake_args = []

[build.cpp.scripts.arm64]

before_build = []
after_build = []
cmake_args = []

[build.cpp.scripts.amd64]

before_build = []
after_build = []
cmake_args = []


# 编译时 toolchain 的处理
[build.cpp.toolchain.common]

# 注入默认 toolchain 里面的特定脚本
context = []

[build.cpp.toolchain.arm64]

context = []

[build.cpp.toolchain.amd64]

context = []

# 打包
[deploy.cpp]

# tags 是否需要打包
tags = true

# 需要打包的分支名称，可以使用正则表达式匹配，需完全匹配
branch = ["main"]

# 打包需要 deb
need_deb = true

# 打包需要 tar
need_tar = false


[deploy.cpp.tags_mapping]

"ubuntu20.04" = { name = "focal" }
"ubuntu22.04" = { name = "jammy" }
debian12 = { name = "bookworm" }
orin-devel = { name = "focal", suffix = "orin" }

[deploy.cpp.deb.config]

# 包名
package_name = "ubt-rosa"

# 版本号
# 可选 写了直接用这个，不写会根据 tag 来取
# 没有 tag 取环境变量 $PKG_VERSION 的值 默认为 latest
# version = ""

# 作者
maintainer = "Yuren Li <<EMAIL>>"

# 描述
description = ""

# 依赖项 list
depends = []

# 安装之前的脚本
pre_install_scripts = []
# 安装之后的脚本
post_install_scripts = []

# 删除之前的脚本
pre_remove_scripts = []
# 删除之后的脚本
post_remove_scripts = []

[param] # 安装之后的脚本

matrix = [
    { TAG = "ubuntu20.04" },
    { TAG = "ubuntu22.04" },
    { TAG = "debian12" },
]

[param.tag]

# 为汇入的主分支设置后缀 tag
main = "" # main 的默认 tag 就是 `空`

# 除了 main，其他主分支的默认 tag 都是 分支名， 如果有 / 会替换成 -

[param.var]

INSTALL_PATH = "/opt/rosa"

[param.image.standard]

BASE_IMAGE = "hb.ua.ubtrobot.com/rosa/image/base:${TAG}"
CROSS_COMPILE_IMAGE = "hb.ua.ubtrobot.com/rosa/images/crosscompile:${TAG}"

[param.image.extensions]

UBT_3RD_IMAGE = { url = "hb.ua.ubtrobot.com/rosa/image/3rdparty:${TAG}", path = "/opt/ubt_3rdparty" }
ROSA_IMAGE = { url = "hb.ua.ubtrobot.com/rosa/rosa:${TAG}", path = "/opt/rosa" }
