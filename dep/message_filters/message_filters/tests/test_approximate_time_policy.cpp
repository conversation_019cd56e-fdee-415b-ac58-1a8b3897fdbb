#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

#include <gtest/gtest.h>

#include "test_helpers.h"

class ApproximateTimeSynchronizerTest
{
 public:
  ApproximateTimeSynchronizerTest(const std::vector<TimeAndTopic>& input,
                                  const std::vector<TimePair>& output,
                                  uint32_t queue_size)
      : input_(input), output_(output), output_position_(0), sync_(queue_size)
  {
    sync_.registerCallback(std::bind(&ApproximateTimeSynchronizerTest::callback,
                                     this, std::placeholders::_1,
                                     std::placeholders::_2));
  }

  void callback(const MsgConstPtr& p, const MsgConstPtr& q)
  {
    ASSERT_TRUE(p);
    ASSERT_TRUE(q);
    ASSERT_LT(output_position_, output_.size());
    EXPECT_EQ(output_[output_position_].first, p->header.stamp);
    EXPECT_EQ(output_[output_position_].second, q->header.stamp);
    ++output_position_;
  }

  void run()
  {
    for (size_t i = 0; i < input_.size(); i++)
    {
      if (input_[i].second == 0)
      {
        MsgPtr p(std::make_shared<Msg>());
        p->header.stamp = input_[i].first;
        sync_.add<0>(p);
      }
      else
      {
        MsgPtr q(std::make_shared<Msg>());
        q->header.stamp = input_[i].first;
        sync_.add<1>(q);
      }
    }
    EXPECT_EQ(output_.size(), output_position_);
  }

 private:
  const std::vector<TimeAndTopic>& input_;
  const std::vector<TimePair>& output_;
  unsigned int output_position_;
  typedef message_filters::Synchronizer<
      message_filters::sync_policies::ApproximateTime<Msg, Msg>>
      Sync2;

 public:
  Sync2 sync_;
};

class ApproximateTimeSynchronizerTestQuad
{
 public:
  ApproximateTimeSynchronizerTestQuad(const std::vector<TimeAndTopic>& input,
                                      const std::vector<TimeQuad>& output,
                                      uint32_t queue_size)
      : input_(input), output_(output), output_position_(0), sync_(queue_size)
  {
    sync_.registerCallback(
        std::bind(&ApproximateTimeSynchronizerTestQuad::callback, this,
                  std::placeholders::_1, std::placeholders::_2,
                  std::placeholders::_3, std::placeholders::_4));
  }

  void callback(const MsgConstPtr& p,
                const MsgConstPtr& q,
                const MsgConstPtr& r,
                const MsgConstPtr& s)
  {
    ASSERT_TRUE(p);
    ASSERT_TRUE(q);
    ASSERT_TRUE(r);
    ASSERT_TRUE(s);
    ASSERT_LT(output_position_, output_.size());
    EXPECT_EQ(output_[output_position_].time[0], p->header.stamp);
    EXPECT_EQ(output_[output_position_].time[1], q->header.stamp);
    EXPECT_EQ(output_[output_position_].time[2], r->header.stamp);
    EXPECT_EQ(output_[output_position_].time[3], s->header.stamp);
    ++output_position_;
  }

  void run()
  {
    for (size_t i = 0; i < input_.size(); i++)
    {
      MsgPtr p(std::make_shared<Msg>());
      p->header.stamp = input_[i].first;
      switch (input_[i].second)
      {
        case 0:
          sync_.add<0>(p);
          break;
        case 1:
          sync_.add<1>(p);
          break;
        case 2:
          sync_.add<2>(p);
          break;
        case 3:
          sync_.add<3>(p);
          break;
      }
    }
    EXPECT_EQ(output_.size(), output_position_);
  }

 private:
  const std::vector<TimeAndTopic>& input_;
  const std::vector<TimeQuad>& output_;
  unsigned int output_position_;
  typedef message_filters::Synchronizer<
      message_filters::sync_policies::ApproximateTime<Msg, Msg, Msg, Msg>>
      Sync4;

 public:
  Sync4 sync_;
};

TEST(ApproxTimeSync, ExactMatch)
{
  // Input A:  a..b..c
  // Input B:  A..B..C
  // Output:   a..b..c
  //           A..B..C

  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  std::vector<TimeAndTopic> input = createInput({{t, 0},
                                                 {t, 1},
                                                 {t + s * 3, 0},
                                                 {t + s * 3, 1},
                                                 {t + s * 6, 0},
                                                 {t + s * 6, 1}});

  std::vector<TimePair> output =
      createOutput({{t, t}, {t + s * 3, t + s * 3}, {t + s * 6, t + s * 6}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 10);
  sync_test.run();
}

TEST(ApproxTimeSync, PerfectMatch)
{
  // Input A:  a..b..c.
  // Input B:  .A..B..C
  // Output:   ...a..b.
  //           ...A..B.

  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  std::vector<TimeAndTopic> input = createInput({{t, 0},
                                                 {t + s, 1},
                                                 {t + s * 3, 0},
                                                 {t + s * 4, 1},
                                                 {t + s * 6, 0},
                                                 {t + s * 7, 1}});

  std::vector<TimePair> output =
      createOutput({{t, t + s}, {t + s * 3, t + s * 4}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 10);
  sync_test.run();
}

TEST(ApproxTimeSync, ImperfectMatch)
{
  // Input A:  a.xb..c.
  // Input B:  .A...B.C
  // Output:   ..a...c.
  //           ..A...B.
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  std::vector<TimeAndTopic> input = createInput({{t, 0},
                                                 {t + s, 1},
                                                 {t + s * 2, 0},
                                                 {t + s * 3, 0},
                                                 {t + s * 5, 1},
                                                 {t + s * 6, 0},
                                                 {t + s * 7, 1}});

  std::vector<TimePair> output =
      createOutput({{t, t + s}, {t + s * 6, t + s * 5}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 10);
  sync_test.run();
}
TEST(ApproxTimeSync, Acceleration)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s * 7, 1},
                            {t + s * 12, 0},
                            {t + s * 15, 1},
                            {t + s * 17, 0},
                            {t + s * 18, 1}});

  auto output =
      createOutput({{t + s * 12, t + s * 7}, {t + s * 17, t + s * 18}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 10);
  sync_test.run();
}

TEST(ApproxTimeSync, DroppedMessages)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s, 1},
                            {t + s * 3, 1},
                            {t + s * 4, 0},
                            {t + s * 7, 1},
                            {t + s * 8, 0},
                            {t + s * 10, 0},
                            {t + s * 11, 1},
                            {t + s * 13, 0},
                            {t + s * 14, 1}});

  auto output =
      createOutput({{t + s * 4, t + s * 3}, {t + s * 10, t + s * 11}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 1);
  sync_test.run();

  auto output2 = createOutput({{t, t + s},
                               {t + s * 4, t + s * 3},
                               {t + s * 8, t + s * 7},
                               {t + s * 10, t + s * 11}});

  ApproximateTimeSynchronizerTest sync_test2(input, output2, 2);
  sync_test2.run();
}

TEST(ApproxTimeSync, LongQueue)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s, 0},
                            {t + s * 2, 0},
                            {t + s * 3, 0},
                            {t + s * 4, 0},
                            {t + s * 5, 0},
                            {t + s * 6, 0},
                            {t + s * 7, 0},
                            {t + s * 8, 0},
                            {t + s * 3, 1},
                            {t + s * 9, 0},
                            {t + s * 10, 0},
                            {t + s * 11, 0},
                            {t + s * 12, 0},
                            {t + s * 10, 1},
                            {t + s * 13, 0}});

  auto output = createOutput({{t + s * 10, t + s * 10}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 5);
  sync_test.run();
}

TEST(ApproxTimeSync, DoublePublish)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input =
      createInput({{t, 0}, {t + s, 1}, {t + s * 3, 1}, {t + s * 3, 0}});

  auto output = createOutput({{t, t + s}, {t + s * 3, t + s * 3}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 10);
  sync_test.run();
}

TEST(ApproxTimeSync, FourTopics)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s, 1},
                            {t + s * 2, 2},
                            {t + s * 3, 3},
                            {t + s * 5, 0},
                            {t + s * 5, 3},
                            {t + s * 6, 1},
                            {t + s * 6, 2},
                            {t + s * 8, 0},
                            {t + s * 9, 1},
                            {t + s * 10, 2},
                            {t + s * 11, 3},
                            {t + s * 10, 0},
                            {t + s * 13, 0},
                            {t + s * 14, 1}});

  auto output =
      createOutputQuad({{t, t + s, t + s * 2, t + s * 3},
                        {t + s * 5, t + s * 6, t + s * 6, t + s * 5},
                        {t + s * 10, t + s * 9, t + s * 10, t + s * 11}});

  ApproximateTimeSynchronizerTestQuad sync_test(input, output, 10);
  sync_test.run();
}

TEST(ApproxTimeSync, EarlyPublish)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput(
      {{t, 0}, {t + s, 1}, {t + s * 2, 2}, {t + s * 3, 3}, {t + s * 7, 0}});

  auto output = createOutputQuad({{t, t + s, t + s * 2, t + s * 3}});

  ApproximateTimeSynchronizerTestQuad sync_test(input, output, 10);
  sync_test.run();
}

TEST(ApproxTimeSync, RateBound)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s, 1},
                            {t + s * 3, 0},
                            {t + s * 4, 1},
                            {t + s * 6, 0},
                            {t + s * 7, 1}});

  auto output = createOutput({{t, t + s}, {t + s * 3, t + s * 4}});

  ApproximateTimeSynchronizerTest sync_test(input, output, 10);
  sync_test.sync_.setInterMessageLowerBound(0, s * 1.5);
  sync_test.run();

  output.push_back(TimePair(t + s * 6, t + s * 7));

  ApproximateTimeSynchronizerTest sync_test2(input, output, 10);
  sync_test2.sync_.setInterMessageLowerBound(0, s * 2);
  sync_test2.run();
}
