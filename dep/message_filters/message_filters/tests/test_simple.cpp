#include <array>
#include <functional>
#include <memory>

#include <gtest/gtest.h>

#include "message_filters/simple_filter.h"

namespace
{
  struct Msg
  {
  };
  typedef std::shared_ptr<Msg> MsgPtr;
  typedef std::shared_ptr<Msg const> MsgConstPtr;

  struct Filter : public message_filters::SimpleFilter<Msg>
  {
    typedef message_filters::MessageEvent<Msg const> EventType;

    void add(const EventType &evt) { signalMessage(evt); }
  };

  class Helper
  {
   public:
    Helper() { counts_.fill(0); }

    void cb0(const MsgConstPtr &) { ++counts_[0]; }

    void cb1(const Msg &) { ++counts_[1]; }

    void cb2(MsgConstPtr) { ++counts_[2]; }

    void cb3(const message_filters::MessageEvent<Msg const> &) { ++counts_[3]; }

    void cb4(Msg) { ++counts_[4]; }

    void cb5(const MsgPtr &) { ++counts_[5]; }

    void cb6(MsgPtr) { ++counts_[6]; }

    void cb7(const message_filters::MessageEvent<Msg> &) { ++counts_[7]; }

    std::array<int32_t, 30> counts_;
  };
}  // namespace

TEST(SimpleFilter, callbackTypes)
{
  Helper h;
  Filter f;
  f.registerCallback(std::bind(&Helper::cb0, &h, std::placeholders::_1));
  f.registerCallback<const Msg &>(
      std::bind(&Helper::cb1, &h, std::placeholders::_1));
  f.registerCallback<MsgConstPtr>(
      std::bind(&Helper::cb2, &h, std::placeholders::_1));
  f.registerCallback<const message_filters::MessageEvent<Msg const> &>(
      std::bind(&Helper::cb3, &h, std::placeholders::_1));
  f.registerCallback<Msg>(std::bind(&Helper::cb4, &h, std::placeholders::_1));
  f.registerCallback<const MsgPtr &>(
      std::bind(&Helper::cb5, &h, std::placeholders::_1));
  f.registerCallback<MsgPtr>(
      std::bind(&Helper::cb6, &h, std::placeholders::_1));
  f.registerCallback<const message_filters::MessageEvent<Msg> &>(
      std::bind(&Helper::cb7, &h, std::placeholders::_1));

  f.add(Filter::EventType(std::make_shared<Msg>()));
  EXPECT_EQ(h.counts_[0], 1);
  EXPECT_EQ(h.counts_[1], 1);
  EXPECT_EQ(h.counts_[2], 1);
  EXPECT_EQ(h.counts_[3], 1);
  EXPECT_EQ(h.counts_[4], 1);
  EXPECT_EQ(h.counts_[5], 1);
  EXPECT_EQ(h.counts_[6], 1);
  EXPECT_EQ(h.counts_[7], 1);
}

struct OldFilter
{
  message_filters::Connection registerCallback(
      const std::function<void(const MsgConstPtr &)> &)
  {
    return message_filters::Connection();
  }
};

TEST(SimpleFilter, oldRegisterWithNewFilter)
{
  OldFilter f;
  Helper h;
  f.registerCallback(std::bind(&Helper::cb3, &h, std::placeholders::_1));
}
