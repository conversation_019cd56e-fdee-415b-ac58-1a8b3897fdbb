#include <functional>
#include <memory>
#include <utility>

#include <gtest/gtest.h>
//#include <rclcpp_lifecycle/lifecycle_node.hpp>

#include "message_filters/chain.h"
#include "message_filters/subscriber.h"
#include "rosa/init.h"
#include "sensor_msgs/msg/Imu.h"

typedef sensor_msgs::msg::Imu Msg;
typedef std::shared_ptr<sensor_msgs::msg::Imu const> MsgConstPtr;
typedef std::shared_ptr<sensor_msgs::msg::Imu> MsgPtr;
constexpr const char* kFakeArgv[] = {"program_name"};
constexpr auto kFakeArgc = sizeof(kFakeArgv) / sizeof(kFakeArgv[0]);

namespace subtest
{
  struct Helper
  {
   public:
    Helper() : count_(0) {}

    void cb(const MsgConstPtr& msg) { ++count_; }

    int32_t count_ = 0;
  };
}  // namespace subtest

TEST(Subscriber, simple)
{
  rosa::init(kFakeArgc, kFakeArgv);
  auto node = rosa::Node::make_shared("test_node");

  subtest::Helper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(
      std::bind(&subtest::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic", 10);
  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  auto msg = std::make_shared<Msg>();
  while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
  {
    pub->write(msg);
    rosa::Rate(50).sleep();
    // rosa::spin(node);
  }
  rosa::shutdown();
  ASSERT_GT(h.count_, 0);
}

TEST(Subscriber, simple_raw)
{
  rosa::init(kFakeArgc, kFakeArgv);
  auto node = std::make_shared<rosa::Node>("test_node");
  subtest::Helper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(
      std::bind(&subtest::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic", 10);
  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
  {
    pub->write(std::make_shared<Msg>());
    rosa::Rate(50).sleep();
    // rosa::spin(node);
  }

  ASSERT_GT(h.count_, 0);
}

TEST(Subscriber, subUnsubSub)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  subtest::Helper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(
      std::bind(&subtest::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic", 10);

  sub.unsubscribe();
  sub.subscribe();

  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
  {
    pub->write(std::make_shared<Msg>());
    rosa::Rate(50).sleep();
    // rosa::spin(node);
  }

  ASSERT_GT(h.count_, 0);
}

TEST(Subscriber, subUnsubSub_raw)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  subtest::Helper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(
      std::bind(&subtest::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic", 10);

  sub.unsubscribe();
  sub.subscribe();

  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
  {
    pub->write(std::make_shared<Msg>());
    rosa::Rate(50).sleep();
    // rosa::spin(node);
  }

  ASSERT_GT(h.count_, 0);
}

TEST(Subscriber, switchRawAndShared)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  subtest::Helper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(
      std::bind(&subtest::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic2", 10);

  sub.unsubscribe();
  sub.subscribe(node, "test_topic2");

  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
  {
    pub->write(std::make_shared<Msg>());
    rosa::Rate(50).sleep();
    // rosa::spin(node);
  }

  ASSERT_GT(h.count_, 0);
}

TEST(Subscriber, subInChain)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  subtest::Helper h;
  message_filters::Chain<Msg> c;
  c.addFilter(
      std::make_shared<message_filters::Subscriber<Msg>>(node, "test_topic"));
  c.registerCallback(
      std::bind(&subtest::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic", 10);

  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
  {
    pub->write(std::make_shared<Msg>());
    rosa::Rate(50).sleep();
    // rosa::spin(node);
  }

  ASSERT_GT(h.count_, 0);
}

struct ConstHelper
{
  void cb(const MsgConstPtr msg) { msg_ = msg; }

  MsgConstPtr msg_;
};

struct NonConstHelper
{
  void cb(const MsgPtr msg) { msg_ = msg; }

  MsgPtr msg_;
};

TEST(Subscriber, singleNonConstCallback)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  NonConstHelper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(&NonConstHelper::cb, &h);
  auto pub = node->createWriter<Msg>("test_topic", 10);
  Msg msg;
  pub->write(std::make_shared<Msg>());

  rosa::Rate(50).sleep();
  // rosa::spin(node);

  ASSERT_TRUE(h.msg_);
  ASSERT_EQ(msg, *h.msg_.get());
}

TEST(Subscriber, multipleNonConstCallbacksFilterSubscriber)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  NonConstHelper h, h2;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(&NonConstHelper::cb, &h);
  sub.registerCallback(&NonConstHelper::cb, &h2);
  auto pub = node->createWriter<Msg>("test_topic", 10);
  auto msg = std::make_unique<Msg>();
  pub->write(std::move(msg));

  rosa::Rate(50).sleep();
  // rosa::spin(node);

  ASSERT_TRUE(h.msg_);
  ASSERT_TRUE(h2.msg_);
  EXPECT_NE(msg.get(), h.msg_.get());
  EXPECT_NE(msg.get(), h2.msg_.get());
  EXPECT_NE(h.msg_.get(), h2.msg_.get());
}

TEST(Subscriber, CallbacksGroup)
{
  auto node = std::make_shared<rosa::Node>("test_node");
  NonConstHelper h, h2;
  auto group =
      std::make_shared<rosa::CallbackGroup>(rosa::CallbackGroupType::Reentrant);

  message_filters::Subscriber<Msg> sub(node, "test_topic",
                                       rosa::SensorDataQoS(), group);

  sub.registerCallback(&NonConstHelper::cb, &h);

  auto function_ = std::bind(&NonConstHelper::cb, &h2, std::placeholders::_1);
  auto sub2 = node->createReader<Msg>("test_topic", 10, function_);
  auto pub = node->createWriter<Msg>("test_topic", 10);
  auto msg = std::make_unique<Msg>();
  pub->write(std::move(msg));

  std::thread([&]() {
    auto n = 0;
    while (n < 3)
    {
      rosa::spinOnce(node);
      n++;
      rosa::Rate(50).sleep();
    }
  }).detach();
  ASSERT_TRUE(h.msg_);
  ASSERT_TRUE(h2.msg_);
  EXPECT_NE(msg.get(), h.msg_.get());
  EXPECT_NE(msg.get(), h2.msg_.get());
  EXPECT_NE(h.msg_.get(), h2.msg_.get());
}

// TEST(Subscriber, lifecycle)
// {
//   auto node = std::make_shared<rclcpp_lifecycle::LifecycleNode>("test_node");
//   Helper h;
//   message_filters::Subscriber<Msg, rclcpp_lifecycle::LifecycleNode> sub(
//       node, "test_topic");
//   sub.registerCallback(std::bind(&Helper::cb, &h, std::placeholders::_1));
//   auto pub = node->createWriter<Msg>("test_topic", 10);
//   pub->on_activate();
//   rosa::Clock ros_clock;
//   auto start = ros_clock.now();
//   while (h.count_ == 0 && (ros_clock.now() - start) < rosa::Duration(1, 0))
//   {
//     pub->write(std::make_shared<Msg>());
//     rosa::Rate(50).sleep();
//     rosa::spin(node->get_node_base_interface());
//   }

//   ASSERT_GT(h.count_, 0);
// }
