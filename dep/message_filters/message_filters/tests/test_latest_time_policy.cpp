#include <chrono>
#include <cstddef>
#include <functional>
#include <memory>
#include <thread>
#include <vector>

#include <gtest/gtest.h>

#include "rosgraph_msgs/msg/Clock.h"
#include "test_helpers.h"

using namespace std::chrono;

namespace policy
{
  class Helper
  {
   public:
    Helper() {}

    void cb(const MsgConstPtr& p, const MsgConstPtr& q, const MsgConstPtr& r)
    {
      EXPECT_TRUE(p);
      EXPECT_TRUE(q);
      EXPECT_TRUE(r);

      p_ = p;
      q_ = q;
      r_ = r;
      ++count_;
    }

    MsgConstPtr p_{nullptr}, q_{nullptr}, r_{nullptr};
    uint16_t count_{0U};
  };

}  // namespace policy

typedef message_filters::sync_policies::LatestTime<Msg, Msg, Msg>
    LatestTimePolicy3;
typedef message_filters::Synchronizer<LatestTimePolicy3> LatestTimeSync3;
constexpr const char* kFakeArgv[] = {"program_name"};
constexpr auto kFakeArgc = sizeof(kFakeArgv) / sizeof(kFakeArgv[0]);

class LatestTimePolicy : public ::testing::Test
{
 protected:
  rosa::Node::SharedPtr node;
  // rosa::SyncParametersClient::SharedPtr param_client;
  LatestTimeSync3 sync;
  policy::Helper h;
  std::vector<MsgPtr> p;
  std::vector<MsgPtr> q;
  std::vector<MsgPtr> r;

  virtual void SetUp()
  {
    // Shutdown in case there was a dangling global context from other test
    // fixtures
    rosa::init(kFakeArgc, kFakeArgv);
    node = std::make_shared<rosa::Node>("clock_sleep_node");
    // param_client = std::make_shared<rosa::SyncParametersClient>(node);
    // ASSERT_TRUE(param_client->wait_for_service(5s));

    sync.registerCallback(
        std::bind(&policy::Helper::cb, &h, std::placeholders::_1,
                  std::placeholders::_2, std::placeholders::_3));
    p.reserve(12U);
    q.reserve(6U);
    r.reserve(3U);
    for (std::size_t idx = 0U; idx < 12U; ++idx)
    {
      MsgPtr p_idx(std::make_shared<Msg>());
      p_idx->data = idx;
      p.push_back(p_idx);
      if (idx % 2U == 0U)
      {
        MsgPtr q_idx(std::make_shared<Msg>());
        q_idx->data = idx;
        q.push_back(q_idx);
      }
      if (idx % 4U == 0U)
      {
        MsgPtr r_idx(std::make_shared<Msg>());
        r_idx->data = idx;
        r.push_back(r_idx);
      }
    }
  }

  void TearDown()
  {
    node.reset();
    rosa::shutdown();
  }
};

TEST_F(LatestTimePolicy, Leading)
{
  rosa::Rate rate(50.0);
  for (std::size_t idx = 0U; idx < 8U; ++idx)
  {
    if (idx % 2U == 0U)
    {
      sync.add<1>(q[idx / 2U]);
    }
    if (idx % 4U == 0U)
    {
      sync.add<2>(r[idx / 4U]);
    }
    sync.add<0>(p[idx]);

    EXPECT_EQ(h.count_, idx);
    if (idx > 0)
    {
      EXPECT_EQ(h.p_->data, p[idx]->data);
      EXPECT_EQ(h.q_->data, q[idx / 2U]->data);
      EXPECT_EQ(h.r_->data, r[idx / 4U]->data);
    }
    else
    {
      EXPECT_FALSE(h.p_);
      EXPECT_FALSE(h.q_);
      EXPECT_FALSE(h.r_);
    }

    rate.sleep();
  }
}

TEST_F(LatestTimePolicy, Trailing)
{
  rosa::Rate rate(50.0);
  for (std::size_t idx = 0U; idx < 8U; ++idx)
  {
    if (idx % 2U == 1U)
    {
      sync.add<1>(q[(idx - 1U) / 2U]);
    }
    if (idx % 4U == 3U)
    {
      sync.add<2>(r[(idx - 3U) / 4U]);
    }
    sync.add<0>(p[idx]);

    if (idx > 2U)
    {
      EXPECT_EQ(h.count_, idx - 2U);
      EXPECT_EQ(h.p_->data, p[idx]->data);
      EXPECT_EQ(h.q_->data, q[(idx - 1U) / 2U]->data);
      EXPECT_EQ(h.r_->data, r[(idx - 3U) / 4U]->data);
    }
    else
    {
      EXPECT_FALSE(h.p_);
      EXPECT_FALSE(h.q_);
      EXPECT_FALSE(h.r_);
    }

    rate.sleep();
  }
}

// TEST_F(LatestTimePolicy, ChangeRateLeading)
// {
//   // param_client->set_parameters({rosa::Parameter("use_sim_time", true)});
//   // RCL_ROS_TIME
//   auto clock = std::make_shared<rosa::Clock>();
//   // rosa::ClockQoS()
//   auto clock_publisher =
//       node->createWriter<rosgraph_msgs::msg::Clock>("/clock", rosa::QoS());
//   // rosa::TimeSource time_source;
//   // time_source.attachNode(node);
//   // time_source.attachClock(clock);
//   sync.setClock(clock);
//   // rosa::executors::SingleThreadedExecutor executor;
//   // executor.add_node(node);

//   for (std::size_t idx = 0U; idx < 12U; ++idx)
//   {
//     if (idx % 2U == 0U)
//     {
//       sync.add<1>(q[idx / 2U]);
//     }

//     if (idx % 4U == 0U)
//     {
//       sync.add<2>(r[idx / 4U]);
//     }

//     if (idx < 4U)
//     {
//       sync.add<0>(p[idx]);
//     }
//     else
//     {  // Change rate of p
//       if (idx % 3U == 0U)
//       {
//         static std::size_t p_idx = 3U;
//         sync.add<0>(p[++p_idx]);
//       }
//     }

//     // operates like "Leading" test for idx <= 3
//     if (idx >= 1U && idx < 4U)
//     {
//       EXPECT_EQ(h.count_, idx);
//       EXPECT_EQ(h.p_->data, p[idx]->data);
//       EXPECT_EQ(h.q_->data, q[idx / 2U]->data);
//       EXPECT_EQ(h.r_->data, r[idx / 4U]->data);
//     }
//     // p rate is changed but isn't detected as late until idx==6,
//     // since q is 500Hz and p isn't late yet when q checks at idx==4.
//     // Will not publish again until idx==6 when q is found as new pivot.
//     // Same behavior as initialization dropping faster messages until rates
//     of
//     // all are known or found to be late.
//     else if (idx >= 4U && idx < 6U)
//     {
//       EXPECT_EQ(h.count_, (idx + 2U) / 2U);
//       EXPECT_EQ(h.p_->data, p[3]->data);
//       EXPECT_EQ(h.q_->data, q[1]->data);
//       EXPECT_EQ(h.r_->data, r[0]->data);
//     }
//     // New actual rate of p computed when is received after q when idx==6
//     // for idx >= 6, follows normal "Leading" pattern again
//     // with pivot on q
//     else if (idx >= 6U)
//     {
//       EXPECT_EQ(h.count_, (idx + 2U) / 2U);
//       EXPECT_EQ(h.p_->data, p[idx / 2U]->data);
//       EXPECT_EQ(h.q_->data, q[idx / 2U]->data);
//       EXPECT_EQ(h.r_->data, r[(idx - 2U) / 4U]->data);
//     }
//     else
//     {
//       EXPECT_FALSE(h.p_);
//       EXPECT_FALSE(h.q_);
//       EXPECT_FALSE(h.r_);
//     }

//     double period = 20e6;
//     auto new_time =
//         clock->now() + rosa::Duration(0, static_cast<uint32_t>(period));
//     auto msg = std::make_shared<rosgraph_msgs::msg::Clock>();
//     msg->clock = rosa::Time(new_time);
//     clock_publisher->write(msg);
//     // while (rosa::ok() && clock->now() < new_time)
//     // {
//     //   executor.spin_once(10ms);
//     // }
//   }
// }

// TEST_F(LatestTimePolicy, ChangeRateTrailing)
// {
//   // param_client->set_parameters({rosa::Parameter("use_sim_time", true)});
//   // RCL_ROS_TIME
//   auto clock = std::make_shared<rosa::Clock>();
//   // rosa::ClockQoS()
//   auto clock_publisher =
//       node->createWriter<rosgraph_msgs::msg::Clock>("/clock", rosa::QoS());
//   // rosa::TimeSource time_source;
//   // time_source.attachNode(node);
//   // time_source.attachClock(clock);
//   sync.setClock(clock);
//   // rosa::executors::SingleThreadedExecutor executor;
//   // executor.add_node(node);

//   for (std::size_t idx = 0U; idx < 12U; ++idx)
//   {
//     if (idx % 2U == 1U)
//     {
//       sync.add<1>(q[(idx - 1U) / 2U]);
//     }

//     if (idx % 4U == 3U)
//     {
//       sync.add<2>(r[(idx - 3U) / 4U]);
//     }

//     if (idx < 4U)
//     {
//       sync.add<0>(p[idx]);
//     }
//     else
//     {  // Change rate of p (still 1kHz @ idx == 4)
//       if (idx % 3U == 1U)
//       {
//         static std::size_t p_idx = 3U;
//         sync.add<0>(p[++p_idx]);
//       }
//     }

//     // operates like "Trailing" test for idx <= 3
//     if (idx > 2U && idx <= 4U)
//     {
//       EXPECT_EQ(h.count_, idx - 2U);
//       EXPECT_EQ(h.p_->data, p[idx]->data);
//       EXPECT_EQ(h.q_->data, q[(idx - 1U) / 2U]->data);
//       EXPECT_EQ(h.r_->data, r[(idx - 3U) / 4U]->data);
//     }
//     // Rate of p still 1kHz @ idx==4.
//     // Then, change rate of p lower than q when idx==5.
//     // At idx==5, policy still doesn't know that p is late when q is
//     received.
//     // Same behavior as initialization dropping faster messages until rates
//     of
//     // all are known or found to be late.
//     else if (idx > 4U && idx < 7U)
//     {
//       EXPECT_EQ(h.count_, 2U);
//       EXPECT_EQ(h.p_->data, p[4U]->data);
//       EXPECT_EQ(h.q_->data, q[1U]->data);
//       EXPECT_EQ(h.r_->data, r[0U]->data);
//     }
//     // Will not publish again until idx==7, since rate of q is 500Hz
//     // and p is calculated as late when q received when idx==7 -- this
//     makes
//     q
//     // new pivot. Since q is new pivot and publishes when idx==7, and r
//     comes in
//     // after q, r is now trailing. New actual rate of p computed when is
//     // received after q when idx==7 for idx >= 7, follows normal "Trailing"
//     // pattern again with pivot on q
//     else if (idx >= 7U)
//     {
//       EXPECT_EQ(h.count_, (idx - 1U) / 2U);
//       EXPECT_EQ(h.p_->data, p[(idx + 1U) / 2U]->data);
//       EXPECT_EQ(h.q_->data, q[(idx - 1U) / 2U]->data);
//       EXPECT_EQ(h.r_->data, r[(idx - 5U) / 4U]->data);
//     }
//     else
//     {
//       EXPECT_FALSE(h.p_);
//       EXPECT_FALSE(h.q_);
//       EXPECT_FALSE(h.r_);
//     }

//     double period = 20e6;
//     auto new_time =
//         clock->now() + rosa::Duration(0, static_cast<uint32_t>(period));
//     auto msg = std::make_shared<rosgraph_msgs::msg::Clock>();
//     msg->clock = rosa::Time(new_time);
//     clock_publisher->write(msg);
//     // while (rosa::ok() && clock->now() < new_time)
//     // {
//     //   executor.spin_once(10ms);
//     // }
//   }
// }
