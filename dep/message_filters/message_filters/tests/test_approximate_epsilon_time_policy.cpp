#include <cstdint>
#include <functional>
#include <memory>
#include <utility>
#include <vector>

#include <gtest/gtest.h>

#include "test_helpers.h"

class ApproximateEpsilonTimeSynchronizerTest
{
 public:
  ApproximateEpsilonTimeSynchronizerTest(const std::vector<TimeAndTopic>& input,
                                         const std::vector<TimePair>& output,
                                         uint32_t queue_size,
                                         rosa::Duration epsilon)
      : input_(input),
        output_(output),
        output_position_(0),
        sync_(message_filters::sync_policies::ApproximateEpsilonTime<Msg, Msg>{
            queue_size, epsilon})
  {
    sync_.registerCallback(
        std::bind(&ApproximateEpsilonTimeSynchronizerTest::callback, this,
                  std::placeholders::_1, std::placeholders::_2));
  }

  void callback(const MsgConstPtr& p, const MsgConstPtr& q)
  {
    ASSERT_TRUE(p);
    ASSERT_TRUE(q);
    ASSERT_LT(output_position_, output_.size());
    EXPECT_EQ(output_[output_position_].first, p->header.stamp);
    EXPECT_EQ(output_[output_position_].second, q->header.stamp);
    ++output_position_;
  }

  void run()
  {
    for (const auto& time_topic : input_)
    {
      const rosa::Time& time = time_topic.first;
      const unsigned int& topic = time_topic.second;
      if (topic == 0)
      {
        MsgPtr p(std::make_shared<Msg>());
        p->header.stamp = time;
        sync_.add<0>(p);
      }
      else
      {
        MsgPtr q(std::make_shared<Msg>());
        q->header.stamp = time;
        sync_.add<1>(q);
      }
    }
    EXPECT_EQ(output_.size(), output_position_);
  }

 private:
  const std::vector<TimeAndTopic>& input_;
  const std::vector<TimePair>& output_;
  unsigned int output_position_;
  typedef message_filters::Synchronizer<
      message_filters::sync_policies::ApproximateEpsilonTime<Msg, Msg>>
      Sync2;

 public:
  Sync2 sync_;
};

TEST(ApproxEpsilonTimeSync, ExactMatch)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t, 1},
                            {t + s * 3, 0},
                            {t + s * 3, 1},
                            {t + s * 6, 0},
                            {t + s * 6, 1}});

  auto output =
      createOutput({{t, t}, {t + s * 3, t + s * 3}, {t + s * 6, t + s * 6}});

  ApproximateEpsilonTimeSynchronizerTest sync_test(
      input, output, 10, rosa::Duration::fromSeconds(0.5));
  sync_test.run();
}

TEST(ApproxEpsilonTimeSync, PerfectMatch)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s, 1},
                            {t + s * 3, 0},
                            {t + s * 4, 1},
                            {t + s * 6, 0},
                            {t + s * 7, 1}});

  auto output = createOutput(
      {{t, t + s}, {t + s * 3, t + s * 4}, {t + s * 6, t + s * 7}});

  ApproximateEpsilonTimeSynchronizerTest sync_test(
      input, output, 10, rosa::Duration::fromSeconds(1.5));
  sync_test.run();
}

TEST(ApproxEpsilonTimeSync, ImperfectMatch)
{
  rosa::Time t(0, 0);
  rosa::Duration s(1, 0);

  auto input = createInput({{t, 0},
                            {t + s, 1},
                            {t + s * 2, 0},
                            {t + s * 3, 0},
                            {t + s * 4, 1},
                            {t + s * 6, 0},
                            {t + s * 7, 1}});

  auto output = createOutput(
      {{t, t + s}, {t + s * 3, t + s * 4}, {t + s * 6, t + s * 7}});

  ApproximateEpsilonTimeSynchronizerTest sync_test(
      input, output, 10, rosa::Duration::fromSeconds(1.5));
  sync_test.run();
}
