
#include <memory>

#include <gtest/gtest.h>

#include "test_helpers.h"

TEST(TimeSequencer, simple)
{
  rosa::Node::SharedPtr node = std::make_shared<rosa::Node>("test_node");
  message_filters::TimeSequencer<Msg> seq(
      rosa::Duration(0, 250000000), rosa::Duration(0, 10000000), 10, node);
  Helper h;
  seq.registerCallback(std::bind(&Helper::cb, &h, std::placeholders::_1));
  MsgPtr msg(std::make_shared<Msg>());
  msg->header.stamp = rosa::Clock().now();
  seq.add(msg);

  rosa::Rate(10).sleep();
  // rosa::spin_some(node);
  ASSERT_EQ(h.count_, 0);

  // Must be longer than the first duration above
  rosa::Rate(3).sleep();
  // rosa::spin_some(node);

  ASSERT_EQ(h.count_, 1);
}

TEST(TimeSequencer, compilation)
{
  rosa::Node::SharedPtr node = std::make_shared<rosa::Node>("test_node");
  message_filters::TimeSequencer<Msg> seq(
      rosa::Duration(1, 0), rosa::Duration(0, 10000000), 10, node);
  message_filters::TimeSequencer<Msg> seq2(
      rosa::Duration(1, 0), rosa::Duration(0, 10000000), 10, node);
  seq2.connectInput(seq);
}

TEST(TimeSequencer, eventInEventOut)
{
  rosa::Node::SharedPtr nh = std::make_shared<rosa::Node>("test_node");
  message_filters::TimeSequencer<Msg> seq(rosa::Duration(1, 0),
                                          rosa::Duration(0, 10000000), 10, nh);
  message_filters::TimeSequencer<Msg> seq2(seq, rosa::Duration(1, 0),
                                           rosa::Duration(0, 10000000), 10, nh);
  EventHelper h;
  seq2.registerCallback(&EventHelper::cb, &h);

  message_filters::MessageEvent<Msg const> evt(std::make_shared<Msg const>(),
                                               rosa::Clock().now());
  seq.add(evt);
  rosa::Rate(100).sleep();
  h.event_.getMessage();

  EXPECT_EQ(h.event_.getReceiptTime(), evt.getReceiptTime());
  EXPECT_EQ(h.event_.getMessage(), evt.getMessage());
}
