#include <functional>
#include <memory>
#include <vector>

#include <gtest/gtest.h>

#include "test_helpers.h"

void fillCacheEasy(message_filters::Cache<Msg>& cache,
                   unsigned int start,
                   unsigned int end)
{
  for (unsigned int i = start; i < end; i++)
  {
    Msg* msg = new Msg;
    msg->data = i;
    msg->header.stamp = rosa::Time(i * 10, 0);

    std::shared_ptr<Msg const> msg_ptr(msg);
    cache.add(msg_ptr);
  }
}

TEST(Cache, easyInterval)
{
  message_filters::Cache<Msg> cache(10);
  fillCacheEasy(cache, 0, 5);

  std::vector<std::shared_ptr<Msg const>> interval_data =
      cache.getInterval(rosa::Time(5, 0), rosa::Time(35, 0));

  ASSERT_EQ(interval_data.size(), (unsigned int)3);
  EXPECT_EQ(interval_data[0]->data, 1);
  EXPECT_EQ(interval_data[1]->data, 2);
  EXPECT_EQ(interval_data[2]->data, 3);

  // Look for an interval past the end of the cache
  interval_data = cache.getInterval(rosa::Time(55, 0), rosa::Time(65, 0));
  EXPECT_EQ(interval_data.size(), (unsigned int)0);

  // Look for an interval that fell off the back of the cache
  fillCacheEasy(cache, 5, 20);
  interval_data = cache.getInterval(rosa::Time(5, 0), rosa::Time(35, 0));
  EXPECT_EQ(interval_data.size(), (unsigned int)0);
}

TEST(Cache, easySurroundingInterval)
{
  message_filters::Cache<Msg> cache(10);
  fillCacheEasy(cache, 1, 6);

  std::vector<std::shared_ptr<Msg const>> interval_data;
  interval_data =
      cache.getSurroundingInterval(rosa::Time(15, 0), rosa::Time(35, 0));
  ASSERT_EQ(interval_data.size(), (unsigned int)4);
  EXPECT_EQ(interval_data[0]->data, 1);
  EXPECT_EQ(interval_data[1]->data, 2);
  EXPECT_EQ(interval_data[2]->data, 3);
  EXPECT_EQ(interval_data[3]->data, 4);

  interval_data =
      cache.getSurroundingInterval(rosa::Time(0, 0), rosa::Time(35, 0));
  ASSERT_EQ(interval_data.size(), (unsigned int)4);
  EXPECT_EQ(interval_data[0]->data, 1);

  interval_data =
      cache.getSurroundingInterval(rosa::Time(35, 0), rosa::Time(35, 0));
  ASSERT_EQ(interval_data.size(), (unsigned int)2);
  EXPECT_EQ(interval_data[0]->data, 3);
  EXPECT_EQ(interval_data[1]->data, 4);

  interval_data =
      cache.getSurroundingInterval(rosa::Time(55, 0), rosa::Time(55, 0));
  ASSERT_EQ(interval_data.size(), (unsigned int)1);
  EXPECT_EQ(interval_data[0]->data, 5);
}

std::shared_ptr<Msg const> buildMsg(int32_t seconds, int data)
{
  Msg* msg = new Msg;
  msg->data = data;
  msg->header.stamp = rosa::Time(seconds, 0);

  std::shared_ptr<Msg const> msg_ptr(msg);
  return msg_ptr;
}

TEST(Cache, easyUnsorted)
{
  message_filters::Cache<Msg> cache(10);

  cache.add(buildMsg(10, 1));
  cache.add(buildMsg(30, 3));
  cache.add(buildMsg(70, 7));
  cache.add(buildMsg(5, 0));
  cache.add(buildMsg(20, 2));

  std::vector<std::shared_ptr<Msg const>> interval_data =
      cache.getInterval(rosa::Time(3, 0), rosa::Time(15, 0));

  ASSERT_EQ(interval_data.size(), (unsigned int)2);
  EXPECT_EQ(interval_data[0]->data, 0);
  EXPECT_EQ(interval_data[1]->data, 1);

  // Grab all the data
  interval_data = cache.getInterval(rosa::Time(0, 0), rosa::Time(80, 0));
  ASSERT_EQ(interval_data.size(), (unsigned int)5);
  EXPECT_EQ(interval_data[0]->data, 0);
  EXPECT_EQ(interval_data[1]->data, 1);
  EXPECT_EQ(interval_data[2]->data, 2);
  EXPECT_EQ(interval_data[3]->data, 3);
  EXPECT_EQ(interval_data[4]->data, 7);
}

TEST(Cache, easyElemBeforeAfter)
{
  message_filters::Cache<Msg> cache(10);
  std::shared_ptr<Msg const> elem_ptr;

  fillCacheEasy(cache, 5, 10);

  elem_ptr = cache.getElemAfterTime(rosa::Time(85, 0));

  ASSERT_FALSE(!elem_ptr);
  EXPECT_EQ(elem_ptr->data, 9);

  elem_ptr = cache.getElemBeforeTime(rosa::Time(85, 0));
  ASSERT_FALSE(!elem_ptr);
  EXPECT_EQ(elem_ptr->data, 8);

  elem_ptr = cache.getElemBeforeTime(rosa::Time(45, 0));
  EXPECT_TRUE(!elem_ptr);
}

TEST(Cache, getElementLatestAndOldest)
{
  message_filters::Cache<Msg> cache(10);
  std::shared_ptr<Msg const> elem_ptr;

  fillCacheEasy(cache, 3, 6);

  elem_ptr = cache.getLatestElem();
  ASSERT_FALSE(!elem_ptr);
  EXPECT_EQ(elem_ptr->data, 5);

  elem_ptr = cache.getOldestElem();
  ASSERT_FALSE(!elem_ptr);
  EXPECT_EQ(elem_ptr->data, 3);

  auto size = cache.size();
  EXPECT_EQ(size, 3);
}

TEST(Cache, eventInEventOut)
{
  message_filters::Cache<Msg> c0(10);
  message_filters::Cache<Msg> c1(c0, 10);
  EventHelper h;
  c1.registerCallback(&EventHelper::cb, &h);

  message_filters::MessageEvent<Msg const> evt(std::make_shared<Msg const>(),
                                               rosa::Time(4, 0));
  c0.add(evt);

  EXPECT_EQ(h.event_.getReceiptTime(), evt.getReceiptTime());
  EXPECT_EQ(h.event_.getMessage(), evt.getMessage());
}
