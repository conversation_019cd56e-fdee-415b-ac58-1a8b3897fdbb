#include <gtest/gtest.h>

#include "message_filters/message_traits.h"

struct Msg
{
  std_msgs::msg::Header header;
};

TEST(MessageTraits, timeSource)
{
  Msg msg;
  msg.header.stamp = rosa::Time::now();
  rosa::Time time = message_filters::message_traits::TimeStamp<Msg>::value(msg);

  EXPECT_EQ(time.getClockType(), rosa::ClockType::SystemTime);

  bool unused;
  EXPECT_NO_THROW(unused = (time == rosa::Time{msg.header.stamp}));
  (void)unused;
}
