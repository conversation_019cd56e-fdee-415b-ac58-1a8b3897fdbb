#include <memory>

#include <gtest/gtest.h>

#include "test_helpers.h"

TEST(Chain, simple)
{
  Helper h;
  message_filters::Chain<Msg> c;
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.registerCallback(std::bind(&Helper::cb, &h));

  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 1);
  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 2);
}

TEST(Chain, multipleFilters)
{
  Helper h;
  message_filters::Chain<Msg> c;
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.registerCallback(std::bind(&Helper::cb, &h));

  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 1);
  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 2);
}

TEST(Chain, addingFilters)
{
  Helper h;
  message_filters::Chain<Msg> c;
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.registerCallback(std::bind(&Helper::cb, &h));

  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 1);

  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());

  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 2);
}

TEST(Chain, inputFilter)
{
  Helper h;
  message_filters::Chain<Msg> c;
  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());
  c.registerCallback(std::bind(&Helper::cb, &h));

  message_filters::PassThrough<Msg> p;
  c.connectInput(p);
  p.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 1);

  p.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 2);
}

TEST(Chain, nonSharedPtrFilter)
{
  Helper h;
  message_filters::Chain<Msg> c;
  message_filters::PassThrough<Msg> p;
  c.addFilter(&p);
  c.registerCallback(std::bind(&Helper::cb, &h));

  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 1);
  c.add(std::make_shared<Msg>());
  EXPECT_EQ(h.count_, 2);
}

TEST(Chain, retrieveFilter)
{
  message_filters::Chain<Msg> c;

  ASSERT_FALSE(c.getFilter<message_filters::PassThrough<Msg>>(0));

  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());

  ASSERT_TRUE(c.getFilter<message_filters::PassThrough<Msg>>(0));
  ASSERT_FALSE(c.getFilter<message_filters::PassThrough<Msg>>(1));
}

TEST(Chain, retrieveFilterThroughBaseClass)
{
  message_filters::Chain<Msg> c;
  message_filters::ChainBase* cb = &c;

  ASSERT_FALSE(cb->getFilter<message_filters::PassThrough<Msg>>(0));

  c.addFilter(std::make_shared<message_filters::PassThrough<Msg>>());

  ASSERT_TRUE(cb->getFilter<message_filters::PassThrough<Msg>>(0));
  ASSERT_FALSE(cb->getFilter<message_filters::PassThrough<Msg>>(1));
}

struct PTDerived : public message_filters::PassThrough<Msg>
{
};

TEST(Chain, retrieveBaseClass)
{
  message_filters::Chain<Msg> c;
  c.addFilter(std::make_shared<PTDerived>());
  ASSERT_TRUE(c.getFilter<message_filters::PassThrough<Msg>>(0));
  ASSERT_TRUE(c.getFilter<PTDerived>(0));
}
