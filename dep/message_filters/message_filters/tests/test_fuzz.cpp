#include <functional>
#include <memory>
#include <random>

#include <gtest/gtest.h>

#include "message_filters/chain.h"
#include "message_filters/subscriber.h"
#include "message_filters/time_sequencer.h"
#include "message_filters/time_synchronizer.h"
#include "rosa/init.h"
#include "sensor_msgs/msg/Imu.h"

typedef sensor_msgs::msg::Imu Msg;
typedef std::shared_ptr<sensor_msgs::msg::Imu const> MsgConstPtr;
typedef std::shared_ptr<sensor_msgs::msg::Imu> MsgPtr;
constexpr const char *kFakeArgv[] = {"program_name"};
constexpr auto kFakeArgc = sizeof(kFakeArgv) / sizeof(kFakeArgv[0]);

namespace fuzz
{
  class Helper
  {
   public:
    Helper() : count_(0) {}

    void cb(const MsgConstPtr &) { ++count_; }

    void cb2(const MsgConstPtr &, const MsgConstPtr &) { ++count_; }
    int32_t count_;
  };
}  // namespace fuzz

static void fuzz_msg(MsgPtr msg)
{
  static std::random_device seeder;
  std::mt19937 gen(seeder());
  std::uniform_real_distribution<float> distr(1.0, 3.0);
  msg->linear_acceleration.x = distr(gen);
  msg->linear_acceleration.y = distr(gen);
  msg->linear_acceleration.z = distr(gen);
}

TEST(TimeSequencer, fuzz_sequencer)
{
  rosa::Node::SharedPtr node = std::make_shared<rosa::Node>("test_node");
  message_filters::TimeSequencer<Msg> seq(rosa::Duration(0, 10000000),
                                          rosa::Duration(0, 1000000), 10, node);
  fuzz::Helper h;
  seq.registerCallback(std::bind(&fuzz::Helper::cb, &h, std::placeholders::_1));
  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  auto msg = std::make_shared<Msg>();
  // while ((ros_clock.now() - start) < rosa::Duration(5, 0))
  {
    h.count_ = 0;
    fuzz_msg(msg);
    msg->header.stamp = ros_clock.now();
    seq.add(msg);

    // rosa::Rate(20).sleep();
    // ASSERT_EQ(h.count_, 0);
    // rosa::spin_some(node);
    // rosa::Rate(100).sleep();
    // rosa::spin_some(node);
    // ASSERT_EQ(h.count_, 1);
  }
}

TEST(TimeSynchronizer, fuzz_synchronizer)
{
  message_filters::TimeSynchronizer<Msg, Msg> sync(1);
  fuzz::Helper h;
  sync.registerCallback(std::bind(&fuzz::Helper::cb2, &h, std::placeholders::_1,
                                  std::placeholders::_2));

  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  auto msg1 = std::make_shared<Msg>();
  auto msg2 = std::make_shared<Msg>();
  // while ((ros_clock.now() - start) < rosa::Duration(5, 0))
  {
    h.count_ = 0;
    fuzz_msg(msg1);
    msg1->header.stamp = rosa::Clock().now();
    fuzz_msg(msg2);
    msg2->header.stamp = msg1->header.stamp;
    sync.add0(msg1);
    // ASSERT_EQ(h.count_, 0);
    sync.add1(msg2);
    // ASSERT_EQ(h.count_, 1);
    // rosa::Rate(50).sleep();
  }
}

TEST(Subscriber, fuzz_subscriber)
{
  rosa::init(kFakeArgc, kFakeArgv);
  auto node = std::make_shared<rosa::Node>("test_node");
  fuzz::Helper h;
  message_filters::Subscriber<Msg> sub(node, "test_topic");
  sub.registerCallback(std::bind(&fuzz::Helper::cb, &h, std::placeholders::_1));
  auto pub = node->createWriter<Msg>("test_topic", 10);
  rosa::Clock ros_clock;
  auto start = ros_clock.now();
  auto msg = std::make_shared<Msg>();
  // while ((ros_clock.now() - start) < rosa::Duration(5, 0))
  {
    h.count_ = 0;
    fuzz_msg(msg);
    msg->header.stamp = ros_clock.now();
    pub->write(msg);
    rosa::Rate(50).sleep();
    // rosa::spin_some(node);
    // ASSERT_EQ(h.count_, 1);
  }
  // rosa::spin_some(node);
  rosa::shutdown();
}
