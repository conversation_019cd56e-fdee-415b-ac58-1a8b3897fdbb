#pragma once
#include "message_filters/cache.h"
#include "message_filters/chain.h"
#include "message_filters/message_traits.h"
#include "message_filters/pass_through.h"
#include "message_filters/sync_policies/approximate_epsilon_time.h"
#include "message_filters/sync_policies/approximate_time.h"
#include "message_filters/sync_policies/exact_time.h"
#include "message_filters/sync_policies/latest_time.h"
#include "message_filters/synchronizer.h"
#include "message_filters/time_sequencer.h"
#include "message_filters/time_synchronizer.h"
#include "rosa/time.h"

typedef std::pair<rosa::Time, rosa::Time> TimePair;
typedef std::pair<rosa::Time, unsigned int> TimeAndTopic;
struct TimeQuad
{
  TimeQuad(rosa::Time p, rosa::Time q, rosa::Time r, rosa::Time s)
  {
    time[0] = p;
    time[1] = q;
    time[2] = r;
    time[3] = s;
  }
  rosa::Time time[4];
};

template <typename M0,
          typename M1,
          typename M2 = message_filters::NullType,
          typename M3 = message_filters::NullType,
          typename M4 = message_filters::NullType,
          typename M5 = message_filters::NullType,
          typename M6 = message_filters::NullType,
          typename M7 = message_filters::NullType,
          typename M8 = message_filters::NullType>
struct NullPolicy
    : public message_filters::PolicyBase<M0, M1, M2, M3, M4, M5, M6, M7, M8>
{
  typedef message_filters::Synchronizer<NullPolicy> Sync;
  typedef message_filters::PolicyBase<M0, M1, M2, M3, M4, M5, M6, M7, M8> Super;
  typedef typename Super::Messages Messages;
  typedef typename Super::Signal Signal;
  typedef typename Super::Events Events;
  typedef typename Super::RealTypeCount RealTypeCount;

  NullPolicy()
  {
    for (int i = 0; i < RealTypeCount::value; ++i)
    {
      added_[i] = 0;
    }
  }

  void initParent(Sync *) {}

  template <int i>
  void add(const typename std::tuple_element<i, Events>::type &)
  {
    ++added_.at(i);
  }

  std::array<int32_t, RealTypeCount::value> added_;
};

namespace sync_test
{
  struct Header
  {
    rosa::Time stamp;
  };

  struct Msg
  {
    Header header;
    int data;
  };

  typedef std::shared_ptr<Msg> MsgPtr;
  typedef std::shared_ptr<Msg const> MsgConstPtr;
  typedef message_filters::sync_policies::ExactTime<Msg, Msg> ExactTimePolicy2;
  typedef message_filters::sync_policies::ExactTime<Msg, Msg, Msg>
      ExactTimePolicy3;
  typedef message_filters::Synchronizer<ExactTimePolicy2> Sync2;
  typedef message_filters::Synchronizer<ExactTimePolicy3> Sync3;
  typedef std::shared_ptr<message_filters::PassThrough<Msg>> PassThroughPtr;

  typedef NullPolicy<Msg, Msg> Policy2;
  typedef NullPolicy<Msg, Msg, Msg> Policy3;
  typedef NullPolicy<Msg, Msg, Msg, Msg> Policy4;
  typedef NullPolicy<Msg, Msg, Msg, Msg, Msg> Policy5;
  typedef NullPolicy<Msg, Msg, Msg, Msg, Msg, Msg> Policy6;
  typedef NullPolicy<Msg, Msg, Msg, Msg, Msg, Msg, Msg> Policy7;
  typedef NullPolicy<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg> Policy8;
  typedef NullPolicy<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg> Policy9;

  class Helper
  {
   public:
    Helper() : count_(0), drop_count_(0) {}

    void cb() { ++count_; }

    void dropcb() { ++drop_count_; }

    int32_t count_;
    int32_t drop_count_;
  };

  template <typename Policy, typename Function>
  void testSyncRegisterCallback(Function &&f)
  {
    message_filters::Synchronizer<Policy> sync;
    sync.registerCallback(std::forward<Function>(f));
  }

  template <typename... Msgs>
  void testTimeSynchronizer(int queue_size)
  {
    message_filters::TimeSynchronizer<Msgs...> sync(
        message_filters::NullFilter<Msgs>()..., queue_size);
  }

  template <typename... Msgs>
  void testTimeSynchronizerCallback(void (*callback)(const MsgConstPtr &, ...))
  {
    message_filters::TimeSynchronizer<Msgs...> sync(1);
    sync.registerCallback(callback);
  }

  template <typename... Msgs, typename Method>
  void testTimeSynchronizerMethod(
      void (Method::*method)(const MsgConstPtr &...), Method *instance)
  {
    message_filters::TimeSynchronizer<Msgs...> sync(1);
    sync.registerCallback(method, instance);
  }

  template <typename... Msgs>
  void testImmediateSync(int add_index)
  {
    message_filters::TimeSynchronizer<Msgs...> sync(1);
    sync_test::Helper h;
    sync.registerCallback(std::bind(&sync_test::Helper::cb, &h));

    MsgPtr m(std::make_shared<Msg>());
    m->header.stamp = rosa::Clock().now();

    for (int i = 0; i < sizeof...(Msgs); ++i)
    {
      sync.add(i == add_index ? m : nullptr);
      ASSERT_EQ(h.count_, i == add_index ? 1 : 0);
    }
  }

  inline void function2(const MsgConstPtr &, const MsgConstPtr &) {}
  inline void function3(const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &)
  {
  }
  inline void function4(const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &)
  {
  }
  inline void function5(const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &)
  {
  }
  inline void function6(const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &)
  {
  }
  inline void function7(const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &)
  {
  }
  inline void function8(const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &,
                        const MsgConstPtr &)
  {
  }
  inline void function9(const MsgConstPtr &,
                        MsgConstPtr,
                        const MsgPtr &,
                        MsgPtr,
                        const Msg &,
                        Msg,
                        const message_filters::MessageEvent<Msg const> &,
                        const message_filters::MessageEvent<Msg> &,
                        const MsgConstPtr &)
  {
  }

  struct MethodHelper
  {
    void method2(const MsgConstPtr &, const MsgConstPtr &) {}
    void method3(const MsgConstPtr &, const MsgConstPtr &, const MsgConstPtr &)
    {
    }
    void method4(const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &)
    {
    }
    void method5(const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &)
    {
    }
    void method6(const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &)
    {
    }
    void method7(const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &,
                 const MsgConstPtr &)
    {
    }
    void method8(const MsgConstPtr &,
                 MsgConstPtr,
                 const MsgPtr &,
                 MsgPtr,
                 const Msg &,
                 Msg,
                 const message_filters::MessageEvent<Msg const> &,
                 const message_filters::MessageEvent<Msg> &)
    {
    }
    // Can only do 8 here because the object instance counts as a parameter
    // and bind only supports 9
  };

  struct EventHelper
  {
    void callback(const message_filters::MessageEvent<Msg const> &e1,
                  const message_filters::MessageEvent<Msg const> &e2)
    {
      e1_ = e1;
      e2_ = e2;
    }

    void cb(const message_filters::MessageEvent<Msg const> &evt)
    {
      event_ = evt;
    }

    message_filters::MessageEvent<Msg const> event_;

    message_filters::MessageEvent<Msg const> e1_;
    message_filters::MessageEvent<Msg const> e2_;
  };

}  // namespace sync_test

namespace message_filters
{
  namespace message_traits
  {
    template <>
    struct TimeStamp<sync_test::Msg>
    {
      static rosa::Time value(const sync_test::Msg &m)
      {
        return m.header.stamp;
      }
    };
  }  // namespace message_traits
}  // namespace message_filters

inline std::vector<TimeAndTopic> createInput(
    const std::vector<std::pair<rosa::Time, int>> &time_topic_pairs)
{
  std::vector<TimeAndTopic> input;
  for (const auto &pair : time_topic_pairs)
  {
    input.push_back(TimeAndTopic(pair.first, pair.second));
  }
  return input;
}

inline std::vector<TimePair> createOutput(
    const std::vector<std::pair<rosa::Time, rosa::Time>> &time_pairs)
{
  std::vector<TimePair> output;
  for (const auto &pair : time_pairs)
  {
    output.push_back(TimePair(pair.first, pair.second));
  }
  return output;
}

inline std::vector<TimeQuad> createOutputQuad(
    const std::vector<
        std::tuple<rosa::Time, rosa::Time, rosa::Time, rosa::Time>>
        &time_quad_tuples)
{
  std::vector<TimeQuad> output;
  for (const auto &tuple : time_quad_tuples)
  {
    output.push_back(TimeQuad(std::get<0>(tuple), std::get<1>(tuple),
                              std::get<2>(tuple), std::get<3>(tuple)));
  }
  return output;
}

using namespace sync_test;
