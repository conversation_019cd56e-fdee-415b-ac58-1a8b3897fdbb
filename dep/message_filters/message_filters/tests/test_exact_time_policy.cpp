#include <functional>
#include <memory>

#include <gtest/gtest.h>

#include "test_helpers.h"

//////////////////////////////////////////////////////////////////////////////////////////////////
// From here on we assume that testing the 3-message version is sufficient, so
// as not to duplicate tests for everywhere from 2-9
//////////////////////////////////////////////////////////////////////////////////////////////////
TEST(ExactTime, multipleTimes)
{
  Sync3 sync(2);
  Helper h;
  sync.registerCallback(std::bind(&Helper::cb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Time();

  sync.add<0>(m);
  ASSERT_EQ(h.count_, 0);

  m = std::make_shared<Msg>();
  m->header.stamp = rosa::Time(100000000);
  sync.add<1>(m);
  ASSERT_EQ(h.count_, 0);
  sync.add<0>(m);
  ASSERT_EQ(h.count_, 0);
  sync.add<2>(m);
  ASSERT_EQ(h.count_, 1);
}

TEST(ExactTime, queueSize)
{
  Sync3 sync(1);
  Helper h;
  sync.registerCallback(std::bind(&Helper::cb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Time();

  sync.add<0>(m);
  ASSERT_EQ(h.count_, 0);
  sync.add<1>(m);
  ASSERT_EQ(h.count_, 0);

  m = std::make_shared<Msg>();
  m->header.stamp = rosa::Time(100000000);
  sync.add<1>(m);
  ASSERT_EQ(h.count_, 0);

  m = std::make_shared<Msg>();
  m->header.stamp = rosa::Time();
  sync.add<1>(m);
  ASSERT_EQ(h.count_, 0);
  sync.add<2>(m);
  ASSERT_EQ(h.count_, 0);
}

TEST(ExactTime, dropCallback)
{
  Sync2 sync(1);
  Helper h;
  sync.registerCallback(std::bind(&Helper::cb, &h));
  sync.getPolicy()->registerDropCallback(std::bind(&Helper::dropcb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Time();

  sync.add<0>(m);
  ASSERT_EQ(h.drop_count_, 0);
  m->header.stamp = rosa::Time(100000000);
  sync.add<0>(m);

  ASSERT_EQ(h.drop_count_, 1);
}

TEST(ExactTime, eventInEventOut)
{
  Sync2 sync(2);
  EventHelper h;
  sync.registerCallback(&EventHelper::callback, &h);
  message_filters::MessageEvent<Msg const> evt(std::make_shared<Msg>(),
                                               rosa::Time(4, 0));

  sync.add<0>(evt);
  sync.add<1>(evt);

  ASSERT_TRUE(h.e1_.getMessage());
  ASSERT_TRUE(h.e2_.getMessage());
  ASSERT_EQ(h.e1_.getReceiptTime(), evt.getReceiptTime());
  ASSERT_EQ(h.e2_.getReceiptTime(), evt.getReceiptTime());
}
