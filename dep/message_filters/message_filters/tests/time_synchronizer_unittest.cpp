#include <memory>

#include <gtest/gtest.h>

#include "test_helpers.h"

TEST(TimeSynchronizer, compile2)
{
  sync_test::testTimeSynchronizer<Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile3)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile4)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile5)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg, Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile6)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg, Msg, Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile7)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg, Msg, Msg, Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile8)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg>(1);
}

TEST(TimeSynchronizer, compile9)
{
  sync_test::testTimeSynchronizer<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg>(
      1);
}

TEST(TimeSynchronizer, compileFunction2)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg>(function2);
}

TEST(TimeSynchronizer, compileFunction3)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg>(function3);
}

TEST(TimeSynchronizer, compileFunction4)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg, Msg>(function4);
}

TEST(TimeSynchronizer, compileFunction5)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg, Msg, Msg>(function5);
}

TEST(TimeSynchronizer, compileFunction6)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg, Msg, Msg, Msg>(
      function6);
}

TEST(TimeSynchronizer, compileFunction7)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg, Msg, Msg, Msg, Msg>(
      function7);
}

TEST(TimeSynchronizer, compileFunction8)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg, Msg, Msg, Msg, Msg,
                                          Msg>(function8);
}

TEST(TimeSynchronizer, compileFunction9)
{
  sync_test::testTimeSynchronizerCallback<Msg, Msg, Msg, Msg, Msg, Msg, Msg,
                                          Msg, Msg>(sync_test::function9);
}

TEST(TimeSynchronizer, compileMethod2)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg>(&MethodHelper::method2, &h);
}

TEST(TimeSynchronizer, compileMethod3)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg, Msg>(&MethodHelper::method3,
                                                       &h);
}

TEST(TimeSynchronizer, compileMethod4)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg, Msg, Msg>(
      &MethodHelper::method4, &h);
}

TEST(TimeSynchronizer, compileMethod5)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg, Msg, Msg, Msg>(
      &MethodHelper::method5, &h);
}

TEST(TimeSynchronizer, compileMethod6)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg, Msg, Msg, Msg, Msg>(
      &MethodHelper::method6, &h);
}

TEST(TimeSynchronizer, compileMethod7)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg, Msg, Msg, Msg, Msg, Msg>(
      &MethodHelper::method7, &h);
}

TEST(TimeSynchronizer, compileMethod8)
{
  MethodHelper h;
  sync_test::testTimeSynchronizerMethod<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg>(
      &MethodHelper::method8, &h);
}

TEST(TimeSynchronizer, immediate2)
{
  sync_test::testImmediateSync<Msg, Msg>(1);
}

TEST(TimeSynchronizer, immediate3)
{
  sync_test::testImmediateSync<Msg, Msg, Msg>(2);
}

TEST(TimeSynchronizer, immediate4)
{
  sync_test::testImmediateSync<Msg, Msg, Msg, Msg>(3);
}

TEST(TimeSynchronizer, immediate5)
{
  sync_test::testImmediateSync<Msg, Msg, Msg, Msg, Msg>(4);
}

TEST(TimeSynchronizer, immediate6)
{
  sync_test::testImmediateSync<Msg, Msg, Msg, Msg, Msg, Msg>(5);
}

TEST(TimeSynchronizer, immediate7)
{
  sync_test::testImmediateSync<Msg, Msg, Msg, Msg, Msg, Msg, Msg>(6);
}

TEST(TimeSynchronizer, immediate8)
{
  sync_test::testImmediateSync<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg>(7);
}

TEST(TimeSynchronizer, immediate9)
{
  sync_test::testImmediateSync<Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg, Msg>(8);
}

//////////////////////////////////////////////////////////////////////////////////////////////////
// From here on we assume that testing the 3-message version is sufficient, so
// as not to duplicate tests for everywhere from 2-9
//////////////////////////////////////////////////////////////////////////////////////////////////
TEST(TimeSynchronizer, multipleTimes)
{
  message_filters::TimeSynchronizer<Msg, Msg, Msg> sync(2);
  sync_test::Helper h;
  sync.registerCallback(std::bind(&sync_test::Helper::cb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Time();

  sync.add0(m);
  ASSERT_EQ(h.count_, 0);

  m = std::make_shared<Msg>();
  m->header.stamp = rosa::Time(100000000);
  sync.add1(m);
  ASSERT_EQ(h.count_, 0);
  sync.add0(m);
  ASSERT_EQ(h.count_, 0);
  sync.add2(m);
  ASSERT_EQ(h.count_, 1);
}

TEST(TimeSynchronizer, queueSize)
{
  message_filters::TimeSynchronizer<Msg, Msg, Msg> sync(1);
  sync_test::Helper h;
  sync.registerCallback(std::bind(&sync_test::Helper::cb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Time();

  sync.add0(m);
  ASSERT_EQ(h.count_, 0);
  sync.add1(m);
  ASSERT_EQ(h.count_, 0);

  m = std::make_shared<Msg>();
  m->header.stamp = rosa::Time(100000000);
  sync.add1(m);
  ASSERT_EQ(h.count_, 0);

  m = std::make_shared<Msg>();
  m->header.stamp = rosa::Time();
  sync.add1(m);
  ASSERT_EQ(h.count_, 0);
  sync.add2(m);
  ASSERT_EQ(h.count_, 0);
}

TEST(TimeSynchronizer, dropCallback)
{
  message_filters::TimeSynchronizer<Msg, Msg> sync(1);
  sync_test::Helper h;
  sync.registerCallback(std::bind(&sync_test::Helper::cb, &h));
  sync.registerDropCallback(std::bind(&sync_test::Helper::dropcb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Time(0, 0);

  sync.add0(m);
  ASSERT_EQ(h.drop_count_, 0);
  m->header.stamp = rosa::Time(100000000);
  sync.add0(m);

  ASSERT_EQ(h.drop_count_, 1);
}

TEST(TimeSynchronizer, eventInEventOut)
{
  message_filters::TimeSynchronizer<Msg, Msg> sync(2);
  EventHelper h;
  sync.registerCallback(&EventHelper::callback, &h);
  message_filters::MessageEvent<Msg const> evt(std::make_shared<Msg>(),
                                               rosa::Time(4, 0));

  sync.add<0>(evt);
  sync.add<1>(evt);

  ASSERT_TRUE(h.e1_.getMessage());
  ASSERT_TRUE(h.e2_.getMessage());
  ASSERT_EQ(h.e1_.getReceiptTime(), evt.getReceiptTime());
  ASSERT_EQ(h.e2_.getReceiptTime(), evt.getReceiptTime());
}

TEST(TimeSynchronizer, connectConstructor)
{
  message_filters::PassThrough<Msg> pt1, pt2;
  message_filters::TimeSynchronizer<Msg, Msg> sync(pt1, pt2, 1);
  sync_test::Helper h;
  sync.registerCallback(std::bind(&sync_test::Helper::cb, &h));
  MsgPtr m(std::make_shared<Msg>());
  m->header.stamp = rosa::Clock().now();

  pt1.add(m);
  ASSERT_EQ(h.count_, 0);
  pt2.add(m);
  ASSERT_EQ(h.count_, 1);
}
