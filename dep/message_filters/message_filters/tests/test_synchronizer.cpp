#include <array>
#include <memory>

#include <gtest/gtest.h>

#include "test_helpers.h"

TEST(Synchronizer, compile2)
{
  message_filters::NullFilter<Msg> f0, f1;
  message_filters::Synchronizer<Policy2> sync(f0, f1);
}

TEST(Synchronizer, compile3)
{
  message_filters::NullFilter<Msg> f0, f1, f2;
  message_filters::Synchronizer<Policy3> sync(f0, f1, f2);
}

TEST(Synchronizer, compile4)
{
  message_filters::NullFilter<Msg> f0, f1, f2, f3;
  message_filters::Synchronizer<Policy4> sync(f0, f1, f2, f3);
}

TEST(Synchronizer, compile5)
{
  message_filters::NullFilter<Msg> f0, f1, f2, f3, f4;
  message_filters::Synchronizer<Policy5> sync(f0, f1, f2, f3, f4);
}

TEST(Synchronizer, compile6)
{
  message_filters::NullFilter<Msg> f0, f1, f2, f3, f4, f5;
  message_filters::Synchronizer<Policy6> sync(f0, f1, f2, f3, f4, f5);
}

TEST(Synchronizer, compile7)
{
  message_filters::NullFilter<Msg> f0, f1, f2, f3, f4, f5, f6;
  message_filters::Synchronizer<Policy7> sync(f0, f1, f2, f3, f4, f5, f6);
}

TEST(Synchronizer, compile8)
{
  message_filters::NullFilter<Msg> f0, f1, f2, f3, f4, f5, f6, f7;
  message_filters::Synchronizer<Policy8> sync(f0, f1, f2, f3, f4, f5, f6, f7);
}

TEST(Synchronizer, compile9)
{
  message_filters::NullFilter<Msg> f0, f1, f2, f3, f4, f5, f6, f7, f8;
  message_filters::Synchronizer<Policy9> sync(f0, f1, f2, f3, f4, f5, f6, f7,
                                              f8);
}

TEST(Synchronizer, compileFunction2)
{
  sync_test::testSyncRegisterCallback<Policy2>(sync_test::function2);
}

TEST(Synchronizer, compileFunction3)
{
  sync_test::testSyncRegisterCallback<Policy3>(sync_test::function3);
}

TEST(Synchronizer, compileFunction4)
{
  sync_test::testSyncRegisterCallback<Policy4>(sync_test::function4);
}

TEST(Synchronizer, compileFunction5)
{
  sync_test::testSyncRegisterCallback<Policy5>(sync_test::function5);
}

TEST(Synchronizer, compileFunction6)
{
  sync_test::testSyncRegisterCallback<Policy6>(sync_test::function6);
}

TEST(Synchronizer, compileFunction7)
{
  sync_test::testSyncRegisterCallback<Policy7>(sync_test::function7);
}

TEST(Synchronizer, compileFunction8)
{
  sync_test::testSyncRegisterCallback<Policy8>(sync_test::function8);
}

TEST(Synchronizer, compileFunction9)
{
  sync_test::testSyncRegisterCallback<Policy9>(sync_test::function9);
}
TEST(Synchronizer, compileMethod2)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy2> sync;
  sync.registerCallback(&MethodHelper::method2, &h);
}

TEST(Synchronizer, compileMethod3)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy3> sync;
  sync.registerCallback(&MethodHelper::method3, &h);
}

TEST(Synchronizer, compileMethod4)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy4> sync;
  sync.registerCallback(&MethodHelper::method4, &h);
}

TEST(Synchronizer, compileMethod5)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy5> sync;
  sync.registerCallback(&MethodHelper::method5, &h);
}

TEST(Synchronizer, compileMethod6)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy6> sync;
  sync.registerCallback(&MethodHelper::method6, &h);
}

TEST(Synchronizer, compileMethod7)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy7> sync;
  sync.registerCallback(&MethodHelper::method7, &h);
}

TEST(Synchronizer, compileMethod8)
{
  MethodHelper h;
  message_filters::Synchronizer<Policy8> sync;
  sync.registerCallback(&MethodHelper::method8, &h);
}

TEST(Synchronizer, add2)
{
  message_filters::Synchronizer<Policy2> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
}

TEST(Synchronizer, add3)
{
  message_filters::Synchronizer<Policy3> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
}

TEST(Synchronizer, add4)
{
  message_filters::Synchronizer<Policy4> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
  ASSERT_EQ(sync.added_[3], 0);
  sync.add<3>(m);
  ASSERT_EQ(sync.added_[3], 1);
}

TEST(Synchronizer, add5)
{
  message_filters::Synchronizer<Policy5> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
  ASSERT_EQ(sync.added_[3], 0);
  sync.add<3>(m);
  ASSERT_EQ(sync.added_[3], 1);
  ASSERT_EQ(sync.added_[4], 0);
  sync.add<4>(m);
  ASSERT_EQ(sync.added_[4], 1);
}

TEST(Synchronizer, add6)
{
  message_filters::Synchronizer<Policy6> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
  ASSERT_EQ(sync.added_[3], 0);
  sync.add<3>(m);
  ASSERT_EQ(sync.added_[3], 1);
  ASSERT_EQ(sync.added_[4], 0);
  sync.add<4>(m);
  ASSERT_EQ(sync.added_[4], 1);
  ASSERT_EQ(sync.added_[5], 0);
  sync.add<5>(m);
  ASSERT_EQ(sync.added_[5], 1);
}

TEST(Synchronizer, add7)
{
  message_filters::Synchronizer<Policy7> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
  ASSERT_EQ(sync.added_[3], 0);
  sync.add<3>(m);
  ASSERT_EQ(sync.added_[3], 1);
  ASSERT_EQ(sync.added_[4], 0);
  sync.add<4>(m);
  ASSERT_EQ(sync.added_[4], 1);
  ASSERT_EQ(sync.added_[5], 0);
  sync.add<5>(m);
  ASSERT_EQ(sync.added_[5], 1);
  ASSERT_EQ(sync.added_[6], 0);
  sync.add<6>(m);
  ASSERT_EQ(sync.added_[6], 1);
}

TEST(Synchronizer, add8)
{
  message_filters::Synchronizer<Policy8> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
  ASSERT_EQ(sync.added_[3], 0);
  sync.add<3>(m);
  ASSERT_EQ(sync.added_[3], 1);
  ASSERT_EQ(sync.added_[4], 0);
  sync.add<4>(m);
  ASSERT_EQ(sync.added_[4], 1);
  ASSERT_EQ(sync.added_[5], 0);
  sync.add<5>(m);
  ASSERT_EQ(sync.added_[5], 1);
  ASSERT_EQ(sync.added_[6], 0);
  sync.add<6>(m);
  ASSERT_EQ(sync.added_[6], 1);
  ASSERT_EQ(sync.added_[7], 0);
  sync.add<7>(m);
  ASSERT_EQ(sync.added_[7], 1);
}

TEST(Synchronizer, add9)
{
  message_filters::Synchronizer<Policy9> sync;
  MsgPtr m(std::make_shared<Msg>());

  ASSERT_EQ(sync.added_[0], 0);
  sync.add<0>(m);
  ASSERT_EQ(sync.added_[0], 1);
  ASSERT_EQ(sync.added_[1], 0);
  sync.add<1>(m);
  ASSERT_EQ(sync.added_[1], 1);
  ASSERT_EQ(sync.added_[2], 0);
  sync.add<2>(m);
  ASSERT_EQ(sync.added_[2], 1);
  ASSERT_EQ(sync.added_[3], 0);
  sync.add<3>(m);
  ASSERT_EQ(sync.added_[3], 1);
  ASSERT_EQ(sync.added_[4], 0);
  sync.add<4>(m);
  ASSERT_EQ(sync.added_[4], 1);
  ASSERT_EQ(sync.added_[5], 0);
  sync.add<5>(m);
  ASSERT_EQ(sync.added_[5], 1);
  ASSERT_EQ(sync.added_[6], 0);
  sync.add<6>(m);
  ASSERT_EQ(sync.added_[6], 1);
  ASSERT_EQ(sync.added_[7], 0);
  sync.add<7>(m);
  ASSERT_EQ(sync.added_[7], 1);
  ASSERT_EQ(sync.added_[8], 0);
  sync.add<8>(m);
  ASSERT_EQ(sync.added_[8], 1);
}
