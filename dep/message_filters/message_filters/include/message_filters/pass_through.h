
// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :pass_through.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/23
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <utility>
#include <vector>

#include "message_filters/simple_filter.h"

namespace message_filters
{
  /**
   * \brief Simple passthrough filter.  What comes in goes out immediately.
   */
  template <typename M>
  class PassThrough : public SimpleFilter<M>
  {
   public:
    typedef std::shared_ptr<M const> MConstPtr;
    typedef MessageEvent<M const> EventType;

    PassThrough() {}

    template <typename F>
    PassThrough(F& f)
    {
      connectInput(f);
    }

    template <class F>
    void connectInput(F& f)
    {
      incoming_connection_.disconnect();
      incoming_connection_ =
          f.registerCallback(typename SimpleFilter<M>::EventCallback(
              std::bind(&PassThrough::cb, this, std::placeholders::_1)));
    }

    void add(const MConstPtr& msg) { add(EventType(msg)); }

    void add(const EventType& evt) { this->signalMessage(evt); }

   private:
    void cb(const EventType& evt) { add(evt); }

    Connection incoming_connection_;
  };

}  // namespace message_filters
