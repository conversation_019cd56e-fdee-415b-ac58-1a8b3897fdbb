// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :cache.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <cstddef>
#include <deque>
#include <functional>
#include <memory>
#include <vector>

#include "message_filters/connection.h"
#include "message_filters/message_traits.h"
#include "message_filters/null_types.h"
#include "message_filters/simple_filter.h"

namespace message_filters
{
  template <class M>
  class Cache : public SimpleFilter<M>
  {
   public:
    typedef std::shared_ptr<M const> MConstPtr;
    typedef MessageEvent<M const> EventType;

    template <class F>
    explicit Cache(F& f, unsigned int cache_size = 1)
    {
      setCacheSize(cache_size);
      connectInput(f);
    }

    explicit Cache(unsigned int cache_size = 1) { setCacheSize(cache_size); }

    template <class F>
    void connectInput(F& f)
    {
      incoming_connection_ =
          f.registerCallback(typename SimpleFilter<M>::EventCallback(
              std::bind(&Cache::callback, this, std::placeholders::_1)));
    }

    ~Cache() { incoming_connection_.disconnect(); }

    void setCacheSize(unsigned int cache_size)
    {
      if (cache_size == 0)
      {
        return;
      }

      cache_size_ = cache_size;
    }

    void add(const MConstPtr& msg) { add(EventType(msg)); }

    void add(const EventType& evt)
    {
      namespace mt = message_filters::message_traits;

      {
        std::lock_guard<std::mutex> lock(cache_lock_);

        // 队列溢出时，移除最早的数据
        while (cache_.size() >= cache_size_)
        {
          cache_.pop_front();
        }

        // 排序
        typename std::deque<EventType>::reverse_iterator rev_it =
            cache_.rbegin();

        rosa::Time evt_stamp = mt::TimeStamp<M>::value(*evt.getMessage());
        while (rev_it != cache_.rend() &&
               mt::TimeStamp<M>::value(*(*rev_it).getMessage()) > evt_stamp)
        {
          rev_it++;
        }

        cache_.insert(rev_it.base(), evt);
      }  // namespace message_filters::message_traits;

      this->signalMessage(evt);
    }

    std::vector<MConstPtr> getInterval(const rosa::Time& start,
                                       const rosa::Time& end) const
    {
      namespace mt = message_filters::message_traits;
      std::lock_guard<std::mutex> lock(cache_lock_);

      size_t start_index = 0;
      while (start_index < cache_.size() &&
             mt::TimeStamp<M>::value(*cache_[start_index].getMessage()) < start)
      {
        start_index++;
      }

      size_t end_index = start_index;
      while (end_index < cache_.size() &&
             mt::TimeStamp<M>::value(*cache_[end_index].getMessage()) <= end)
      {
        end_index++;
      }

      std::vector<MConstPtr> interval_elems;
      interval_elems.reserve(end_index - start_index);
      for (size_t i = start_index; i < end_index; i++)
      {
        interval_elems.push_back(cache_[i].getMessage());
      }

      return interval_elems;
    }

    std::vector<MConstPtr> getSurroundingInterval(const rosa::Time& start,
                                                  const rosa::Time& end) const
    {
      namespace mt = message_filters::message_traits;

      std::lock_guard<std::mutex> lock(cache_lock_);
      int start_index = static_cast<int>(cache_.size()) - 1;
      while (start_index > 0 &&
             mt::TimeStamp<M>::value(*cache_[start_index].getMessage()) > start)
      {
        start_index--;
      }

      int end_index = start_index;
      while (end_index < static_cast<int>(cache_.size()) - 1 &&
             mt::TimeStamp<M>::value(*cache_[end_index].getMessage()) < end)
      {
        end_index++;
      }

      std::vector<MConstPtr> interval_elems;
      interval_elems.reserve(end_index - start_index + 1);
      for (int i = start_index; i <= end_index; i++)
      {
        interval_elems.push_back(cache_[i].getMessage());
      }

      return interval_elems;
    }

    MConstPtr getElemBeforeTime(const rosa::Time& time) const
    {
      namespace mt = message_filters::message_traits;

      std::lock_guard<std::mutex> lock(cache_lock_);

      MConstPtr out;

      unsigned int i = 0;
      int elem_index = -1;
      while (i < cache_.size() &&
             mt::TimeStamp<M>::value(*cache_[i].getMessage()) < time)
      {
        elem_index = i;
        i++;
      }

      if (elem_index >= 0)
      {
        out = cache_[elem_index].getMessage();
      }

      return out;
    }

    MConstPtr getElemAfterTime(const rosa::Time& time) const
    {
      namespace mt = message_filters::message_traits;

      std::lock_guard<std::mutex> lock(cache_lock_);

      MConstPtr out;

      int i = static_cast<int>(cache_.size()) - 1;
      int elem_index = -1;
      while (i >= 0 && mt::TimeStamp<M>::value(*cache_[i].getMessage()) > time)
      {
        elem_index = i;
        i--;
      }

      if (elem_index >= 0)
      {
        out = cache_[elem_index].getMessage();
      }
      else
      {
        out.reset();
      }

      return out;
    }

    rosa::Time getLatestTime() const
    {
      namespace mt = message_filters::message_traits;

      std::lock_guard<std::mutex> lock(cache_lock_);

      rosa::Time latest_time;

      if (cache_.size() > 0)
      {
        latest_time = mt::TimeStamp<M>::value(*cache_.back().getMessage());
      }

      return latest_time;
    }

    rosa::Time getOldestTime() const
    {
      namespace mt = message_filters::message_traits;

      std::lock_guard<std::mutex> lock(cache_lock_);

      rosa::Time oldest_time;

      if (cache_.size() > 0)
      {
        oldest_time = mt::TimeStamp<M>::value(*cache_.front().getMessage());
      }

      return oldest_time;
    }

    MConstPtr getLatestElem() const
    {
      std::lock_guard<std::mutex> lock(cache_lock_);
      MConstPtr out;

      if (cache_.size() > 0)
      {
        out = cache_.back().getMessage();
      }
      else
      {
        out.reset();
      }

      return out;
    }

    MConstPtr getOldestElem() const
    {
      std::lock_guard<std::mutex> lock(cache_lock_);
      MConstPtr out;

      if (cache_.size() > 0)
      {
        out = cache_.front().getMessage();
      }
      else
      {
        out.reset();
      }

      return out;
    }

    size_t size() const
    {
      std::lock_guard<std::mutex> lock(cache_lock_);
      return cache_.size();
    }

   private:
    void callback(const EventType& evt) { add(evt); }

    mutable std::mutex cache_lock_;
    std::deque<EventType> cache_;
    unsigned int cache_size_;
    Connection incoming_connection_;
  };
}  // namespace message_filters
