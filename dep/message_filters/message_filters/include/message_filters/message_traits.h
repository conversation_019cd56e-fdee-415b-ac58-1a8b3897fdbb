// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :message_traits.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <type_traits>

#include "rosa/time.h"
#include "shm_msgs/msg/Header.h"
#include "std_msgs/msg/Header.h"

namespace message_filters
{
  namespace message_traits
  {
    template <typename M, typename = void>
    struct HasHeader : public std::false_type
    {
    };

    template <typename M>
    struct HasHeader<
        M,
        typename std::enable_if<
            std::is_same<std_msgs::msg::Header, decltype(M().header)>::value ||
            std::is_same<shm_msgs::msg::Header, decltype(M().header)>::value>::
            type> : public std::true_type
    {
    };

    template <typename M, typename Enable = void>
    struct FrameId
    {
      static std::string* pointer(M& m)
      {
        (void)m;
        return nullptr;
      }
      static std::string const* pointer(const M& m)
      {
        (void)m;
        return nullptr;
      }
    };

    template <typename M>
    struct FrameId<M, typename std::enable_if<HasHeader<M>::value>::type>
    {
      static std::string* pointer(M& m) { return &m.header.frame_id; }
      static std::string const* pointer(const M& m)
      {
        return &m.header.frame_id;
      }
      static std::string value(const M& m) { return m.header.frame_id; }
    };

    template <typename M, typename Enable = void>
    struct TimeStamp
    {
      static rosa::Time value(const M& m)
      {
        (void)m;
        return rosa::Time();
      }
    };

    template <typename M>
    struct TimeStamp<M, typename std::enable_if<HasHeader<M>::value>::type>
    {
      static rosa::Time value(const M& m)
      {
        // TODO: modify to ROS_TIME
        return rosa::Time(m.header.stamp, rosa::ClockType::SystemTime);
      }
    };

  }  // namespace message_traits
}  // namespace message_filters
