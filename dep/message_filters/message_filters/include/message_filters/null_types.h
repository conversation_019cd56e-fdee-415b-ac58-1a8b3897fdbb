// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :null_types.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <memory>

#include "message_filters/connection.h"
#include "message_filters/message_traits.h"

namespace message_filters
{
  struct NullType
  {
  };

  typedef std::shared_ptr<NullType const> NullTypeConstPtr;

  template <class M>
  struct NullFilter
  {
    template <typename C>
    Connection registerCallback(const C &)
    {
      return Connection();
    }

    template <typename P>
    Connection registerCallback(const std::function<void(P)> &)
    {
      return Connection();
    }
  };

  namespace message_traits
  {
    template <>
    struct TimeStamp<message_filters::NullType>
    {
      static rosa::Time value(const message_filters::NullType &)
      {
        return rosa::Time();
      }
    };
  }  // namespace message_traits
}  // namespace message_filters
