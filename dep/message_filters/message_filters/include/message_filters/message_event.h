// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :message_event.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <memory>
#include <type_traits>

#include "rosa/clock.h"
#include "rosa/time.h"

namespace message_filters
{
  typedef std::map<std::string, std::string> M_string;
  typedef std::shared_ptr<M_string> M_stringPtr;

  template <typename M>
  struct DefaultMessageCreator
  {
    std::shared_ptr<M> operator()() { return std::make_shared<M>(); }
  };

  template <typename M>
  class MessageEvent
  {
   public:
    typedef typename std::add_const<M>::type ConstMessage;
    typedef typename std::remove_const<M>::type Message;
    typedef std::shared_ptr<Message> MessagePtr;
    typedef std::shared_ptr<ConstMessage> ConstMessagePtr;
    typedef std::function<MessagePtr()> CreateFunction;

    MessageEvent() : nonconst_need_copy_(true) {}

    MessageEvent(const MessageEvent<Message>& rhs) { *this = rhs; }

    MessageEvent(const MessageEvent<ConstMessage>& rhs) { *this = rhs; }

    MessageEvent(const MessageEvent<Message>& rhs, bool nonconst_need_copy)
    {
      *this = rhs;
      nonconst_need_copy_ = nonconst_need_copy;
    }

    MessageEvent(const MessageEvent<ConstMessage>& rhs, bool nonconst_need_copy)
    {
      *this = rhs;
      nonconst_need_copy_ = nonconst_need_copy;
    }

    MessageEvent(const MessageEvent<void const>& rhs,
                 const CreateFunction& create)
    {
      init(std::const_pointer_cast<Message>(
               std::static_pointer_cast<ConstMessage>(rhs.getMessage())),
           rhs.getReceiptTime(), rhs.nonConstWillCopy(), create);
    }

    MessageEvent(const ConstMessagePtr& message)
    {
      init(message, rosa::Clock().now(), true,
           message_filters::DefaultMessageCreator<Message>());
    }

    MessageEvent(const ConstMessagePtr& message, rosa::Time receipt_time)
    {
      init(message, receipt_time, true,
           message_filters::DefaultMessageCreator<Message>());
    }

    MessageEvent(const ConstMessagePtr& message,
                 rosa::Time receipt_time,
                 bool nonconst_need_copy,
                 const CreateFunction& create)
    {
      init(message, receipt_time, nonconst_need_copy, create);
    }

    void init(const ConstMessagePtr& message,
              rosa::Time receipt_time,
              bool nonconst_need_copy,
              const CreateFunction& create)
    {
      message_ = message;
      receipt_time_ = receipt_time;
      nonconst_need_copy_ = nonconst_need_copy;
      create_ = create;
    }

    void operator=(const MessageEvent<Message>& rhs)
    {
      init(std::static_pointer_cast<Message>(rhs.getMessage()),
           rhs.getReceiptTime(), rhs.nonConstWillCopy(),
           rhs.getMessageFactory());
      message_copy_.reset();
    }

    void operator=(const MessageEvent<ConstMessage>& rhs)
    {
      init(std::const_pointer_cast<Message>(
               std::static_pointer_cast<ConstMessage>(rhs.getMessage())),
           rhs.getReceiptTime(), rhs.nonConstWillCopy(),
           rhs.getMessageFactory());
      message_copy_.reset();
    }

    std::shared_ptr<M> getMessage() const
    {
      return copyMessageIfNecessary<M>();
    }

    const std::shared_ptr<ConstMessage>& getConstMessage() const
    {
      return message_;
    }

    rosa::Time getReceiptTime() const { return receipt_time_; }

    bool nonConstWillCopy() const { return nonconst_need_copy_; }
    bool getMessageWillCopy() const
    {
      return !std::is_const<M>::value && nonconst_need_copy_;
    }

    bool operator<(const MessageEvent<M>& rhs)
    {
      if (message_ != rhs.message_)
      {
        return message_ < rhs.message_;
      }

      if (receipt_time_ != rhs.receipt_time_)
      {
        return receipt_time_ < rhs.receipt_time_;
      }

      return nonconst_need_copy_ < rhs.nonconst_need_copy_;
    }

    bool operator==(const MessageEvent<M>& rhs)
    {
      return message_ == rhs.message_ && receipt_time_ == rhs.receipt_time_ &&
             nonconst_need_copy_ == rhs.nonconst_need_copy_;
    }

    bool operator!=(const MessageEvent<M>& rhs) { return !(*this == rhs); }

    const CreateFunction& getMessageFactory() const { return create_; }

   private:
    template <typename M2>
    typename std::enable_if<!std::is_void<M2>::value, std::shared_ptr<M>>::type
    copyMessageIfNecessary() const
    {
      if (std::is_const<M>::value || !nonconst_need_copy_)
      {
        return std::const_pointer_cast<Message>(message_);
      }

      if (message_copy_)
      {
        return message_copy_;
      }

      assert(create_);
      message_copy_ = create_();
      *message_copy_ = *message_;

      return message_copy_;
    }

    template <typename M2>
    typename std::enable_if<std::is_void<M2>::value, std::shared_ptr<M>>::type
    copyMessageIfNecessary() const
    {
      return std::const_pointer_cast<Message>(message_);
    }

    ConstMessagePtr message_;
    mutable MessagePtr message_copy_;
    rosa::Time receipt_time_;
    bool nonconst_need_copy_;
    CreateFunction create_;

    static const std::string s_unknown_publisher_string_;
  };

  template <typename M>
  const std::string MessageEvent<M>::s_unknown_publisher_string_(
      "unknown_publisher");

}  // namespace message_filters
