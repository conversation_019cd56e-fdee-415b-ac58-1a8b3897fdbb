// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :parameter_adapter.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <memory>
#include <type_traits>

#include "message_filters/message_event.h"

namespace message_filters
{
  template <typename M>
  struct ParameterAdapter
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef M Parameter;
    static const bool is_const = true;

    static Parameter getParameter(const Event &event)
    {
      return *event.getMessage();
    }
  };

  template <typename M>
  struct ParameterAdapter<const std::shared_ptr<M const> &>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef const std::shared_ptr<Message const> Parameter;
    static const bool is_const = true;

    static Parameter getParameter(const Event &event)
    {
      return event.getMessage();
    }
  };

  template <typename M>
  struct ParameterAdapter<const std::shared_ptr<M> &>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef std::shared_ptr<Message> Parameter;
    static const bool is_const = false;

    static Parameter getParameter(const Event &event)
    {
      return MessageEvent<Message>(event).getMessage();
    }
  };

  template <typename M>
  struct ParameterAdapter<const M &>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef const M &Parameter;
    static const bool is_const = true;

    static Parameter getParameter(const Event &event)
    {
      return *event.getMessage();
    }
  };

  template <typename M>
  struct ParameterAdapter<std::shared_ptr<M const>>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef std::shared_ptr<Message const> Parameter;
    static const bool is_const = true;

    static Parameter getParameter(const Event &event)
    {
      return event.getMessage();
    }
  };

  template <typename M>
  struct ParameterAdapter<std::shared_ptr<M>>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef std::shared_ptr<Message> Parameter;
    static const bool is_const = false;

    static Parameter getParameter(const Event &event)
    {
      return MessageEvent<Message>(event).getMessage();
    }
  };

  template <typename M>
  struct ParameterAdapter<const MessageEvent<M const> &>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef const MessageEvent<Message const> &Parameter;
    static const bool is_const = true;

    static Parameter getParameter(const Event &event) { return event; }
  };

  template <typename M>
  struct ParameterAdapter<const MessageEvent<M> &>
  {
    typedef typename std::remove_reference<
        typename std::remove_const<M>::type>::type Message;
    typedef MessageEvent<Message const> Event;
    typedef MessageEvent<Message> Parameter;
    static const bool is_const = false;

    static Parameter getParameter(const Event &event)
    {
      return MessageEvent<Message>(event);
    }
  };

}  // namespace message_filters
