
// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :time_synchronizer.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/24
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <memory>

#include "message_filters/message_event.h"
#include "message_filters/sync_policies/exact_time.h"
#include "message_filters/synchronizer.h"

namespace message_filters
{
  /**
   * \brief Synchronizes up to 9 messages by their timestamps.
   *
   * TimeSynchronizer synchronizes up to 9 incoming channels by the timestamps
  contained in their messages' headers.
   * TimeSynchronizer takes anywhere from 2 to 9 message types as template
  parameters, and passes them through to a
   * callback which takes a shared pointer of each.
   *
   * The required queue size parameter when constructing the TimeSynchronizer
  tells it how many sets of messages it should
   * store (by timestamp) while waiting for messages to arrive and complete
  their "set"
   *
   * \section connections CONNECTIONS
   *
   * The input connections for the TimeSynchronizer object is the same signature
  as for rosa subscription callbacks, ie. \verbatim void callback(const
  std::shared_ptr<M const>&); \endverbatim
   * The output connection for the TimeSynchronizer object is dependent on the
  number of messages being synchronized. For
   * a 3-message synchronizer for example, it would be:
  \verbatim
  void callback(const std::shared_ptr<M0 const>&, const std::shared_ptr<M1
  const>&, const std::shared_ptr<M2 const>&); \endverbatim
   * \section usage USAGE
   * Example usage would be:
  \verbatim
  TimeSynchronizer<sensor_msgs::msg::CameraInfo, sensor_msgs::msg::Image,
  sensor_msgs::msg::Image> sync_policies(caminfo_sub, limage_sub, rimage_sub,
  3); sync_policies.registerCallback(callback); \endverbatim

   * The callback is then of the form:
  \verbatim
  void callback(const sensor_msgs::msg::CameraInfo::SharedPtr, const
  sensor_msgs::msg::Image::SharedPtr, const sensor_msgs::msg::Image::SharedPtr);
  \endverbatim
   *
   */
  template <class M0,
            class M1,
            class M2 = NullType,
            class M3 = NullType,
            class M4 = NullType,
            class M5 = NullType,
            class M6 = NullType,
            class M7 = NullType,
            class M8 = NullType>
  class TimeSynchronizer
      : public Synchronizer<
            sync_policies::ExactTime<M0, M1, M2, M3, M4, M5, M6, M7, M8>>
  {
   public:
    typedef sync_policies::ExactTime<M0, M1, M2, M3, M4, M5, M6, M7, M8> Policy;
    typedef Synchronizer<Policy> Base;
    typedef std::shared_ptr<M0 const> M0ConstPtr;
    typedef std::shared_ptr<M1 const> M1ConstPtr;
    typedef std::shared_ptr<M2 const> M2ConstPtr;
    typedef std::shared_ptr<M3 const> M3ConstPtr;
    typedef std::shared_ptr<M4 const> M4ConstPtr;
    typedef std::shared_ptr<M5 const> M5ConstPtr;
    typedef std::shared_ptr<M6 const> M6ConstPtr;
    typedef std::shared_ptr<M7 const> M7ConstPtr;
    typedef std::shared_ptr<M8 const> M8ConstPtr;

    using Base::add;
    using Base::connectInput;
    using Base::getName;
    using Base::registerCallback;
    using Base::setName;
    using Policy::registerDropCallback;
    typedef typename Base::M0Event M0Event;
    typedef typename Base::M1Event M1Event;
    typedef typename Base::M2Event M2Event;
    typedef typename Base::M3Event M3Event;
    typedef typename Base::M4Event M4Event;
    typedef typename Base::M5Event M5Event;
    typedef typename Base::M6Event M6Event;
    typedef typename Base::M7Event M7Event;
    typedef typename Base::M8Event M8Event;

    template <class F0, class F1>
    TimeSynchronizer(F0& f0, F1& f1, uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1);
    }

    template <class F0, class F1, class F2>
    TimeSynchronizer(F0& f0, F1& f1, F2& f2, uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2);
    }

    template <class F0, class F1, class F2, class F3>
    TimeSynchronizer(F0& f0, F1& f1, F2& f2, F3& f3, uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2, f3);
    }

    template <class F0, class F1, class F2, class F3, class F4>
    TimeSynchronizer(
        F0& f0, F1& f1, F2& f2, F3& f3, F4& f4, uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2, f3, f4);
    }

    template <class F0, class F1, class F2, class F3, class F4, class F5>
    TimeSynchronizer(
        F0& f0, F1& f1, F2& f2, F3& f3, F4& f4, F5& f5, uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2, f3, f4, f5);
    }

    template <class F0,
              class F1,
              class F2,
              class F3,
              class F4,
              class F5,
              class F6>
    TimeSynchronizer(F0& f0,
                     F1& f1,
                     F2& f2,
                     F3& f3,
                     F4& f4,
                     F5& f5,
                     F6& f6,
                     uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2, f3, f4, f5, f6);
    }

    template <class F0,
              class F1,
              class F2,
              class F3,
              class F4,
              class F5,
              class F6,
              class F7>
    TimeSynchronizer(F0& f0,
                     F1& f1,
                     F2& f2,
                     F3& f3,
                     F4& f4,
                     F5& f5,
                     F6& f6,
                     F7& f7,
                     uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2, f3, f4, f5, f6, f7);
    }

    template <class F0,
              class F1,
              class F2,
              class F3,
              class F4,
              class F5,
              class F6,
              class F7,
              class F8>
    TimeSynchronizer(F0& f0,
                     F1& f1,
                     F2& f2,
                     F3& f3,
                     F4& f4,
                     F5& f5,
                     F6& f6,
                     F7& f7,
                     F8& f8,
                     uint32_t queue_size)
        : Base(Policy(queue_size))
    {
      connectInput(f0, f1, f2, f3, f4, f5, f6, f7, f8);
    }

    TimeSynchronizer(uint32_t queue_size) : Base(Policy(queue_size)) {}

    ////////////////////////////////////////////////////////////////
    // For backwards compatibility
    ////////////////////////////////////////////////////////////////
    void add0(const M0ConstPtr& msg) { this->template add<0>(M0Event(msg)); }

    void add1(const M1ConstPtr& msg) { this->template add<1>(M1Event(msg)); }

    void add2(const M2ConstPtr& msg) { this->template add<2>(M2Event(msg)); }

    void add3(const M3ConstPtr& msg) { this->template add<3>(M3Event(msg)); }

    void add4(const M4ConstPtr& msg) { this->template add<4>(M4Event(msg)); }

    void add5(const M5ConstPtr& msg) { this->template add<5>(M5Event(msg)); }

    void add6(const M6ConstPtr& msg) { this->template add<6>(M6Event(msg)); }

    void add7(const M7ConstPtr& msg) { this->template add<7>(M7Event(msg)); }

    void add8(const M8ConstPtr& msg) { this->template add<8>(M8Event(msg)); }
  };

}  // namespace message_filters
