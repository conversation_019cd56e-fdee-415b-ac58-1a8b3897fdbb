// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :visibility_control.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/23
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#ifdef __cplusplus
extern "C"
{
#endif

  // This logic was borrowed (then namespaced) from the examples on the gcc
  // wiki:
  //     https://gcc.gnu.org/wiki/Visibility

#if defined _WIN32 || defined __CYGWIN__
#ifdef __GNUC__
#define MESSAGE_FILTERS_EXPORT __attribute__((dllexport))
#define MESSAGE_FILTERS_IMPORT __attribute__((dllimport))
#else
#define MESSAGE_FILTERS_EXPORT __declspec(dllexport)
#define MESSAGE_FILTERS_IMPORT __declspec(dllimport)
#endif
#ifdef MESSAGE_FILTERS_BUILDING_DLL
#define MESSAGE_FILTERS_PUBLIC MESSAGE_FILTERS_EXPORT
#else
#define MESSAGE_FILTERS_PUBLIC MESSAGE_FILTERS_IMPORT
#endif
#define MESSAGE_FILTERS_LOCAL
#else
#define MESSAGE_FILTERS_EXPORT __attribute__((visibility("default")))
#define MESSAGE_FILTERS_IMPORT
#if __GNUC__ >= 4
#define MESSAGE_FILTERS_PUBLIC __attribute__((visibility("default")))
#define MESSAGE_FILTERS_LOCAL __attribute__((visibility("hidden")))
#else
#define MESSAGE_FILTERS_PUBLIC
#define MESSAGE_FILTERS_LOCAL
#endif
#endif

#ifdef __cplusplus
}
#endif
