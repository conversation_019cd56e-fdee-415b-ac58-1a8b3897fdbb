// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :subscriber.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/23
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <memory>
#include <stdexcept>
#include <string>
#include <type_traits>

#include "message_filters/connection.h"
#include "message_filters/simple_filter.h"
#include "rosa/node.h"
#include "rosa/qos.h"
#include "rosa/traits.h"

namespace message_filters
{
  template <class NodeType = rosa::Node>
  class SubscriberBase
  {
   public:
    typedef std::shared_ptr<NodeType> NodePtr;

    virtual ~SubscriberBase() = default;

    virtual void subscribe(
        NodePtr node,
        const std::string& topic,
        const rosa::QoS qos = rosa::QoS(10),
        const rosa::CallbackGroup::SharedPtr callback_group = nullptr)
    {
      this->subscribe(node.get(), topic, qos, callback_group);
    }

    virtual void subscribe(
        NodeType* node,
        const std::string& topic,
        const rosa::QoS qos = rosa::QoS(10),
        const rosa::CallbackGroup::SharedPtr callback_group = nullptr) = 0;

    virtual void subscribe() = 0;
    virtual void unsubscribe() = 0;
  };

  template <typename T>
  using SubscriberBasePtr = std::shared_ptr<SubscriberBase<T>>;

  template <typename M, bool is_adapter = rosa::is_msg<M>::value>
  struct message_type
  {
    using type = M;
  };

  template <typename M>
  using message_type_t = typename message_type<M>::type;

  template <class M, class NodeType = rosa::Node>
  class Subscriber : public SubscriberBase<NodeType>,
                     public SimpleFilter<message_type_t<M>>
  {
   public:
    typedef std::shared_ptr<NodeType> NodePtr;
    typedef message_type_t<M> MessageType;
    typedef MessageEvent<MessageType const> EventType;

    Subscriber(NodePtr node,
               const std::string& topic,
               const rosa::QoS qos = rosa::QoS(10),
               const rosa::CallbackGroup::SharedPtr callback_group = nullptr)
    {
      subscribe(node.get(), topic, qos, callback_group);
    }

    Subscriber(NodeType* node,
               const std::string& topic,
               const rosa::QoS qos = rosa::QoS(10),
               const rosa::CallbackGroup::SharedPtr callback_group = nullptr)
    {
      subscribe(node, topic, qos, callback_group);
    }

    Subscriber() = default;

    ~Subscriber() { unsubscribe(); }

    void subscribe(
        NodePtr node,
        const std::string& topic,
        const rosa::QoS qos = rosa::QoS(10),
        const rosa::CallbackGroup::SharedPtr callback_group = nullptr) override
    {
      subscribe(node.get(), topic, qos, callback_group);
      node_shared_ = node;
    }

    void subscribe(
        NodeType* node,
        const std::string& topic,
        const rosa::QoS qos = rosa::QoS(10),
        const rosa::CallbackGroup::SharedPtr callback_group = nullptr) override
    {
      unsubscribe();
      if (!topic.empty())
      {
        topic_ = topic;
        // rclcpp_qos.get_rmw_qos_profile() = qos;
        qos_ = qos;
        sub_ = node->template createReader<M>(
            topic, qos,
            [this](const std::shared_ptr<MessageType const> msg) {
              this->cb(EventType(msg));
            },
            callback_group);

        node_raw_ = node;
      }
    }

    void subscribe() override
    {
      if (!topic_.empty())
      {
        if (node_raw_ != nullptr)
        {
          subscribe(node_raw_, topic_, qos_);
        }
        else if (node_shared_ != nullptr)
        {
          subscribe(node_shared_, topic_, qos_);
        }
      }
    }

    void unsubscribe() override { sub_.reset(); }

    std::string getTopic() const { return this->topic_; }

    const typename rosa::Reader<M>::SharedPtr getSubscriber() const
    {
      return sub_;
    }

    template <typename F>
    void connectInput(F& f)
    {
      (void)f;
    }

    void add(const EventType& e) { (void)e; }

   private:
    void cb(const EventType& e) { this->signalMessage(e); }
    typename rosa::Reader<M>::SharedPtr sub_;

    NodePtr node_shared_;
    NodeType* node_raw_{nullptr};

    std::string topic_;
    rosa::QoS qos_;
  };

}  // namespace message_filters
