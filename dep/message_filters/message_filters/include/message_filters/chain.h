// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :chain.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/23
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <memory>
#include <vector>

#include "message_filters/pass_through.h"
#include "message_filters/simple_filter.h"

namespace message_filters
{
  /**
   * \brief Base class for Chain, allows you to store multiple chains in the
   * same container.  Provides filter retrieval by index.
   */
  class ChainBase
  {
   public:
    virtual ~ChainBase() {}
    template <typename F>
    std::shared_ptr<F> getFilter(size_t index) const
    {
      std::shared_ptr<void> filter = getFilterForIndex(index);
      if (filter)
      {
        return std::static_pointer_cast<F>(filter);
      }

      return std::shared_ptr<F>();
    }

   protected:
    virtual std::shared_ptr<void> getFilterForIndex(size_t index) const = 0;
  };
  typedef std::shared_ptr<ChainBase> ChainBasePtr;

  template <typename M>
  class Chain : public ChainBase, public SimpleFilter<M>
  {
   public:
    typedef std::shared_ptr<M const> MConstPtr;
    typedef MessageEvent<M const> EventType;

    Chain() {}
    template <typename F>
    explicit Chain(F &f)
    {
      connectInput(f);
    }

    struct NullDeleter
    {
      void operator()(void const *) const {}
    };

    template <class F>
    size_t addFilter(F *filter)
    {
      std::shared_ptr<F> ptr(filter, NullDeleter());
      return addFilter(ptr);
    }

    template <class F>
    size_t addFilter(const std::shared_ptr<F> &filter)
    {
      FilterInfo info;
      info.add_func = std::bind((void (F::*)(const EventType &)) & F::add,
                                filter.get(), std::placeholders::_1);
      info.filter = filter;
      info.passthrough = std::make_shared<PassThrough<M>>();

      last_filter_connection_.disconnect();
      info.passthrough->connectInput(*filter);
      last_filter_connection_ = info.passthrough->registerCallback(
          typename SimpleFilter<M>::EventCallback(
              std::bind(&Chain::lastFilterCB, this, std::placeholders::_1)));
      if (!filters_.empty())
      {
        filter->connectInput(*filters_.back().passthrough);
      }

      size_t count = filters_.size();
      filters_.push_back(info);
      return count;
    }

    template <typename F>
    std::shared_ptr<F> getFilter(size_t index) const
    {
      if (index >= filters_.size())
      {
        return std::shared_ptr<F>();
      }

      return std::static_pointer_cast<F>(filters_[index].filter);
    }

    template <class F>
    void connectInput(F &f)
    {
      incoming_connection_.disconnect();
      incoming_connection_ =
          f.registerCallback(typename SimpleFilter<M>::EventCallback(
              std::bind(&Chain::incomingCB, this, std::placeholders::_1)));
    }

    void add(const MConstPtr &msg) { add(EventType(msg)); }

    void add(const EventType &evt)
    {
      if (!filters_.empty())
      {
        filters_[0].add_func(evt);
      }
    }

   protected:
    virtual std::shared_ptr<void> getFilterForIndex(size_t index) const
    {
      if (index >= filters_.size())
      {
        return std::shared_ptr<void>();
      }

      return filters_[index].filter;
    }

   private:
    void incomingCB(const EventType &evt) { add(evt); }

    void lastFilterCB(const EventType &evt) { this->signalMessage(evt); }

    struct FilterInfo
    {
      std::function<void(const EventType &)> add_func;
      std::shared_ptr<void> filter;
      std::shared_ptr<PassThrough<M>> passthrough;
    };
    typedef std::vector<FilterInfo> V_FilterInfo;

    V_FilterInfo filters_;
    Connection incoming_connection_;
    Connection last_filter_connection_;
  };
}  // namespace message_filters
