// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :connection.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <functional>
#include <memory>

#include "message_filters/visibility_control.h"

namespace message_filters
{
  class noncopyable
  {
   protected:
    noncopyable() {}
    ~noncopyable() {}
    noncopyable(const noncopyable &) = delete;
    noncopyable &operator=(const noncopyable &) = delete;
  };

  class Connection
  {
   public:
    using VoidDisconnectFunction = std::function<void(void)>;
    using WithConnectionDisconnectFunction =
        std::function<void(const Connection &)>;
    MESSAGE_FILTERS_PUBLIC Connection() {}
    MESSAGE_FILTERS_PUBLIC Connection(const VoidDisconnectFunction &func);
    MESSAGE_FILTERS_PUBLIC void disconnect();

   private:
    VoidDisconnectFunction void_disconnect_;
    WithConnectionDisconnectFunction connection_disconnect_;
  };

}  // namespace message_filters
