// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :signal1.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <algorithm>
#include <memory>
#include <mutex>

#include "message_filters/connection.h"
#include "message_filters/message_event.h"
#include "message_filters/parameter_adapter.h"

namespace message_filters
{
  template <class M>
  class CallbackHelper1
  {
   public:
    virtual ~CallbackHelper1() {}

    virtual void call(const MessageEvent<M const>& event,
                      bool nonconst_need_copy) = 0;

    typedef std::shared_ptr<CallbackHelper1<M>> Ptr;
  };

  template <typename P, typename M>
  class CallbackHelper1T : public CallbackHelper1<M>
  {
   public:
    typedef ParameterAdapter<P> Adapter;
    typedef std::function<void(typename Adapter::Parameter)> Callback;
    typedef typename Adapter::Event Event;

    CallbackHelper1T(const Callback& cb) : callback_(cb) {}

    virtual void call(const MessageEvent<M const>& event,
                      bool nonconst_force_copy)
    {
      Event my_event(event, nonconst_force_copy || event.nonConstWillCopy());
      callback_(Adapter::getParameter(my_event));
    }

   private:
    Callback callback_;
  };

  template <class M>
  class Signal1
  {
    typedef std::shared_ptr<CallbackHelper1<M>> CallbackHelper1Ptr;
    typedef std::vector<CallbackHelper1Ptr> V_CallbackHelper1;

   public:
    template <typename P>
    CallbackHelper1Ptr addCallback(const std::function<void(P)>& callback)
    {
      CallbackHelper1T<P, M>* helper = new CallbackHelper1T<P, M>(callback);

      std::lock_guard<std::mutex> lock(mutex_);
      callbacks_.push_back(CallbackHelper1Ptr(helper));
      return callbacks_.back();
    }

    void removeCallback(const CallbackHelper1Ptr& helper)
    {
      std::lock_guard<std::mutex> lock(mutex_);
      typename V_CallbackHelper1::iterator it =
          std::find(callbacks_.begin(), callbacks_.end(), helper);
      if (it != callbacks_.end())
      {
        callbacks_.erase(it);
      }
    }

    void call(const MessageEvent<M const>& event)
    {
      std::lock_guard<std::mutex> lock(mutex_);
      bool nonconst_force_copy = callbacks_.size() > 1;
      typename V_CallbackHelper1::iterator it = callbacks_.begin();
      typename V_CallbackHelper1::iterator end = callbacks_.end();
      for (; it != end; ++it)
      {
        const CallbackHelper1Ptr& helper = *it;
        helper->call(event, nonconst_force_copy);
      }
    }

   private:
    std::mutex mutex_;
    V_CallbackHelper1 callbacks_;
  };
}  // namespace message_filters
