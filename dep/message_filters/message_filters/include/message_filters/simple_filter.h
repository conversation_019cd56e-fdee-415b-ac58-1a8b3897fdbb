// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :simple_filter.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/22
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once
#include <functional>
#include <memory>
#include <string>

#include "message_filters/connection.h"
#include "message_filters/message_event.h"
#include "message_filters/signal1.h"

namespace message_filters
{
  template <class M>
  class SimpleFilter : public noncopyable
  {
   public:
    typedef std::shared_ptr<M const> MConstPtr;
    typedef std::function<void(const MConstPtr &)> Callback;
    typedef MessageEvent<M const> EventType;
    typedef std::function<void(const EventType &)> EventCallback;

    template <typename C>
    Connection registerCallback(const C &callback)
    {
      typename CallbackHelper1<M>::Ptr helper =
          signal_.addCallback(Callback(callback));
      return Connection(std::bind(&Signal::removeCallback, &signal_, helper));
    }

    template <typename P>
    Connection registerCallback(const std::function<void(P)> &callback)
    {
      return Connection(std::bind(&Signal::removeCallback, &signal_,
                                  signal_.addCallback(callback)));
    }

    template <typename P>
    Connection registerCallback(void (*callback)(P))
    {
      typename CallbackHelper1<M>::Ptr helper = signal_.template addCallback<P>(
          std::bind(callback, std::placeholders::_1));
      return Connection(std::bind(&Signal::removeCallback, &signal_, helper));
    }

    template <typename T, typename P>
    Connection registerCallback(void (T::*callback)(P), T *t)
    {
      typename CallbackHelper1<M>::Ptr helper = signal_.template addCallback<P>(
          std::bind(callback, t, std::placeholders::_1));
      return Connection(std::bind(&Signal::removeCallback, &signal_, helper));
    }

    void setName(const std::string &name) { name_ = name; }

    const std::string &getName() { return name_; }

   protected:
    void signalMessage(const MConstPtr &msg)
    {
      MessageEvent<M const> event(msg);

      signal_.call(event);
    }
    void signalMessage(const MessageEvent<M const> &event)
    {
      signal_.call(event);
    }

   private:
    typedef Signal1<M> Signal;
    Signal signal_;
    std::string name_;
  };

}  // namespace message_filters
