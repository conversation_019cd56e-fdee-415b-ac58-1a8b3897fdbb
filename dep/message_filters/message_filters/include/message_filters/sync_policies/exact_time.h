// IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
// File Name         :exact_time.h
// Author            :zhijie.li (<EMAIL>)
// Version           :2.0.0
// Date              :2024/05/24
// Description       :
// This software is provided by the copyright holders and contributors "as is"
// and any express or implied warranties, including, but not limited to, the
// implied warranties of merchantability and fitness for a particular purpose
// are disclaimed. In no event shall the UBTECH Corporation or contributors be
// liable for any direct, indirect, incidental, special, exemplary, or
// consequential damages (including, but not limited to, procurement of
// substitute goods or services; loss of use, data, or profits; or business
// interruption) however caused and on any theory of liability, whether in
// contract, strict liability, or tort (including negligence or otherwise)
// arising in any way out of the use of this software, even if advised of the
// possibility of such damage.

#pragma once

#include <cstdint>
#include <deque>
#include <map>
#include <string>
#include <tuple>

#include "message_filters/connection.h"
#include "message_filters/message_traits.h"
#include "message_filters/null_types.h"
#include "message_filters/signal9.h"
#include "message_filters/synchronizer.h"

namespace message_filters
{
  namespace sync_policies
  {
    template <typename M0,
              typename M1,
              typename M2 = NullType,
              typename M3 = NullType,
              typename M4 = NullType,
              typename M5 = NullType,
              typename M6 = NullType,
              typename M7 = NullType,
              typename M8 = NullType>
    struct ExactTime : public PolicyBase<M0, M1, M2, M3, M4, M5, M6, M7, M8>
    {
      typedef Synchronizer<ExactTime> Sync;
      typedef PolicyBase<M0, M1, M2, M3, M4, M5, M6, M7, M8> Super;
      typedef typename Super::Messages Messages;
      typedef typename Super::Signal Signal;
      typedef typename Super::Events Events;
      typedef typename Super::RealTypeCount RealTypeCount;
      typedef typename Super::M0Event M0Event;
      typedef typename Super::M1Event M1Event;
      typedef typename Super::M2Event M2Event;
      typedef typename Super::M3Event M3Event;
      typedef typename Super::M4Event M4Event;
      typedef typename Super::M5Event M5Event;
      typedef typename Super::M6Event M6Event;
      typedef typename Super::M7Event M7Event;
      typedef typename Super::M8Event M8Event;
      typedef Events Tuple;

      ExactTime(uint32_t queue_size) : parent_(0), queue_size_(queue_size) {}

      ExactTime(const ExactTime& e) { *this = e; }

      ExactTime& operator=(const ExactTime& rhs)
      {
        parent_ = rhs.parent_;
        queue_size_ = rhs.queue_size_;
        last_signal_time_ = rhs.last_signal_time_;
        tuples_ = rhs.tuples_;

        return *this;
      }

      void initParent(Sync* parent) { parent_ = parent; }

      template <int i>
      void add(const typename std::tuple_element<i, Events>::type& evt)
      {
        assert(parent_);

        namespace mt = message_filters::message_traits;

        std::lock_guard<std::mutex> lock(mutex_);

        Tuple& t = tuples_[mt::TimeStamp<typename std::tuple_element<
            i, Messages>::type>::value(*evt.getMessage())];
        std::get<i>(t) = evt;

        checkTuple(t);
      }

      template <class C>
      Connection registerDropCallback(const C& callback)
      {
        return drop_signal_.addCallback(callback);
      }

      template <class C>
      Connection registerDropCallback(C& callback)
      {
        return drop_signal_.addCallback(callback);
      }

      template <class C, typename T>
      Connection registerDropCallback(const C& callback, T* t)
      {
        return drop_signal_.addCallback(callback, t);
      }

      template <class C, typename T>
      Connection registerDropCallback(C& callback, T* t)
      {
        return drop_signal_.addCallback(callback, t);
      }

      rosa::Time getLastSignalTime() const { return last_signal_time_; }

     private:
      // assumes mutex_ is already locked
      void checkTuple(Tuple& t)
      {
        namespace mt = message_filters::message_traits;

        bool full = true;
        full = full && (bool)std::get<0>(t).getMessage();
        full = full && (bool)std::get<1>(t).getMessage();
        full = full &&
               (RealTypeCount::value > 2 ? (bool)std::get<2>(t).getMessage()
                                         : true);
        full = full &&
               (RealTypeCount::value > 3 ? (bool)std::get<3>(t).getMessage()
                                         : true);
        full = full &&
               (RealTypeCount::value > 4 ? (bool)std::get<4>(t).getMessage()
                                         : true);
        full = full &&
               (RealTypeCount::value > 5 ? (bool)std::get<5>(t).getMessage()
                                         : true);
        full = full &&
               (RealTypeCount::value > 6 ? (bool)std::get<6>(t).getMessage()
                                         : true);
        full = full &&
               (RealTypeCount::value > 7 ? (bool)std::get<7>(t).getMessage()
                                         : true);
        full = full &&
               (RealTypeCount::value > 8 ? (bool)std::get<8>(t).getMessage()
                                         : true);

        if (full)
        {
          parent_->signal(std::get<0>(t), std::get<1>(t), std::get<2>(t),
                          std::get<3>(t), std::get<4>(t), std::get<5>(t),
                          std::get<6>(t), std::get<7>(t), std::get<8>(t));

          last_signal_time_ =
              mt::TimeStamp<M0>::value(*std::get<0>(t).getMessage());

          tuples_.erase(last_signal_time_);

          clearOldTuples();
        }

        if (queue_size_ > 0)
        {
          while (tuples_.size() > queue_size_)
          {
            Tuple& t2 = tuples_.begin()->second;
            drop_signal_.call(std::get<0>(t2), std::get<1>(t2), std::get<2>(t2),
                              std::get<3>(t2), std::get<4>(t2), std::get<5>(t2),
                              std::get<6>(t2), std::get<7>(t2),
                              std::get<8>(t2));
            tuples_.erase(tuples_.begin());
          }
        }
      }

      // assumes mutex_ is already locked
      void clearOldTuples()
      {
        typename M_TimeToTuple::iterator it = tuples_.begin();
        typename M_TimeToTuple::iterator end = tuples_.end();
        for (; it != end;)
        {
          if (it->first <= last_signal_time_)
          {
            typename M_TimeToTuple::iterator old = it;
            ++it;

            Tuple& t = old->second;
            drop_signal_.call(std::get<0>(t), std::get<1>(t), std::get<2>(t),
                              std::get<3>(t), std::get<4>(t), std::get<5>(t),
                              std::get<6>(t), std::get<7>(t), std::get<8>(t));
            tuples_.erase(old);
          }
          else
          {
            // the map is sorted by time, so we can ignore anything after this
            // if this one's time is ok
            break;
          }
        }
      }

     private:
      Sync* parent_;

      uint32_t queue_size_;
      typedef std::map<rosa::Time, Tuple> M_TimeToTuple;
      M_TimeToTuple tuples_;
      rosa::Time last_signal_time_;

      Signal drop_signal_;

      std::mutex mutex_;
    };

  }  // namespace sync_policies
}  // namespace message_filters
