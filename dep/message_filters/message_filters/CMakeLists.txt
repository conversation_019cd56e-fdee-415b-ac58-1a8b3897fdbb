cmake_minimum_required(VERSION 3.5)

project(message_filters)

find_package(rosa_cmake REQUIRED)

set(DEPS
    rosa
    rosa_utils
    rosa_msgs
    shm_msgs
    std_msgs
)
rosa_find_package(${DEPS})

add_library(${PROJECT_NAME} SHARED src/connection.cpp)
target_include_directories(
  ${PROJECT_NAME} PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
                         $<INSTALL_INTERFACE:include>
)

foreach(dep ${DEPS})
  target_link_libraries(${PROJECT_NAME} PUBLIC ${dep}::${dep})
endforeach()

if(BUILD_TESTING)
  find_package(ament_cmake_gtest REQUIRED)
  find_package(sensor_msgs REQUIRED)
  find_package(rosgraph_msgs REQUIRED)
  ament_add_gtest(
    ${PROJECT_NAME}_test
    tests/test_main.cpp
    tests/test_cache.cpp
    tests/test_simple.cpp
    tests/test_message_traits.cpp
    tests/test_chain.cpp
    tests/test_synchronizer.cpp
    tests/test_approximate_epsilon_time_policy.cpp
    tests/test_approximate_time_policy.cpp
    tests/test_exact_time_policy.cpp
    tests/test_fuzz.cpp
    tests/test_latest_time_policy.cpp
  )

  target_link_libraries(
    ${PROJECT_NAME}_test
    ${PROJECT_NAME}
    sensor_msgs::sensor_msgs
    rosgraph_msgs::rosgraph_msgs
  )
endif()

install(DIRECTORY include/ DESTINATION include)

rosa_install_library(${PROJECT_NAME})
rosa_package()
