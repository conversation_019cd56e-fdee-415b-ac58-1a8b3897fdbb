cmake_minimum_required(VERSION 3.5)
project(pcl_conversions)

find_package(rosa_cmake REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(
  PCL
  REQUIRED
  QUIET
  COMPONENTS common io
)
# find_package(pcl_msgs REQUIRED)

set(DEPS
    rosa
    message_filters
    sensor_msgs
    std_msgs
)
rosa_find_package(${DEPS})

add_library(${PROJECT_NAME} INTERFACE)

target_include_directories(
  ${PROJECT_NAME}
  INTERFACE $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
            $<INSTALL_INTERFACE:include>
)

target_link_libraries(${PROJECT_NAME} INTERFACE Eigen3::Eigen ${PCL_LIBRARIES})

foreach(dep ${DEPS})
  target_link_libraries(${PROJECT_NAME} INTERFACE ${dep}::${dep})
endforeach()

rosa_install_library(${PROJECT_NAME})

install(DIRECTORY include/ DESTINATION include)

# Add gtest based cpp test target
if(BUILD_TESTING)
  find_package(ament_cmake_gtest REQUIRED)

  ament_add_gtest(${PROJECT_NAME}_test test/test_pcl_conversions.cpp)
  target_link_libraries(${PROJECT_NAME}_test ${PROJECT_NAME})
endif()

rosa_package()
