ARG BASE_IMAGE=glcr.rd.ubtrobot.com/pub/docker/webots:R2025a-ubuntu22.04
FROM glcr.rd.ubtrobot.com/rosa/rosa:ubuntu22.04-v2.1.6 as rosa
FROM glcr.rd.ubtrobot.com/t800/common/image/3rdparty:ubuntu22.04 as ubt_3rd

FROM $BASE_IMAGE as core

ARG TARGETPLATFORM

USER root

COPY --chown=1000 . /opt/src/
WORKDIR /opt/src

# RUN ./build.sh
RUN --mount=type=bind,from=ubt_3rd,source=/opt/ubt_3rdparty,target=/opt/ubt_3rdparty \
    --mount=type=bind,from=rosa,source=/opt/rosa,target=/opt/rosa \
    --mount=type=ssh,uid=1000 \
    ./build.sh

FROM glcr.rd.ubtrobot.com/pub/hub/busybox:musl
COPY --from=core /opt/src/install /opt/nav_webots_rosa
