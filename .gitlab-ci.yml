---
include:
  - project: "pub/ci_templates"
    file: "/cpp.lint.gitlab-ci.yml"
  - project: "pub/ci_templates"
    file: "/docker.build/define.gitlab-ci.yml"


stages:
  - lint
  - build

build:ubuntu22_merge_requests:
  variables:
    PLATFORM: linux/amd64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/webots:R2025a-ubuntu22.04
    TAG: ubuntu22.04
  extends:
    - .merge_requests_job

build:ubuntu22_main:
  variables:
    PLATFORM: linux/amd64
    BASE_IMAGE_NAME: ${UA_REGISTRY}/pub/docker/webots:R2025a-ubuntu22.04
    TAG: ubuntu22.04
  extends:
    - .main_job

